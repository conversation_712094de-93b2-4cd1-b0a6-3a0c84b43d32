package event_repository

import (
	"context"
	"jxt-evidence-system/evidence-management/shared/domain/event"
)

// DomainEventRepository 定义领域事件仓储接口
type DomainEventRepository interface {
	// Save 保存领域事件
	Save(ctx context.Context, event *event.DomainEvent) error

	// FindByAggregateID 根据聚合根ID查找领域事件
	FindByAggregateID(ctx context.Context, aggregateID string) ([]event.DomainEvent, error)

	// FindByEventType 根据事件类型查找领域事件
	FindByEventType(ctx context.Context, eventType string) ([]event.DomainEvent, error)

	// FindByTimeRange 根据时间范围查找领域事件
	FindByTimeRange(ctx context.Context, startTime, endTime int64) ([]event.DomainEvent, error)

	// Delete 删除领域事件
	Delete(ctx context.Context, eventID string) error

	// DeleteByAggregateID 根据聚合根ID删除领域事件
	DeleteByAggregateID(ctx context.Context, aggregateID string) error

	// FindEarliestEvents 查询OccurredAt时间最早的domainEvent，最多返回50个
	FindEarliestEvents(ctx context.Context) ([]event.DomainEvent, error)

	// FindEarliestEventsByAggregateType 查询某个聚合类型OccurredAt时间最早的domainEvent，最多返回50个
	FindEarliestEventsByAggregateType(ctx context.Context, aggregateType string) ([]event.DomainEvent, error)
}
