package domain_service

import (
	"context"
	"errors"
	"fmt"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archive/repository"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archivemediarelation"
	archiveMediaRelationRepo "jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archivemediarelation/repository"
	mediaRepo "jxt-evidence-system/evidence-management/command/internal/domain/aggregate/media/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"
	domain_event "jxt-evidence-system/evidence-management/shared/domain/event"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

func init() {
	registrations = append(registrations, registerArchiveMediaRelationDomainServiceDependencies)
}

func registerArchiveMediaRelationDomainServiceDependencies() {
	err := di.Provide(func(
		archiveMediaRelationRepo archiveMediaRelationRepo.ArchiveMediaRelationRepository,
		archiveRepo repository.ArchiveRepository,
		mediaRepository mediaRepo.MediaRepository) ArchiveMediaRelationDomainService {
		return &archiveMediaRelationDomainService{
			archiveMediaRelationRepo: archiveMediaRelationRepo,
			archiveRepo:              archiveRepo,
			mediaRepo:                mediaRepository,
			events:                   make([]domain_event.Event, 0),
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide ArchiveMediaRelationDomainService: %v", err)
	}
}

// ArchiveMediaRelationDomainService 档案媒体关联领域服务接口
type ArchiveMediaRelationDomainService interface {
	// BatchCreateArchiveMediaRelation 批量创建档案媒体关联
	BatchCreateArchiveMediaRelation(ctx context.Context, archiveId int64, documentIds []int64, createBy int) (context.Context, []int64, []*archivemediarelation.ArchiveMediaRelation, error)

	// BatchDeleteArchiveMediaRelation 批量删除档案媒体关联
	BatchDeleteArchiveMediaRelation(ctx context.Context, ids []int64, updateBy int) (context.Context, []int64, error)
}

// archiveMediaRelationDomainService 档案媒体关联领域服务实现，不负责持久化操作，领域服务必须无状态
type archiveMediaRelationDomainService struct {
	archiveMediaRelationRepo archiveMediaRelationRepo.ArchiveMediaRelationRepository
	archiveRepo              repository.ArchiveRepository
	mediaRepo                mediaRepo.MediaRepository
	events                   []domain_event.Event
}

// BatchCreateArchiveMediaRelation 批量创建档案媒体关联
func (s *archiveMediaRelationDomainService) BatchCreateArchiveMediaRelation(ctx context.Context, archiveId int64, documentIds []int64, createBy int) (context.Context, []int64, []*archivemediarelation.ArchiveMediaRelation, error) {
	if archiveId <= 0 {
		return ctx, nil, nil, errors.New("档案ID必须大于0")
	}
	if len(documentIds) == 0 {
		return ctx, nil, nil, errors.New("文档ID列表不能为空")
	}

	// 验证档案是否存在
	archive, err := s.archiveRepo.FindByID(ctx, archiveId)
	if err != nil {
		logger.Error("查询档案失败", zap.Error(err), zap.Int64("archiveId", archiveId))
		return ctx, nil, nil, fmt.Errorf("查询档案失败: %w", err)
	}
	if archive == nil {
		return ctx, nil, nil, fmt.Errorf("档案[%d]不存在", archiveId)
	}

	var validDocumentIds []int64
	var relations []*archivemediarelation.ArchiveMediaRelation

	// 批量创建的领域逻辑：验证每个媒体并过滤已存在的关联
	for _, documentId := range documentIds {
		// 验证媒体是否存在
		media, err := s.mediaRepo.FindByID(ctx, documentId)
		if err != nil {
			logger.Warn("查询媒体失败，跳过", zap.Error(err), zap.Int64("mediaId", documentId))
			continue
		}
		if media == nil {
			logger.Warn("媒体不存在，跳过", zap.Int64("mediaId", documentId))
			continue
		}

		// 检查关联关系是否已存在
		existingRelation, err := s.archiveMediaRelationRepo.FindByArchiveAndMedia(ctx, archiveId, documentId)
		if err != nil {
			logger.Warn("查询关联关系失败，跳过", zap.Error(err))
			continue
		}
		if existingRelation != nil {
			logger.Warn("关联关系已存在，跳过",
				zap.Int64("archiveId", archiveId),
				zap.Int64("mediaId", documentId))
			continue
		}

		// 检查媒体是否已被锁定
		if media.IsLocked == 1 {
			logger.Warn("媒体已被锁定，跳过关联创建",
				zap.Int64("archiveId", archiveId),
				zap.Int64("mediaId", documentId))
			continue
		}

		// 创建关联对象
		relation := &archivemediarelation.ArchiveMediaRelation{
			ArchiveId: archiveId,
			MediaId:   documentId,
		}
		relation.CreateBy = createBy
		relation.UpdateBy = createBy

		// 使用聚合根方法验证业务规则
		err = relation.CreateArchiveMediaRelationAndSave()
		if err != nil {
			logger.Warn("创建关联业务规则验证失败，跳过", zap.Error(err))
			continue
		}

		validDocumentIds = append(validDocumentIds, documentId)
		relations = append(relations, relation)
	}

	if len(validDocumentIds) == 0 {
		return ctx, nil, nil, errors.New("没有有效的关联关系可以创建")
	}

	// 创建批量创建事件
	batchCreatedEvent := s.createArchiveMediaRelationBatchCreatedEvent(archiveId, validDocumentIds, createBy)
	if batchCreatedEvent == nil {
		return ctx, nil, nil, errors.New("创建批量创建事件失败")
	}

	// 将事件添加到上下文中
	newCtx := domain_event.AddEventToContext(ctx, batchCreatedEvent)

	return newCtx, validDocumentIds, relations, nil
}

// BatchDeleteArchiveMediaRelation 批量删除档案媒体关联
func (s *archiveMediaRelationDomainService) BatchDeleteArchiveMediaRelation(ctx context.Context, ids []int64, updateBy int) (context.Context, []int64, error) {
	if len(ids) == 0 {
		return ctx, nil, errors.New("关联ID列表不能为空")
	}

	var validIds []int64

	// 批量删除的领域逻辑：验证每个关联是否可以删除
	for _, id := range ids {
		// 查询关联是否存在
		relation, err := s.archiveMediaRelationRepo.FindByID(ctx, id)
		if err != nil {
			logger.Warn("查询关联失败，跳过", zap.Error(err), zap.Int64("relationId", id))
			continue
		}
		if relation == nil {
			logger.Warn("关联不存在，跳过", zap.Int64("relationId", id))
			continue
		}

		// 业务规则验证：可以在这里添加删除前的业务规则检查
		// 例如：检查关联的档案或媒体状态、权限等

		// 使用聚合根方法验证删除规则
		err = relation.DeleteArchiveMediaRelation()
		if err != nil {
			logger.Warn("删除关联业务规则验证失败，跳过", zap.Error(err))
			continue
		}

		validIds = append(validIds, id)
	}

	if len(validIds) == 0 {
		return ctx, nil, errors.New("没有有效的关联可以删除")
	}

	// 创建批量删除事件
	batchDeletedEvent := s.createArchiveMediaRelationBatchDeletedEvent(validIds, updateBy)
	if batchDeletedEvent == nil {
		return ctx, nil, errors.New("创建批量删除事件失败")
	}

	// 将事件添加到上下文中
	newCtx := domain_event.AddEventToContext(ctx, batchDeletedEvent)

	return newCtx, validIds, nil
}

// createArchiveMediaRelationBatchCreatedEvent 创建批量创建事件
func (s *archiveMediaRelationDomainService) createArchiveMediaRelationBatchCreatedEvent(archiveId int64, documentIds []int64, createBy int) domain_event.Event {
	if len(documentIds) == 0 {
		logger.Error("文档ID列表为空，无法创建批量创建事件")
		return nil
	}

	// 构建关联关系列表用于事件载荷
	var relations []domain_event.ArchiveMediaRelationCreatedPayload
	for _, documentId := range documentIds {
		relations = append(relations, domain_event.ArchiveMediaRelationCreatedPayload{
			ArchiveId: archiveId,
			MediaId:   documentId,
			CreateBy:  createBy,
			UpdateBy:  createBy,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		})
	}

	payload := domain_event.ArchiveMediaRelationBatchCreatedPayload{
		Relations: relations,
		CreateBy:  createBy,
		CreatedAt: time.Now(),
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 ArchiveMediaRelationBatchCreatedPayload 转换为 JSON", zap.Error(err))
		return nil
	}

	// 使用档案ID作为聚合根ID
	return domain_event.NewDomainEvent(domain_event.EventTypeArchiveMediaRelationBatchCreated, archiveId, "ArchiveMediaRelation", payloadJSON)
}

// createArchiveMediaRelationBatchDeletedEvent 创建批量删除事件
func (s *archiveMediaRelationDomainService) createArchiveMediaRelationBatchDeletedEvent(ids []int64, updateBy int) domain_event.Event {
	if len(ids) == 0 {
		logger.Error("关联ID列表为空，无法创建批量删除事件")
		return nil
	}

	payload := domain_event.ArchiveMediaRelationBatchDeletedPayload{
		IDs:       ids,
		UpdateBy:  updateBy,
		DeletedAt: time.Now(),
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 ArchiveMediaRelationBatchDeletedPayload 转换为 JSON", zap.Error(err))
		return nil
	}

	// 使用第一个ID作为聚合根ID
	aggregateID := ids[0]
	return domain_event.NewDomainEvent(domain_event.EventTypeArchiveMediaRelationBatchDeleted, aggregateID, "ArchiveMediaRelation", payloadJSON)
}
