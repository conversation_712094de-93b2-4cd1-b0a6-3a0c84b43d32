// Package eventbus 提供了Kafka消息总线相关功能
// NoBacklogDetector 用于检测Kafka消费者组是否存在消息积压
// 检测原理:
// 1. 获取消费者组在各分区的当前消费位移
// 2. 获取各分区的最新消息位移
// 3. 计算位移差(lag)判断是否积压:
//   - 如果lag超过阈值,认为存在积压
//   - 如果lag不为0但小于阈值,检查最后消费消息的时间戳
//   - 如果时间戳超过阈值,也认为存在积压
package eventbus

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/Shopify/sarama"
)

type NoBacklogDetector struct {
	admin            sarama.ClusterAdmin
	client           sarama.Client
	group            string
	maxLagThreshold  int64
	maxTimeThreshold time.Duration
	consumers        sync.Map
	// 新增字段用于记录检测结果
	lastCheckTime   time.Time
	lastCheckResult bool
	checkMutex      sync.RWMutex
}

// NewNoBacklogDetector 创建一个新的 NoBacklogDetector 实例
func NewNoBacklogDetector(brokers []string, group string, maxLagThreshold int64, maxTimeThreshold time.Duration) (*NoBacklogDetector, error) {
	config := sarama.NewConfig()
	config.Version = sarama.V2_0_0_0
	// 设置必要的配置
	config.Consumer.Return.Errors = true
	config.Consumer.Offsets.Initial = sarama.OffsetOldest

	admin, err := sarama.NewClusterAdmin(brokers, config)
	if err != nil {
		return nil, fmt.Errorf("error creating cluster admin: %w", err)
	}

	client, err := sarama.NewClient(brokers, config)
	if err != nil {
		admin.Close()
		return nil, fmt.Errorf("error creating client: %w", err)
	}

	return &NoBacklogDetector{
		admin:            admin,
		client:           client,
		group:            group,
		maxLagThreshold:  maxLagThreshold,
		maxTimeThreshold: maxTimeThreshold,
		consumers:        sync.Map{},
		lastCheckTime:    time.Now(),
	}, nil
}

// IsNoBacklog 检查是否存在积压
func (nbd *NoBacklogDetector) IsNoBacklog(ctx context.Context) (bool, error) {
	// 获取消费者组信息
	groups, err := nbd.admin.ListConsumerGroups()
	if err != nil {
		return false, fmt.Errorf("error listing consumer groups: %w", err)
	}

	// 检查消费者组是否存在
	if _, exists := groups[nbd.group]; !exists {
		return false, fmt.Errorf("consumer group %s does not exist", nbd.group)
	}

	// 获取消费者组详情
	offsetFetchResponse, err := nbd.admin.ListConsumerGroupOffsets(nbd.group, nil)
	if err != nil {
		return false, fmt.Errorf("error fetching consumer group offsets: %w", err)
	}

	var wg sync.WaitGroup
	results := make(chan bool, len(offsetFetchResponse.Blocks))
	errors := make(chan error, len(offsetFetchResponse.Blocks))

	for topic, partitions := range offsetFetchResponse.Blocks {
		wg.Add(1)
		go func(topic string, partitions map[int32]*sarama.OffsetFetchResponseBlock) {
			defer wg.Done()
			hasBacklog, err := nbd.checkTopicBacklog(ctx, topic, partitions)
			if err != nil {
				errors <- fmt.Errorf("error checking topic %s: %w", topic, err)
				return
			}
			results <- hasBacklog
		}(topic, partitions)
	}

	// 等待所有检查完成
	go func() {
		wg.Wait()
		close(results)
		close(errors)
	}()

	// 处理错误
	for err := range errors {
		if err != nil {
			return false, err
		}
	}

	// 检查结果
	for hasBacklog := range results {
		if hasBacklog {
			nbd.updateCheckResult(false)
			return false, nil
		}
	}

	nbd.updateCheckResult(true)
	return true, nil
}

// checkTopicBacklog 检查单个主题的积压情况
func (nbd *NoBacklogDetector) checkTopicBacklog(ctx context.Context, topic string, partitions map[int32]*sarama.OffsetFetchResponseBlock) (bool, error) {
	consumer, err := nbd.getOrCreateConsumer(topic)
	if err != nil {
		return false, err
	}

	for partition, block := range partitions {
		if block.Err != sarama.ErrNoError {
			return false, fmt.Errorf("error in offset fetch response for partition %d: %v", partition, block.Err)
		}

		// 获取最新偏移量
		latestOffset, err := nbd.client.GetOffset(topic, partition, sarama.OffsetNewest)
		if err != nil {
			return false, fmt.Errorf("error getting latest offset for partition %d: %w", partition, err)
		}

		// 计算lag
		lag := latestOffset - block.Offset
		if lag > nbd.maxLagThreshold {
			return true, nil
		}

		// 检查最后消费消息的时间戳
		if lag > 0 {
			lastMsg, err := nbd.getLastConsumedMessage(ctx, consumer, topic, partition, block.Offset-1)
			if err == nil && time.Since(lastMsg.Timestamp) > nbd.maxTimeThreshold {
				return true, nil
			}
		}
	}

	return false, nil
}

func (nbd *NoBacklogDetector) getOrCreateConsumer(topic string) (sarama.Consumer, error) {
	if consumer, ok := nbd.consumers.Load(topic); ok {
		return consumer.(sarama.Consumer), nil
	}

	consumer, err := sarama.NewConsumerFromClient(nbd.client)
	if err != nil {
		return nil, fmt.Errorf("error creating consumer: %w", err)
	}

	actual, loaded := nbd.consumers.LoadOrStore(topic, consumer)
	if loaded {
		consumer.Close()
		return actual.(sarama.Consumer), nil
	}

	return consumer, nil
}

func (nbd *NoBacklogDetector) getLastConsumedMessage(ctx context.Context, consumer sarama.Consumer, topic string, partition int32, offset int64) (*sarama.ConsumerMessage, error) {
	partitionConsumer, err := consumer.ConsumePartition(topic, partition, offset)
	if err != nil {
		return nil, fmt.Errorf("error creating partition consumer: %w", err)
	}
	defer partitionConsumer.Close()

	select {
	case msg := <-partitionConsumer.Messages():
		return msg, nil
	case err := <-partitionConsumer.Errors():
		return nil, fmt.Errorf("error consuming message: %w", err)
	case <-ctx.Done():
		return nil, ctx.Err()
	case <-time.After(5 * time.Second):
		return nil, fmt.Errorf("timeout waiting for message")
	}
}

// 更新检测结果
func (nbd *NoBacklogDetector) updateCheckResult(result bool) {
	nbd.checkMutex.Lock()
	defer nbd.checkMutex.Unlock()
	nbd.lastCheckResult = result
	nbd.lastCheckTime = time.Now()
}

// GetLastCheckResult 获取最近一次检测结果
func (nbd *NoBacklogDetector) GetLastCheckResult() (bool, time.Time) {
	nbd.checkMutex.RLock()
	defer nbd.checkMutex.RUnlock()
	return nbd.lastCheckResult, nbd.lastCheckTime
}

func (nbd *NoBacklogDetector) Close() error {
	var errs []error

	// 关闭所有消费者
	nbd.consumers.Range(func(_, value interface{}) bool {
		if err := value.(sarama.Consumer).Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close consumer: %w", err))
		}
		return true
	})

	// 关闭admin客户端
	if err := nbd.admin.Close(); err != nil {
		errs = append(errs, fmt.Errorf("failed to close admin: %w", err))
	}

	// 关闭client客户端
	if err := nbd.client.Close(); err != nil {
		errs = append(errs, fmt.Errorf("failed to close client: %w", err))
	}

	if len(errs) > 0 {
		return fmt.Errorf("multiple errors occurred during close: %v", errs)
	}

	return nil
}
