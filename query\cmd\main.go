package main

import (
	"errors"
	"fmt"
	"jxt-evidence-system/evidence-management/shared/common/global"
	"os"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg"

	"github.com/spf13/cobra"

	"jxt-evidence-system/evidence-management/query/cmd/api"
	"jxt-evidence-system/evidence-management/query/cmd/config"
	"jxt-evidence-system/evidence-management/query/cmd/migrate"
	"jxt-evidence-system/evidence-management/query/cmd/version"
)

var rootCmd = &cobra.Command{
	Use:          "jxt-evidence-query",
	Short:        "jxt-evidence-query",
	SilenceUsage: true,
	Long:         `jxt-evidence-query`,
	Args: func(cmd *cobra.Command, args []string) error {
		if len(args) < 1 {
			tip()
			return errors.New(pkg.Red("requires at least one arg"))
		}
		return nil
	},
	PersistentPreRunE: func(*cobra.Command, []string) error { return nil },
	Run: func(cmd *cobra.Command, args []string) {
		tip()
	},
}

func tip() {
	usageStr := `欢迎使用 ` + pkg.Green(`go-admin `+global.Version) + ` 可以使用 ` + pkg.Red(`-h`) + ` 查看命令`
	usageStr1 := `也可以参考 https://doc.go-admin.dev/guide/ksks 的相关内容`
	fmt.Printf("%s\n", usageStr)
	fmt.Printf("%s\n", usageStr1)
}

func init() {
	rootCmd.AddCommand(api.StartCmd)
	rootCmd.AddCommand(migrate.StartCmd)
	rootCmd.AddCommand(version.StartCmd)
	rootCmd.AddCommand(config.StartCmd)
}

// Execute : apply commands
func main() {
	if err := rootCmd.Execute(); err != nil {
		os.Exit(-1)
	}
}
