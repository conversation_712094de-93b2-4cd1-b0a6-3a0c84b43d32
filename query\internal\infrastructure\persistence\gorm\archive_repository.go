package persistence

import (
	"context"
	"errors"
	query "jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/models"
	"jxt-evidence-system/evidence-management/query/internal/models/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"
	cQuery "jxt-evidence-system/evidence-management/shared/common/query"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"gorm.io/gorm"
)

func init() {
	registrations = append(registrations, registerArchiveReadModelRepoDependencies)
}

func registerArchiveReadModelRepoDependencies() {
	if err := di.Provide(func() repository.ArchiveReadModelRepository {
		return &gormArchiveReadModelRepository{}
	}); err != nil {
		logger.Fatalf("failed to provide GormArchiveReadModelRepository: %v", err)
	}
}

type gormArchiveReadModelRepository struct {
	GormRepository
}

// ArchiveQueryScope 档案查询作用域，处理特殊查询条件
func ArchiveQueryScope(r *query.ArchivePagedQuery) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		// 构建查询条件
		conditions := make([]func(*gorm.DB) *gorm.DB, 0)

		// 添加档案编码查询条件
		if r.ArchiveCode != "" {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.archive_code LIKE ?", "%"+r.ArchiveCode+"%")
			})
		}

		// 添加档案标题查询条件
		if r.ArchiveTitle != "" {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.archive_title LIKE ?", "%"+r.ArchiveTitle+"%")
			})
		}

		// 添加档案类型查询条件
		if r.ArchiveType > 0 {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.archive_type = ?", r.ArchiveType)
			})
		}

		// 添加组织ID查询条件
		if r.OrgID > 0 {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.org_id = ?", r.OrgID)
			})
		}

		// 添加组织编码查询条件
		if r.OrgCode != "" {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.org_code LIKE ?", "%"+r.OrgCode+"%")
			})
		}

		// 添加组织名称查询条件
		if r.OrgName != "" {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.org_name LIKE ?", "%"+r.OrgName+"%")
			})
		}

		// 添加状态查询条件
		if r.Status >= 0 {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.status = ?", r.Status)
			})
		}

		// 添加录入时间范围查询条件
		if !r.InputTimeStart.IsZero() {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.input_time >= ?", r.InputTimeStart)
			})
		}

		if !r.InputTimeEnd.IsZero() {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.input_time <= ?", r.InputTimeEnd)
			})
		}

		// 添加过期时间范围查询条件
		if !r.ExpirationStart.IsZero() {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.expiration_time >= ?", r.ExpirationStart)
			})
		}

		if !r.ExpirationEnd.IsZero() {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.expiration_time <= ?", r.ExpirationEnd)
			})
		}

		// 添加保存期限查询条件
		if r.StorageDuration > 0 {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.storage_duration = ?", r.StorageDuration)
			})
		}

		// 添加录入人员查询条件
		if r.InputUserName != "" {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_archives_read.input_user_name LIKE ?", "%"+r.InputUserName+"%")
			})
		}

		// 应用所有条件
		for _, condition := range conditions {
			db = condition(db)
		}

		return db
	}
}

func (repo *gormArchiveReadModelRepository) GetPage(ctx context.Context, r *query.ArchivePagedQuery) (*[]models.ArchiveReadModel, int64, error) {
	var data models.ArchiveReadModel
	var list []models.ArchiveReadModel
	var count int64

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return nil, 0, err
	}

	err = db.WithContext(ctx).Model(&data).
		Scopes(
			ArchiveQueryScope(r), // 使用档案查询作用域
			cQuery.Paginate(r.GetPageSize(), r.GetPageIndex()), // 分页
		).
		Find(&list).Limit(-1).Offset(-1).
		Count(&count).Error
	if err != nil {
		return nil, 0, err
	}
	return &list, count, nil
}

// FindByID 根据id查询档案读模型
func (repo *gormArchiveReadModelRepository) FindByID(ctx context.Context, id int64) (*models.ArchiveReadModel, error) {
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}

	var model models.ArchiveReadModel
	err = db.WithContext(ctx).Where("archive_id = ?", id).First(&model).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// FindByCode 根据编码查询档案读模型
func (repo *gormArchiveReadModelRepository) FindByCode(ctx context.Context, code string) (*models.ArchiveReadModel, error) {
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}

	var model models.ArchiveReadModel
	err = db.WithContext(ctx).Where("archive_code = ?", code).First(&model).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}

// Create 创建档案读模型
func (repo *gormArchiveReadModelRepository) Create(ctx context.Context, model *models.ArchiveReadModel) error {
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return err
	}
	result := db.WithContext(ctx).Save(model) // 因为model.id已经存在，所以使用Save
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// UpdateByID 更新档案读模型
func (repo *gormArchiveReadModelRepository) UpdateByID(ctx context.Context, id int64, updates map[string]interface{}) error {
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return err
	}
	result := db.WithContext(ctx).Model(&models.ArchiveReadModel{}).Where("archive_id = ?", id).Updates(updates)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// UpdateManyByIDs 批量更新档案读模型
func (repo *gormArchiveReadModelRepository) UpdateManyByIDs(ctx context.Context, ids []int64, updates map[string]interface{}) (rowsAffected int64, err error) {
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return 0, err
	}
	result := db.WithContext(ctx).Model(&models.ArchiveReadModel{}).Where("archive_id IN ?", ids).Updates(updates)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

// DeleteByID 删除档案读模型
func (repo *gormArchiveReadModelRepository) DeleteByID(ctx context.Context, id int64) error {
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return err
	}
	result := db.WithContext(ctx).Delete(&models.ArchiveReadModel{}, "archive_id = ?", id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// DeleteManyByIDs 批量删除档案读模型
func (repo *gormArchiveReadModelRepository) DeleteManyByIDs(ctx context.Context, ids []int64) (rowsAffected int64, err error) {
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return 0, err
	}
	result := db.WithContext(ctx).Delete(&models.ArchiveReadModel{}, "archive_id IN ?", ids)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}
