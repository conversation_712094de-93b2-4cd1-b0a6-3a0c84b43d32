package tests

import (
	"fmt"
	"math/rand"
	"net/http"
	"testing"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	commandTesthelpers "jxt-evidence-system/evidence-management/command/testhelpers"
	"jxt-evidence-system/evidence-management/tests/testhelpers"
)

func TestArchiveMediaRelationApi(t *testing.T) {
	RegisterFailHandler(Fail)
	//RunSpecs(t, "ArchiveMediaRelationApi Suite")
}

var _ = Describe("ArchiveMediaRelationApi", func() {
	var suite *testhelpers.TestSuite
	var archiveHelper *testhelpers.ArchiveTestHelper
	var mediaHelper *testhelpers.MediaTestHelper
	var relationHelper *testhelpers.ArchiveMediaRelationTestHelper

	BeforeEach(func() {
		suite = testhelpers.SetupSuite(baseURL)
		suite.SetToken(token)
		archiveHelper = testhelpers.NewArchiveTestHelper(dbCommand, dbQuery, suite)
		mediaHelper = testhelpers.NewMediaTestHelper(dbCommand, dbQuery, suite)
		relationHelper = testhelpers.NewArchiveMediaRelationTestHelper(dbCommand, dbQuery, suite)
	})

	AfterEach(func() {
		// 清理测试数据 - 按依赖关系顺序清理
		relationHelper.CleanupTestData()
		mediaHelper.CleanupTestData()
		archiveHelper.CleanupTestData()
	})

	Describe("CreateArchiveMediaRelation", func() {
		It("成功创建档案媒体关联", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 使用助手创建档案
			archiveTitle := fmt.Sprintf("关联测试档案_%06d", randNum)
			archiveID, err := archiveHelper.CreateTestArchive(archiveTitle)
			Expect(err).NotTo(HaveOccurred())

			// 使用助手创建媒体
			mediaName := fmt.Sprintf("关联测试媒体_%06d", randNum)
			mediaID, err := mediaHelper.CreateTestMedia(mediaName)
			Expect(err).NotTo(HaveOccurred())

			// 创建档案媒体关联
			createRelationCommand := commandTesthelpers.NewCreateArchiveMediaRelationCommandBuilder().
				WithArchiveId(archiveID).
				WithDocumentId(mediaID).
				WithRelationType("primary").
				Build()

			// 发送创建关联请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/archive-media-relations", createRelationCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证响应
			relationHelper.VerifyCreateRelationResponse(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			data := body["data"].(map[string]interface{})
			Expect(data["archiveId"]).To(Equal(float64(createRelationCommand.ArchiveId)))
			Expect(data["documentId"]).To(Equal(float64(createRelationCommand.DocumentId)))

			// 等待领域事件处理
			relationHelper.WaitForEventProcessing()

			// 验证数据库记录存在性
			relationHelper.VerifyRelationInCommandDB(archiveID, mediaID)
			relationHelper.VerifyRelationInQueryDB(archiveID, mediaID)

			fmt.Printf("✅ 成功创建档案媒体关联: ArchiveID=%d, MediaID=%d\n", archiveID, mediaID)
		})

		It("当尝试创建重复的档案媒体关联时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 使用助手创建档案和媒体
			archiveTitle := fmt.Sprintf("重复关联测试档案_%06d", randNum)
			archiveID, err := archiveHelper.CreateTestArchive(archiveTitle)
			Expect(err).NotTo(HaveOccurred())

			mediaName := fmt.Sprintf("重复关联测试媒体_%06d", randNum)
			mediaID, err := mediaHelper.CreateTestMedia(mediaName)
			Expect(err).NotTo(HaveOccurred())

			// 首先创建一个关联
			relationID, err := relationHelper.CreateTestRelation(archiveID, mediaID)
			Expect(err).NotTo(HaveOccurred())
			Expect(relationID).To(BeNumerically(">", 0))

			// 尝试创建相同的关联
			createRelationCommand := commandTesthelpers.NewCreateArchiveMediaRelationCommandBuilder().
				WithArchiveId(archiveID).
				WithDocumentId(mediaID).
				WithRelationType("primary").
				Build()

			// 发送创建关联请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/archive-media-relations", createRelationCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证错误响应
			relationHelper.VerifyErrorResponse(resp, "关联关系已存在")
		})

		It("当档案ID不存在时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 使用助手创建媒体
			mediaName := fmt.Sprintf("不存在档案关联测试媒体_%06d", randNum)
			mediaID, err := mediaHelper.CreateTestMedia(mediaName)
			Expect(err).NotTo(HaveOccurred())

			// 使用不存在的档案ID创建关联
			nonExistentArchiveID := int64(999999 + randNum)
			createRelationCommand := commandTesthelpers.NewCreateArchiveMediaRelationCommandBuilder().
				WithArchiveId(nonExistentArchiveID).
				WithDocumentId(mediaID).
				WithRelationType("primary").
				Build()

			// 发送创建关联请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/archive-media-relations", createRelationCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证错误响应
			relationHelper.VerifyErrorResponse(resp, "不存在")
		})

		It("当媒体ID不存在时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 使用助手创建档案
			archiveTitle := fmt.Sprintf("不存在媒体关联测试档案_%06d", randNum)
			archiveID, err := archiveHelper.CreateTestArchive(archiveTitle)
			Expect(err).NotTo(HaveOccurred())

			// 使用不存在的媒体ID创建关联
			nonExistentMediaID := int64(999999 + randNum)
			createRelationCommand := commandTesthelpers.NewCreateArchiveMediaRelationCommandBuilder().
				WithArchiveId(archiveID).
				WithDocumentId(nonExistentMediaID).
				WithRelationType("primary").
				Build()

			// 发送创建关联请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/archive-media-relations", createRelationCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证错误响应
			relationHelper.VerifyErrorResponse(resp, "不存在")
		})
	})

	Describe("BatchCreateArchiveMediaRelation", func() {
		It("成功批量创建档案媒体关联", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 使用助手创建档案
			archiveTitle := fmt.Sprintf("批量关联测试档案_%06d", randNum)
			archiveID, err := archiveHelper.CreateTestArchive(archiveTitle)
			Expect(err).NotTo(HaveOccurred())

			// 使用助手创建3个媒体
			mediaIDs := make([]int64, 3)
			for i := 0; i < 3; i++ {
				mediaName := fmt.Sprintf("批量关联测试媒体_%06d_%d", randNum, i+1)
				mediaID, err := mediaHelper.CreateTestMedia(mediaName)
				Expect(err).NotTo(HaveOccurred())
				mediaIDs[i] = mediaID
			}

			// 批量创建档案媒体关联
			batchCreateCommand := commandTesthelpers.NewBatchCreateArchiveMediaRelationCommandBuilder().
				WithArchiveId(archiveID).
				WithDocumentIds(mediaIDs).
				WithRelationType("batch_primary").
				Build()

			// 发送批量创建关联请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/archive-media-relations/batch", batchCreateCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证响应
			relationHelper.VerifyBatchCreateRelationResponse(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			data := body["data"].(map[string]interface{})
			Expect(data["archiveId"]).To(Equal(float64(batchCreateCommand.ArchiveId)))
			documentIds := data["documentIds"].([]interface{})
			Expect(len(documentIds)).To(Equal(3))

			// 等待领域事件处理
			relationHelper.WaitForEventProcessing()

			// 验证数据库记录存在性
			relationHelper.VerifyBatchRelationsInCommandDB(archiveID, mediaIDs)
			relationHelper.VerifyBatchRelationsInQueryDB(archiveID, mediaIDs)

			fmt.Printf("✅ 成功批量创建档案媒体关联: ArchiveID=%d, MediaIDs=%v\n", archiveID, mediaIDs)
		})

		It("当文档ID列表为空时应该返回错误", func() {
			// 创建批量创建命令，使用空的文档ID列表
			batchCreateCommand := commandTesthelpers.NewBatchCreateArchiveMediaRelationCommandBuilder().
				WithArchiveId(1).
				WithDocumentIds([]int64{}).
				WithRelationType("empty_test").
				Build()

			// 发送批量创建关联请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/archive-media-relations/batch", batchCreateCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证错误响应
			relationHelper.VerifyErrorResponse(resp, "不能为空")
		})

		It("当档案不存在时批量创建应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 使用助手创建3个媒体
			mediaIDs := make([]int64, 3)
			for i := 0; i < 3; i++ {
				mediaName := fmt.Sprintf("不存在档案批量关联测试媒体_%06d_%d", randNum, i+1)
				mediaID, err := mediaHelper.CreateTestMedia(mediaName)
				Expect(err).NotTo(HaveOccurred())
				mediaIDs[i] = mediaID
			}

			// 使用不存在的档案ID批量创建关联
			nonExistentArchiveID := int64(999999 + randNum)
			batchCreateCommand := commandTesthelpers.NewBatchCreateArchiveMediaRelationCommandBuilder().
				WithArchiveId(nonExistentArchiveID).
				WithDocumentIds(mediaIDs).
				WithRelationType("nonexistent_archive").
				Build()

			// 发送批量创建关联请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/archive-media-relations/batch", batchCreateCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证错误响应
			relationHelper.VerifyErrorResponse(resp, "不存在")
		})
	})

	Describe("DeleteArchiveMediaRelation", func() {
		Context("单个档案媒体关联删除操作", func() {
			var createdArchiveID int64
			var createdMediaID int64
			var createdRelationID int64

			BeforeEach(func() {
				// 随机生成一个6位数字，避免与其它用例冲突
				rand.Seed(time.Now().UnixNano())
				randNum := 100000 + rand.Intn(900000)

				var err error
				// 使用助手创建档案和媒体
				archiveTitle := fmt.Sprintf("删除关联测试档案_%06d", randNum)
				createdArchiveID, err = archiveHelper.CreateTestArchive(archiveTitle)
				Expect(err).NotTo(HaveOccurred())

				mediaName := fmt.Sprintf("删除关联测试媒体_%06d", randNum)
				createdMediaID, err = mediaHelper.CreateTestMedia(mediaName)
				Expect(err).NotTo(HaveOccurred())

				// 使用助手创建关联
				createdRelationID, err = relationHelper.CreateTestRelation(createdArchiveID, createdMediaID)
				Expect(err).NotTo(HaveOccurred())
			})

			It("应该成功删除指定的档案媒体关联", func() {
				// 发送删除关联请求
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "DELETE", fmt.Sprintf("/api/v1/archive-media-relations/%d", createdRelationID), nil, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 验证删除响应
				relationHelper.VerifyDeleteRelationResponse(resp)

				// 验证响应内容
				body, err := testhelpers.GetResponseBody(resp)
				Expect(err).NotTo(HaveOccurred())
				data := body["data"].(float64)
				Expect(data).To(Equal(float64(createdRelationID)))

				// 等待领域事件处理
				relationHelper.WaitForEventProcessing()

				// 验证关联确实被删除
				relationHelper.VerifyRelationNotExistsInCommandDB(createdRelationID)
				relationHelper.VerifyRelationNotExistsInQueryDB(createdRelationID)

				fmt.Printf("✅ 成功删除档案媒体关联: RelationID=%d\n", createdRelationID)
			})

			It("当删除不存在的关联时应该返回错误", func() {
				// 随机生成一个6位数字，避免与其它用例冲突
				rand.Seed(time.Now().UnixNano())
				randNum := 100000 + rand.Intn(900000)
				nonExistentID := int64(999999 + randNum)

				// 发送删除关联请求
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "DELETE", fmt.Sprintf("/api/v1/archive-media-relations/%d", nonExistentID), nil, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 验证错误响应
				relationHelper.VerifyErrorResponse(resp, "不存在")
			})

			It("当ID参数无效时应该返回错误", func() {
				// 发送删除关联请求，使用无效的ID参数
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "DELETE", "/api/v1/archive-media-relations/invalid_id", nil, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 验证错误响应
				body, err := testhelpers.GetResponseBody(resp)
				Expect(err).NotTo(HaveOccurred())
				Expect(body["code"]).To(Equal(float64(http.StatusBadRequest)))
				Expect(body["msg"]).To(Equal("无效的ID参数"))
			})
		})
	})

	Describe("BatchDeleteArchiveMediaRelation", func() {
		It("成功批量删除多个已存在的档案媒体关联", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 使用助手创建档案
			archiveTitle := fmt.Sprintf("批量删除关联测试档案_%06d", randNum)
			archiveID, err := archiveHelper.CreateTestArchive(archiveTitle)
			Expect(err).NotTo(HaveOccurred())

			// 使用助手创建3个媒体和关联
			relationIDs := make([]int64, 3)
			for i := 0; i < 3; i++ {
				mediaName := fmt.Sprintf("批量删除关联测试媒体_%06d_%d", randNum, i+1)
				mediaID, err := mediaHelper.CreateTestMedia(mediaName)
				Expect(err).NotTo(HaveOccurred())

				relationID, err := relationHelper.CreateTestRelation(archiveID, mediaID)
				Expect(err).NotTo(HaveOccurred())
				relationIDs[i] = relationID
			}

			// 批量删除档案媒体关联
			batchDeleteCommand := commandTesthelpers.NewBatchDeleteArchiveMediaRelationCommandBuilder().
				WithIDs(relationIDs).
				Build()

			// 发送批量删除关联请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "DELETE", "/api/v1/archive-media-relations/batch", batchDeleteCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证批量删除响应
			relationHelper.VerifyBatchDeleteRelationResponse(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			data := body["data"].([]interface{})
			Expect(len(data)).To(Equal(3))

			// 等待领域事件处理
			relationHelper.WaitForEventProcessing()

			// 验证所有关联都被删除
			for _, relationID := range relationIDs {
				relationHelper.VerifyRelationNotExistsInCommandDB(relationID)
				relationHelper.VerifyRelationNotExistsInQueryDB(relationID)
			}

			fmt.Printf("✅ 成功批量删除档案媒体关联: RelationIDs=%v\n", relationIDs)
		})

		It("当ID列表为空时应该返回错误", func() {
			// 创建批量删除命令，使用空的ID列表
			batchDeleteCommand := commandTesthelpers.NewBatchDeleteArchiveMediaRelationCommandBuilder().
				WithIDs([]int64{}).
				Build()

			// 发送批量删除关联请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "DELETE", "/api/v1/archive-media-relations/batch", batchDeleteCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证错误响应
			relationHelper.VerifyErrorResponse(resp, "不能为空")
		})

		It("当尝试批量删除不存在的关联时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建批量删除命令，使用不存在的ID
			nonExistentIDs := []int64{
				int64(1000000 + randNum),
				int64(2000000 + randNum),
				int64(3000000 + randNum),
			}
			batchDeleteCommand := commandTesthelpers.NewBatchDeleteArchiveMediaRelationCommandBuilder().
				WithIDs(nonExistentIDs).
				Build()

			// 发送批量删除关联请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "DELETE", "/api/v1/archive-media-relations/batch", batchDeleteCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证错误响应
			relationHelper.VerifyErrorResponse(resp, "失败")
		})

		It("成功部分删除关联（混合存在和不存在的ID）", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 使用助手创建档案
			archiveTitle := fmt.Sprintf("部分删除关联测试档案_%06d", randNum)
			archiveID, err := archiveHelper.CreateTestArchive(archiveTitle)
			Expect(err).NotTo(HaveOccurred())

			// 使用助手创建2个媒体和关联
			existingIDs := make([]int64, 2)
			for i := 0; i < 2; i++ {
				mediaName := fmt.Sprintf("部分删除关联测试媒体_%06d_%d", randNum, i+1)
				mediaID, err := mediaHelper.CreateTestMedia(mediaName)
				Expect(err).NotTo(HaveOccurred())

				relationID, err := relationHelper.CreateTestRelation(archiveID, mediaID)
				Expect(err).NotTo(HaveOccurred())
				existingIDs[i] = relationID
			}

			// 混合存在和不存在的ID
			mixedIDs := append(existingIDs, int64(999999+randNum))

			// 批量删除档案媒体关联
			batchDeleteCommand := commandTesthelpers.NewBatchDeleteArchiveMediaRelationCommandBuilder().
				WithIDs(mixedIDs).
				Build()

			// 发送批量删除关联请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "DELETE", "/api/v1/archive-media-relations/batch", batchDeleteCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 这种情况下，应该删除存在的记录，但可能会有警告或部分成功的响应
			// 具体行为取决于业务逻辑的实现
			_, err = testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())

			// 等待领域事件处理
			relationHelper.WaitForEventProcessing()

			// 验证存在的记录已删除
			for _, relationID := range existingIDs {
				relationHelper.VerifyRelationNotExistsInCommandDB(relationID)
			}

			fmt.Printf("✅ 成功部分删除档案媒体关联: ExistingIDs=%v\n", existingIDs)
		})
	})
})
