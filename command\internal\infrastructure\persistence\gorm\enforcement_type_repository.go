package persistence

import (
	"context"
	"errors"
	"jxt-evidence-system/evidence-management/command/internal/application/transaction"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/enforcementtype"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/enforcementtype/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"gorm.io/gorm"
)

func init() {
	registrations = append(registrations, registerEnforcementTypeRepoDependencies)
}

// 注册EnforceTypeRepository的依赖注入
func registerEnforcementTypeRepoDependencies() {
	if err := di.Provide(func() repository.EnforcementTypeRepository {
		return &GormEnforcementTypeRepository{}
	}); err != nil {
		logger.Error("failed to provide EnforcementTypeRepository", "error", err)
	}
}

// GormEnforcementTypeRepository GORM执法类型仓储实现
type GormEnforcementTypeRepository struct {
	GormRepository
}

func (r *GormEnforcementTypeRepository) Create(ctx context.Context, tx transaction.Transaction, model *enforcementtype.EnforcementType) error {
	if tx == nil {
		return errors.New("事务不能为空")
	}
	return GetTx(tx).Create(model).Error
}

func (r *GormEnforcementTypeRepository) Update(ctx context.Context, tx transaction.Transaction, model *enforcementtype.EnforcementType) error {
	if tx == nil {
		return errors.New("事务不能为空")
	}
	result := GetTx(tx).Save(model)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("无权更新该数据")
	}
	return nil
}

func (r *GormEnforcementTypeRepository) Delete(ctx context.Context, tx transaction.Transaction, id interface{}) error {
	if tx == nil {
		return errors.New("事务不能为空")
	}
	result := GetTx(tx).Delete(&enforcementtype.EnforcementType{}, id)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("要删除的执法类型不存在")
	}
	return nil
}

func (r *GormEnforcementTypeRepository) FindByID(ctx context.Context, id interface{}) (*enforcementtype.EnforcementType, error) {
	db, err := r.GetOrm(ctx)
	if err != nil {
		return nil, err
	}

	var model enforcementtype.EnforcementType
	err = db.WithContext(ctx).First(&model, id).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return &model, nil
}

func (r *GormEnforcementTypeRepository) FindByCode(ctx context.Context, code string) (*enforcementtype.EnforcementType, error) {
	db, err := r.GetOrm(ctx)
	if err != nil {
		return nil, err
	}

	var model enforcementtype.EnforcementType
	err = db.WithContext(ctx).Where("enforcement_type_code = ?", code).First(&model).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return &model, nil
}

func (r *GormEnforcementTypeRepository) ListByParentID(ctx context.Context, parentID interface{}) ([]*enforcementtype.EnforcementType, error) {
	db, err := r.GetOrm(ctx)
	if err != nil {
		return nil, err
	}

	var models []*enforcementtype.EnforcementType
	err = db.WithContext(ctx).Where("parent_id = ?", parentID).Find(&models).Error
	if err != nil {
		return nil, err
	}
	return models, nil
}

func (r *GormEnforcementTypeRepository) FindChildren(ctx context.Context, id interface{}) ([]*enforcementtype.EnforcementType, error) {
	db, err := r.GetOrm(ctx)
	if err != nil {
		return nil, err
	}

	var models []*enforcementtype.EnforcementType
	err = db.WithContext(ctx).Where("parent_id = ?", id).Find(&models).Error
	if err != nil {
		return nil, err
	}

	// 递归查询所有子节点
	for _, model := range models {
		children, err := r.FindChildren(ctx, model.ID)
		if err != nil {
			return nil, err
		}
		models = append(models, children...)
	}

	return models, nil
}
