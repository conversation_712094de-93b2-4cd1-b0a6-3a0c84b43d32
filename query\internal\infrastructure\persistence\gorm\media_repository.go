package persistence

/*
需求1：增加操作数据库的超时控制
func (s *UserService) CreateUser(ctx context.Context, user repository.User) error {
	return r.db.WithContext(ctx).Create(&user).Error
}
func HandlerFunc(c *gin.Context) {
    db, _ := gorm.Open(sqlite.Open("test.db"), &gorm.Config{})
    ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
    defer cancel()

    repo := NewUserRepository(db)
    user := User{Name: "<PERSON>", Age: 30}
    if err := repo.Create(ctx, user); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Error creating user"})
        return
    }

    c.JSO<PERSON>(http.StatusOK, gin.H{"message": "User created successfully"})
}

需求2：增加操作数据库的追踪ID或用户ID传递
func HandlerFunc(c *gin.Context) {
    db, _ := gorm.Open(sqlite.Open("test.db"), &gorm.Config{})
    ctx := context.WithValue(c.Request.Context(), "traceID", "1234567890")

    traceID := ctx.Value("traceID").(string)
    fmt.Printf("TraceID: %s - Creating user\n", traceID)

    repo := NewUserRepository(db)
    user := User{Name: "John Doe", Age: 30}
    if err := repo.Create(ctx, user); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Error creating user"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "User created successfully"})
}
需求3：按照单一职责repo去掉日志打印，只需要返回error给application service
需求4：用单例模式实现mediaRepo，所哟mediaService 共用这个单例mediaRepo
*/
import (
	"context"
	"errors"
	"fmt"
	query "jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/models"
	"jxt-evidence-system/evidence-management/query/internal/models/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"
	cQuery "jxt-evidence-system/evidence-management/shared/common/query"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"gorm.io/gorm"
)

// jiyuanjie 添加通过依赖注入创建repo
func init() {
	registrations = append(registrations, registerMediaRepoDependencies)
}

// jiyuanjie GormMediaReadModelRepository的依赖注入
func registerMediaRepoDependencies() {
	if err := di.Provide(func() repository.MediaReadModelRepository {
		return &gormMediaReadModelRepository{}
	}); err != nil {
		logger.Fatalf("failed to provide GormMediaReadModelRepository: %v", err)
	}
}

type gormMediaReadModelRepository struct {
	GormRepository
}

// 添加mediaType为0的情况处理，需要显示构造查询条件。因为gorm会把值0当无效值
func MediaTypeZeroScope(r *query.MediaPagedQuery) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		// MediaType值为0，表示照片，这里确保能够过滤照片
		if r.MediaCate == 0 {
			fmt.Printf("MediaGetPageReq++1: %+v", r)
			return db.Where("t_evidence_media_read.media_cate = ?", 0)
		}
		// 如果 MediaCate 为-1，表示没有 MediaCate 过滤条件
		if r.MediaCate == -1 {
			r.MediaCate = 0
		}
		return db
	}
}

// 新的封装函数,确保顺序执行
func CombinedScope(r *query.MediaPagedQuery) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		// 首先应用 MediaTypeZeroScope
		db = MediaTypeZeroScope(r)(db)

		// 构建查询条件
		conditions := make([]func(*gorm.DB) *gorm.DB, 0)

		// 添加媒体名称查询条件
		if r.MediaName != "" {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_media_read.media_name LIKE ?", "%"+r.MediaName+"%")
			})
		}

		// 添加其他查询条件...
		if !r.ShotTimeStart.IsZero() {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_media_read.shot_time >= ?", r.ShotTimeStart)
			})
		}

		if !r.ShotTimeEnd.IsZero() {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_media_read.shot_time <= ?", r.ShotTimeEnd)
			})
		}

		if !r.ImportTimeStart.IsZero() {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_media_read.import_time >= ?", r.ImportTimeStart)
			})
		}

		if !r.ImportTimeEnd.IsZero() {
			conditions = append(conditions, func(db *gorm.DB) *gorm.DB {
				return db.Where("t_evidence_media_read.import_time <= ?", r.ImportTimeEnd)
			})
		}

		// 应用所有条件
		for _, condition := range conditions {
			db = condition(db)
		}

		return db
	}
}

func (repo *gormMediaReadModelRepository) GetPage(ctx context.Context, r *query.MediaPagedQuery) (*[]models.MediaReadModel, int64, error) {
	var data models.MediaReadModel
	var list []models.MediaReadModel
	var count int64

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return nil, 0, err
	}

	err = db.WithContext(ctx).Model(&data).
		Scopes(
			CombinedScope(r), // 使用新的组合作用域
			cQuery.Paginate(r.GetPageSize(), r.GetPageIndex()),
		).
		Find(&list).Limit(-1).Offset(-1).
		Count(&count).Error
	if err != nil {
		return nil, 0, err
	}
	return &list, count, nil
}

func (repo *gormMediaReadModelRepository) FindByID(ctx context.Context, id int64) (*models.MediaReadModel, error) {
	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}

	var model models.MediaReadModel
	err = db.WithContext(ctx).First(&model, id).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.New("查看对象不存在或无权查看")
	}
	if err != nil {
		return nil, err
	}
	return &model, nil
}

// FindByName 根据名称查询媒体读模型
func (repo *gormMediaReadModelRepository) FindByName(ctx context.Context, name string) (*models.MediaReadModel, error) {
	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}
	var model models.MediaReadModel
	db = db.WithContext(ctx).Where("media_name = ?", name).First(&model)
	if err = db.Error; err != nil {
		return nil, err
	}
	return &model, nil
}

// Create 创建媒体读模型
func (repo *gormMediaReadModelRepository) Create(ctx context.Context, model *models.MediaReadModel) error {
	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return err
	}
	result := db.WithContext(ctx).Save(model) //因为model.id已经存在，所以使用Save
	if result.Error != nil {
		//logger.Error("创建媒体读模型失败", "error", result.Error)
		return result.Error
	}
	return nil
}

// Update 更新媒体读模型
func (repo *gormMediaReadModelRepository) UpdateByID(ctx context.Context, id int64, updates map[string]interface{}) error {
	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return err
	}
	result := db.WithContext(ctx).Model(&models.MediaReadModel{}).Where("media_id = ?", id).Updates(updates)
	if result.Error != nil {
		//logger.Error("更新媒体读模型失败", "error", result.Error)
		return result.Error
	}
	return nil
}

// UpdateManyByIDs 批量更新
func (repo *gormMediaReadModelRepository) UpdateManyByIDs(ctx context.Context, ids []int64, updates map[string]interface{}) (rowsAffected int64, err error) {
	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return 0, err
	}
	result := db.WithContext(ctx).Model(&models.MediaReadModel{}).Where("media_id IN ?", ids).Updates(updates)
	if result.Error != nil {
		//logger.Error("批量更新媒体读模型失败", "error", result.Error)
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

// DeleteByID 删除媒体读模型
func (repo *gormMediaReadModelRepository) DeleteByID(ctx context.Context, id int64) error {
	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return err
	}
	result := db.WithContext(ctx).Delete(&models.MediaReadModel{}, id)
	if result.Error != nil {
		//logger.Error("删除媒体读模型失败", "error", result.Error)
		return result.Error
	}
	return nil
}

// DeleteManyByIDs 批量删除媒体读模型
func (repo *gormMediaReadModelRepository) DeleteManyByIDs(ctx context.Context, ids []int64) (rowsAffected int64, err error) {
	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetQueryOrm(ctx)
	if err != nil {
		return 0, err
	}
	result := db.WithContext(ctx).Delete(&models.MediaReadModel{}, ids)
	if result.Error != nil {
		//logger.Error("删除媒体读模型失败", "error", result.Error)
		return 0, result.Error
	}
	return result.RowsAffected, nil
}
