package models

import (
	"jxt-evidence-system/evidence-management/shared/common/models"
	"time"
)

/*
档案媒体关联查询模型设计策略：

采用"核心字段冗余 + 实时补充查询"的混合策略：

1. 冗余存储的稳定字段：
   - 关联关系：ID、ArchiveId、MediaId
   - 档案标识：ArchiveCode、ArchiveTitle、ArchiveType
   - 媒体标识：MediaName、MediaCate、MediaSuffix、FileSize、VideoDuration
   - 时间信息：ShotTime、ShotTimeStart、ImportTime、ExpiryTime
   - 组织信息：OrgID、OrgName
   - 警员信息：PoliceID、PoliceName
   - 设备信息：RecorderID、RecorderNo
   - 业务字段：RelationType、Remarks

2. 不冗余的动态字段（需要时实时查询媒体读模型）：
   - 用户经常修改：ImportantLevel、IsLocked、EnforceType、Comments等
   - 详细信息：组织信息、警员信息、存储信息、设备信息等

3. 查询策略：
   - 列表查询：直接使用关联读模型，获得极致性能
   - 详情查询：关联读模型 + 媒体读模型，获得完整信息
   - 实时状态：单独查询媒体读模型

4. 优势：
   - 避免了频繁的级联更新
   - 减少了数据不一致的风险
   - 保持了查询性能
   - 降低了维护成本
*/

/*
通常在档案管理页面表格中显示的字段：
档案相关字段：
archivesCode - 档案编号
archivesTitle - 档案名称/标题
媒体相关字段：
docName - 视频/文档名称
cate - 媒体类型（如mp4）
duration - 视频时长（格式化后显示）
docSize - 媒体大小（格式化后显示）
组织和人员字段：
orgName - 媒体管理部门/单位
inputUserName - 媒体摄录人员
时间字段：
createTime - 摄录时间
uploadTime - 上传时间
其他字段：
expiryTime - 存储期限
*/

// ArchiveMediaRelationReadModel 档案媒体关联查询模型
// 采用"核心字段冗余 + 实时补充查询"策略，只存储相对稳定的字段
type ArchiveMediaRelationReadModel struct {
	// === 核心关联字段（必须） ===
	ID        int64 `json:"id" gorm:"primaryKey;column:id;autoIncrement:false;comment:关联ID(和写数据库id相同)"`
	ArchiveId int64 `json:"archiveId" gorm:"column:archive_id;comment:档案ID"`
	MediaId   int64 `json:"mediaId" gorm:"column:media_id;comment:媒体ID"`

	// === 稳定的档案字段（冗余，用于反向查询） ===
	ArchiveCode  string `json:"archiveCode" gorm:"size:128;column:archive_code;comment:档案编号"`
	ArchiveTitle string `json:"archiveTitle" gorm:"size:255;column:archive_title;comment:档案标题"`
	ArchiveType  int    `json:"archiveType" gorm:"size:4;column:archive_type;comment:档案类型"`

	// === 稳定的媒体标识字段（冗余，用于列表显示） ===
	MediaName     string `json:"mediaName" gorm:"size:128;column:media_name;comment:媒体名称"`
	MediaCate     int    `json:"mediaCate" gorm:"size:4;column:media_cate;comment:媒体类型(0: 照片 1: 音频 2: 视频 3:日志）"`
	MediaSuffix   string `json:"mediaSuffix" gorm:"size:16;column:media_suffix;comment:媒体后缀"`
	FileSize      int64  `json:"fileSize" gorm:"column:file_size;comment:文件大小(单位: KB)"`
	VideoDuration int    `json:"videoDuration" gorm:"column:video_duration;comment:视频时长（单位: 毫秒）"`

	// === 稳定的时间信息 ===
	ShotTime      time.Time  `json:"shotTime" gorm:"column:shot_time;comment:拍摄时间"`
	ShotTimeStart time.Time  `json:"shotTimeStart" gorm:"column:shot_time_start;comment:拍摄开始时间"`
	ImportTime    *time.Time `json:"importTime" gorm:"column:import_time;default:NULL;comment:导入时间"`
	ExpiryTime    *time.Time `json:"expiryTime" gorm:"column:expiry_time;default:NULL;comment:过期时间"`

	// === 稳定的组织信息（冗余，用于列表显示） ===
	OrgID   int    `json:"orgId" gorm:"column:org_id;comment:组织ID"`
	OrgName string `json:"orgName" gorm:"size:255;column:org_name;comment:组织名称"`

	// === 稳定的警员信息（冗余，用于列表显示） ===
	PoliceID   int    `json:"policeId" gorm:"column:police_id;comment:警员ID"`
	PoliceName string `json:"policeName" gorm:"size:255;column:police_name;comment:警员姓名"`

	// === 稳定的设备信息（冗余，用于列表显示） ===
	RecorderID int    `json:"recorderId" gorm:"column:recorder_id;comment:执法仪ID"`
	RecorderNo string `json:"recorderNo" gorm:"size:255;column:recorder_no;comment:执法仪编号"`

	// === 关联业务信息 ===
	// 关联类型（扩展字段，为将来可能的需求预留）
	RelationType string `json:"relationType" gorm:"size:32;column:relation_type;comment:关联类型"`
	Remarks      string `json:"remarks" gorm:"size:512;column:remarks;comment:备注信息"`

	// === 审计字段 ===
	models.ControlBy
	models.ModelTime

	// === 以下字段不冗余，需要时通过实时查询获取 ===
	// ImportantLevel - 用户经常修改，不存储，需要时查询媒体读模型
	// IsLocked - 用户经常修改，不存储，需要时查询媒体读模型
	// IsNonEnforcementMedia - 用户经常修改，不存储，需要时查询媒体读模型
	// EnforceType - 用户经常修改，不存储，需要时查询媒体读模型
	// Comments - 用户经常修改，不存储，需要时查询媒体读模型
	// 存储信息 - 可通过媒体读模型获取，减少冗余
}

// TableName 指定表名
func (*ArchiveMediaRelationReadModel) TableName() string {
	return "t_archive_media_relations_read"
}

// ArchiveMediaRelationReadModelList 档案媒体关联查询模型列表
type ArchiveMediaRelationReadModelList struct {
	List  []ArchiveMediaRelationReadModel `json:"list"`
	Total int64                           `json:"total"`
}

func (e *ArchiveMediaRelationReadModel) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *ArchiveMediaRelationReadModel) GetId() interface{} {
	return e.ID
}

// ArchiveMediaRelationSummary 档案媒体关联汇总信息（基于稳定字段统计）
type ArchiveMediaRelationSummary struct {
	ArchiveId  int64 `json:"archiveId"`  // 档案ID
	MediaCount int   `json:"mediaCount"` // 关联的媒体数量
	PhotoCount int   `json:"photoCount"` // 照片数量（基于MediaCate）
	VideoCount int   `json:"videoCount"` // 视频数量（基于MediaCate）
	AudioCount int   `json:"audioCount"` // 音频数量（基于MediaCate）
	LogCount   int   `json:"logCount"`   // 日志数量（基于MediaCate）
	TotalSize  int64 `json:"totalSize"`  // 总文件大小（基于FileSize）
	// 注意：LockedCount等动态信息需要通过实时查询媒体读模型获取
}

// MediaArchiveRelationSummary 媒体档案关联汇总信息（从媒体角度）
type MediaArchiveRelationSummary struct {
	MediaId       int64 `json:"mediaId"`       // 媒体ID
	ArchiveCount  int   `json:"archiveCount"`  // 关联的档案数量
	IsMultiLinked bool  `json:"isMultiLinked"` // 是否多档案关联
}
