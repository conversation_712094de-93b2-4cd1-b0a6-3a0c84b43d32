package persistence

import (
	"context"
	"errors"
	query "jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/models"
	"jxt-evidence-system/evidence-management/query/internal/models/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"
	cQuery "jxt-evidence-system/evidence-management/shared/common/query"
	"reflect"
	"strings"
	"unicode"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"gorm.io/gorm"
)

func init() {
	registrations = append(registrations, registerEnforcementTypeRepoDependencies)
}

func registerEnforcementTypeRepoDependencies() {
	if err := di.Provide(func() repository.EnforcementTypeReadModelRepository {
		return &gormEnforcementTypeReadModelRepository{}
	}); err != nil {
		logger.Fatalf("failed to provide GormEnforcementTypeReadModelRepository: %v", err)
	}
}

type gormEnforcementTypeReadModelRepository struct {
	GormRepository
}

// 组合查询条件
func CombinedEnforcementTypeScope(r *query.EnforcementTypePagedQuery) func(*gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		// 构建查询条件
		buildQuery := func(db *gorm.DB) *gorm.DB {
			if r.EnforcementTypeCode != "" {
				db = func(db *gorm.DB) *gorm.DB {
					return db.Where("t_evidence_enforcement_types.enforcement_type_code = ?", r.EnforcementTypeCode)
				}(db)
			}
			if r.EnforcementTypeName != "" {
				db = func(db *gorm.DB) *gorm.DB {
					return db.Where("t_evidence_enforcement_types.enforcement_type_name LIKE ?", "%"+r.EnforcementTypeName+"%")
				}(db)
			}
			if r.EnforcementTypeDesc != "" {
				db = func(db *gorm.DB) *gorm.DB {
					return db.Where("t_evidence_enforcement_types.enforcement_type_desc LIKE ?", "%"+r.EnforcementTypeDesc+"%")
				}(db)
			}
			if r.ParentId != 0 {
				db = func(db *gorm.DB) *gorm.DB {
					return db.Where("t_evidence_enforcement_types.parent_id = ?", r.ParentId)
				}(db)
			}
			if r.Source != "" {
				db = func(db *gorm.DB) *gorm.DB {
					return db.Where("t_evidence_enforcement_types.source = ?", r.Source)
				}(db)
			}
			return db
		}
		return buildQuery(db)
	}
}

func (repo *gormEnforcementTypeReadModelRepository) GetPage(ctx context.Context, r *query.EnforcementTypePagedQuery) ([]*models.EnforcementType, int64, error) {
	var data models.EnforcementType
	var list []*models.EnforcementType
	var count int64

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetCommandOrm(ctx) //特例：为了实时查到执法类型，从命令库查询
	if err != nil {
		return nil, 0, err
	}

	err = db.WithContext(ctx).Model(&data).
		Scopes(
			CombinedEnforcementTypeScope(r), // 使用新的组合作用域
			cQuery.Paginate(r.GetPageSize(), r.GetPageIndex()),
		).
		Find(&list).Limit(-1).Offset(-1).
		Count(&count).Error
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (repo *gormEnforcementTypeReadModelRepository) GetAll(ctx context.Context) ([]*models.EnforcementType, int64, error) {
	var data models.EnforcementType
	var list []*models.EnforcementType // 这里使用指针切片对于应用服务构建执法类型树形结构非常有好处
	var count int64

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetCommandOrm(ctx) //特例：为了实时查到执法类型，从命令库查询
	if err != nil {
		return nil, 0, err
	}

	err = db.WithContext(ctx).Model(&data).
		Find(&list).   // 查询所有数据到list
		Count(&count). // 获取总数
		Error
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (repo *gormEnforcementTypeReadModelRepository) FindByID(ctx context.Context, id interface{}) (*models.EnforcementType, error) {
	db, err := repo.GetCommandOrm(ctx) //特例：为了实时查到执法类型，从命令库查询
	if err != nil {
		return nil, err
	}

	var model models.EnforcementType
	db = db.WithContext(ctx).First(&model, id)
	err = db.Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		return nil, err
	}
	if err = db.Error; err != nil {
		return nil, err
	}
	return &model, nil
}

func (repo *gormEnforcementTypeReadModelRepository) FindByCode(ctx context.Context, code string) (*models.EnforcementType, error) {
	db, err := repo.GetCommandOrm(ctx) //特例：为了实时查到执法类型，从命令库查询
	if err != nil {
		return nil, err
	}

	var model models.EnforcementType
	db = db.WithContext(ctx).Where("enforcement_type_code = ?", code).First(&model)
	err = db.Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看对象不存在或无权查看")
		return nil, err
	}
	if err = db.Error; err != nil {
		return nil, err
	}
	return &model, nil
}

func (repo *gormEnforcementTypeReadModelRepository) List(ctx context.Context) ([]*models.EnforcementType, error) {
	db, err := repo.GetCommandOrm(ctx) //特例：为了实时查到执法类型，从命令库查询
	if err != nil {
		return nil, err
	}

	var models []*models.EnforcementType
	err = db.WithContext(ctx).Find(&models).Error
	if err != nil {
		return nil, err
	}
	return models, nil
}

func (repo *gormEnforcementTypeReadModelRepository) ListByParentID(ctx context.Context, parentID interface{}) ([]*models.EnforcementType, error) {
	db, err := repo.GetCommandOrm(ctx) //特例：为了实时查到执法类型，从命令库查询
	if err != nil {
		return nil, err
	}

	var models []*models.EnforcementType
	err = db.WithContext(ctx).Where("parent_id = ?", parentID).Find(&models).Error
	if err != nil {
		return nil, err
	}
	return models, nil
}

func (repo *gormEnforcementTypeReadModelRepository) Create(ctx context.Context, model *models.EnforcementTypeReadModel) error {
	db, err := repo.GetQueryOrm(ctx) //根据领域事件写执法类型读模型数据库
	if err != nil {
		return err
	}
	result := db.WithContext(ctx).Save(model) //因为model.id已经存在，所以使用Save
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// getDBFieldName 从模型字段名获取数据库字段名
func getDBFieldName(modelFieldName string) string {
	// 使用反射获取结构体字段的标签
	t := reflect.TypeOf(models.EnforcementTypeReadModel{})

	// 尝试直接查找字段
	field, found := t.FieldByName(modelFieldName)
	if found {
		// 解析 gorm 标签
		gormTag := field.Tag.Get("gorm")
		for _, tag := range strings.Split(gormTag, ";") {
			if strings.HasPrefix(tag, "column:") {
				return strings.TrimPrefix(tag, "column:")
			}
		}
	}

	// 如果找不到对应的字段或标签，则使用蛇形命名转换
	return toSnakeCase(modelFieldName)
}

// toSnakeCase 将驼峰命名转换为蛇形命名
func toSnakeCase(s string) string {
	var result string
	for i, r := range s {
		if i > 0 && r >= 'A' && r <= 'Z' {
			result += "_"
		}
		result += string(unicode.ToLower(r))
	}
	return result
}

func (repo *gormEnforcementTypeReadModelRepository) Update(ctx context.Context, id interface{}, updates map[string]interface{}) error {
	db, err := repo.GetQueryOrm(ctx) //根据领域事件写执法类型读模型数据库
	if err != nil {
		return err
	}

	// 将字段名转换为数据库列名
	dbUpdates := make(map[string]interface{})
	for k, v := range updates {
		// 使用辅助函数获取数据库字段名
		dbFieldName := getDBFieldName(k)
		dbUpdates[dbFieldName] = v
	}

	result := db.WithContext(ctx).Model(&models.EnforcementTypeReadModel{}).Where("enforcement_type_id = ?", id).Updates(dbUpdates)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (repo *gormEnforcementTypeReadModelRepository) Delete(ctx context.Context, id interface{}) error {
	db, err := repo.GetQueryOrm(ctx) //根据领域事件写执法类型读模型数据库
	if err != nil {
		return err
	}
	result := db.WithContext(ctx).Delete(&models.EnforcementTypeReadModel{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}
