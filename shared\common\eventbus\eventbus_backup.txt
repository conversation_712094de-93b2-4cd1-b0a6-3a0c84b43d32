package eventbus

import (
	"context"
	"fmt"
	"log"
	"sync"
	"sync/atomic"
	"time"

	"github.com/IBM/sarama"
	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill-kafka/v2/pkg/kafka"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/ChenBigdata421/jxt-core/sdk/pkg"
)

var DefaultKafkaSubscriberManager *KafkaSubscriberManager

type KafkaSubscriberManager struct {
	Subscriber           atomic.Value // 存储 *kafka.Subscriber
	Config               kafka.SubscriberConfig
	Logger               watermill.LoggerAdapter
	wg                   sync.WaitGroup
	mu                   sync.Mutex
	rootCtx              context.Context
	rootCancel           context.CancelFunc
	healthCheckTopic     string
	lastMessageTime      time.Time
	healthCheckInterval  time.Duration
	maxReconnectAttempts int
	backoff              time.Duration
	maxBackoff           time.Duration
}

func NewKafkaSubscriberManager(config kafka.SubscriberConfig, healthCheckInterval time.Duration) (*KafkaSubscriberManager, error) {
	if len(config.Brokers) == 0 {
		return nil, fmt.Errorf("no Kafka brokers specified in config")
	}
	if healthCheckInterval <= 0 {
		return nil, fmt.Errorf("invalid health check interval")
	}
	return &KafkaSubscriberManager{
		Config:               config,
		Logger:               watermill.NewStdLogger(false, false),
		healthCheckTopic:     "health_check_topic",
		healthCheckInterval:  healthCheckInterval,
		maxReconnectAttempts: 5,
		backoff:              time.Second,
		maxBackoff:           time.Minute,
	}, nil
}

func (km *KafkaSubscriberManager) Start() error {
	km.rootCtx, km.rootCancel = context.WithCancel(context.Background())

	if err := km.initSubscriber(); err != nil {
		return err
	}
	// 订阅健康检测主题
	if err := km.subscribeToHealthCheckTopic(); err != nil {
		return err
	}

	log.Printf(pkg.Green("Started subscribing to health check topic: %s"), km.healthCheckTopic)

	if err := km.testConnection(); err != nil {
		log.Printf(pkg.Red("Kafka connection test failed: %v"), err)
		// 处理错误...
	} else {
		log.Println(pkg.Green("Kafka connection test successful"))
	}

	km.wg.Add(2)
	go km.healthCheckLoop()

	return nil
}

func (km *KafkaSubscriberManager) subscribeToHealthCheckTopic() error {
	subscriberAny := km.Subscriber.Load()
	if subscriberAny == nil {
		return fmt.Errorf("subscriber is nil")
	}

	subscriber, ok := subscriberAny.(*kafka.Subscriber)
	if !ok {
		return fmt.Errorf("invalid subscriber type")
	}

	messages, err := subscriber.Subscribe(km.rootCtx, km.healthCheckTopic)
	if err != nil {
		return fmt.Errorf("failed to subscribe to health check topic: %w", err)
	}

	go km.processHealthCheckMessages(messages)

	return nil
}

func (km *KafkaSubscriberManager) initSubscriber() error {
	// 关闭旧的 Subscriber
	if oldSubscriber := km.Subscriber.Load(); oldSubscriber != nil {
		oldSubscriber.(*kafka.Subscriber).Close()
	}

	// 创建新的 Subscriber
	subscriber, err := kafka.NewSubscriber(km.Config, km.Logger)
	if err != nil {
		return fmt.Errorf("failed to create new subscriber: %w", err)
	}
	km.Subscriber.Store(subscriber) // 使用 atomic.Value 存储新的 Subscriber
	return nil

}

func (km *KafkaSubscriberManager) testConnection() error {
	config := sarama.NewConfig()
	config.Net.DialTimeout = 10 * time.Second
	client, err := sarama.NewClient(km.Config.Brokers, config)
	if err != nil {
		return fmt.Errorf("无法连接到 Kafka 集群: %w", err)
	}
	defer client.Close()
	return nil
}

func (km *KafkaSubscriberManager) processHealthCheckMessages(messages <-chan *message.Message) {
	defer km.wg.Done()
	log.Println(pkg.Green("Started processing health check messages"))
	for msg := range messages {
		if msg != nil {
			km.updateLastMessageTime()
			log.Printf(pkg.Green("Received health check message: %s at %s"), string(msg.Payload), time.Now().Format(time.RFC3339))
			msg.Ack()
		} else {
			log.Println(pkg.Yellow("Received nil message, channel might be closed"))
		}
	}
	log.Println(pkg.Yellow("Stopped processing health check messages"))
}

func (km *KafkaSubscriberManager) updateLastMessageTime() {
	km.mu.Lock()
	defer km.mu.Unlock()
	km.lastMessageTime = time.Now()
}

func (km *KafkaSubscriberManager) healthCheckLoop() {
	defer km.wg.Done()
	ticker := time.NewTicker(km.healthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-km.rootCtx.Done():
			log.Println(pkg.Yellow("Context cancelled, stopping health check"))
			return
		case <-ticker.C:
			if err := km.healthCheck(); err != nil {
				log.Printf(pkg.Red("Kafka Subscriber Health check failed: %v\n"), err)
				if reconnectErr := km.reconnect(); reconnectErr != nil {
					log.Printf(pkg.Red("Kafka subscriber reconnection failed: %v\n"), reconnectErr)
				} else {
					log.Println(pkg.Green("Kafka subscriber reconnection successful"))
				}
			} else {
				if km.lastMessageTime.IsZero() {
					log.Println(pkg.Green("Kafka subscriber connection healthy! Awaiting initial messages..."))
				} else {
					log.Println(pkg.Green("Kafka subscriber connection healthy!"))
				}
			}
		}
	}
}

func (km *KafkaSubscriberManager) healthCheck() error {
	km.mu.Lock()
	lastMsgTime := km.lastMessageTime
	km.mu.Unlock()

	if lastMsgTime.IsZero() {
		log.Println(pkg.Yellow("Health Check: No health check messages received yet"))
		return nil // 继续运行，等待后续消息
	}

	// 设置检查时间为发送频率的 2 倍
	if time.Since(lastMsgTime) > 2*km.healthCheckInterval {
		return fmt.Errorf("no health check messages received in the last 2 minutes")
	}

	return nil
}

// 使用指数退避算法重新建立连接
func (km *KafkaSubscriberManager) reconnect() error {
	initialBackoff := km.backoff // 保存初始退避时间
	totalWaitTime := time.Duration(0)

	for i := 0; i < km.maxReconnectAttempts; i++ {
		//log.Printf("Reconnection attempt %d of %d", i+1, km.maxReconnectAttempts)

		if err := km.initSubscriber(); err == nil {
			if err := km.subscribeToHealthCheckTopic(); err != nil {
				//log.Printf("Failed to subscribe to health check topic: %v. Retrying...", err)
				continue
			}
			log.Println("Reconnection successful")
			km.backoff = initialBackoff // 重置退避时间
			return nil
		}

		//log.Printf("Reconnection attempt failed. Waiting %v before next attempt", km.backoff)
		time.Sleep(km.backoff)
		totalWaitTime += km.backoff

		km.backoff *= 2
		if km.backoff > km.maxBackoff {
			km.backoff = km.maxBackoff
		}
	}

	km.backoff = initialBackoff // 重置退避时间
	return fmt.Errorf("failed to reconnect after %d attempts over %v", km.maxReconnectAttempts, totalWaitTime)
}

func (km *KafkaSubscriberManager) Shutdown(ctx context.Context) error {
	log.Println(pkg.Yellow("Shutting down Kafka subscriber manager..."))

	km.rootCancel() // 取消根上下文

	// 使用 context 的超时机制
	done := make(chan struct{})
	go func() {
		km.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Println(pkg.Green("All goroutines stopped"))
	case <-ctx.Done():
		log.Println(pkg.Yellow("Shutdown timed out, some goroutines may still be running"))
		return ctx.Err()
	}

	subscriberAny := km.Subscriber.Load()
	if subscriberAny != nil {
		if subscriber, ok := subscriberAny.(*kafka.Subscriber); ok {
			if err := subscriber.Close(); err != nil {
				log.Printf(pkg.Red("关闭 Kafka subscriber 时出错: %v\n"), err)
				return err
			}
		}
	}

	log.Println(pkg.Green("Kafka subscriber manager shutdown completed"))
	return nil
}

func (km *KafkaSubscriberManager) SubscribeToTopic(topic string, handler func(msg *message.Message), timeout time.Duration) error {
	if topic == "" {
		return fmt.Errorf("topic cannot be empty")
	}

	subscriberAny := km.Subscriber.Load()

	if subscriberAny == nil {
		return fmt.Errorf("subscriber is nil")
	}

	// 类型断言以获取实际的 *kafka.Subscriber
	subscriber, ok := subscriberAny.(*kafka.Subscriber)
	if !ok {
		return fmt.Errorf("invalid subscriber type")
	}

	ctx, cancel := context.WithCancel(km.rootCtx)

	messages, err := subscriber.Subscribe(ctx, topic)
	if err != nil {
		cancel()
		return fmt.Errorf("failed to subscribe to topic %s: %w", topic, err)
	}

	km.wg.Add(1)
	go func() {
		defer km.wg.Done()
		defer cancel()

		for {
			select {
			case msg, ok := <-messages:
				if !ok {
					log.Printf("Channel closed for topic: %s\n", topic)
					return
				}
				km.processMessage(ctx, msg, handler, timeout)
			case <-ctx.Done():
				log.Printf("Context cancelled for topic: %s\n", topic)
				return
			}
		}
	}()

	return nil
}

func (km *KafkaSubscriberManager) processMessage(ctx context.Context, msg *message.Message, handler func(msg *message.Message), timeout time.Duration) {
	msgCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	done := make(chan struct{})
	go func() {
		handler(msg) //每个消息由单独的协程处理
		close(done)
	}()

	select {
	case <-done:
		msg.Ack()
	case <-msgCtx.Done():
		log.Printf("Message processing timed out or cancelled for topic: %s", msg.Metadata.Get("topic"))
		msg.Nack()
	}
}

// 使用示例（作为注释）：
/*
err := DefaultKafkaSubscriberManager.SubscribeToTopic("my-topic", func(msg *message.Message) {
    // 处理接收到的消息
    fmt.Printf("Received message: %s\n", string(msg.Payload))
}, 30*time.Second)
if err != nil {
    log.Printf("Failed to subscribe to topic: %v", err)
}
*/

/*

改进需求：
在我原有的代码基础上，确保不破坏原来代码的任何功能，实现：
（1）系统刚启动阶段处于恢复模式，由于要处理大量积压在kafka的消息，要确保同一个聚合id的所有消息（都属于同一个topic）都是严格按kafka发布顺序进行处理，即同一聚合ID的消息在同一个 goroutine 中按顺序处理。
（2）恢复模式下，聚合处理器采用固定大小资源池方式，当消息携带新的聚合id，则从资源池中分配一个空闲处理器;
（3）恢复模式下，当消息携带的聚合id已分配处理器，则消息送往该处理器处理；
（4）恢复模式下，当处理器资源池无资源可用时，新来的聚合id需要等待处理器资源池释放出空闲资源；
（5）恢复模式下，聚合id当前无消息需要处理时，处理器资源释放；
（6）恢复模式下，可以控制消息处理的速度，避免出现瞬间消息吞吐量过大，导致系统资源耗尽的情况；
（7）系统运行一段时间后，系统检测到积压的消息都已处理完，再切换为普通消费者模式，可以实现每个消息并行消费，提高性能
（8）从恢复模式到普通消费者模式的过度要平滑，在普通消费者模式下，如果某个聚合 ID 还有对应的处理器，消息仍会被送入该处理器进行顺序处理，当消息的聚合id查不到已占用聚合处理器，就立即处理；
（9）逐步的，聚合处理器资源池就全部空闲了，系统也就完全进入普通消费者模式了；
（10）考虑可靠性异常处理充分，特别注意func (km *KafkaSubscriberManager) processMessage的改动”
（11）尽量避免或少用锁，从而减少锁对性能的损耗
（12）请再看仔细检查，是否未破坏原有的代码，同时新增的代码稳定/可靠/优雅


*/

/* kafka知识


Kafka 的消息订阅和消费机制确实是按 topic 进行的，并且消息的顺序保证也是基于 topic 和分区（partition）的。
消费者订阅的是整个 topic 或 topic 的特定分区。
Kafka 只能保证同一 topic 的同一分区内的消息按发布顺序被消费。
每个分区内的消息都有一个唯一的递增序号（offset），确保了分区内的顺序。
如果一个 topic 有多个分区，不同分区之间的消息顺序不能保证。
消费者可能会并行处理来自不同分区的消息，这可能导致跨分区的消息处理顺序与发布顺序不一致。
如果你希望确保特定聚合 ID 的消息按顺序处理，你需要确保同一聚合 ID 的所有消息都发送到同一个分区。
这通常通过使用聚合 ID 作为分区键（partition key）来实现。
在使用消费者组时，每个分区只会被组内的一个消费者消费，这有助于维护分区内的顺序。
但是，如果消费者数量少于分区数量，一个消费者可能会消费多个分区，这时需要特别注意跨分区的消息顺序。

Kafka 的默认分区器使用消息的键来决定消息应该发送到哪个分区。
如果你使用聚合 ID 作为消息的键，默认分区器会自动将相同聚合 ID 的消息发送到同一个分区。

使用聚合 ID 作为消息键不会直接导致出现大量的分区。分区的数量是在创建 Kafka topic 时由你指定的，
并且是固定的。使用聚合 ID 作为消息键的主要作用是确保具有相同聚合 ID 的消息被路由到同一个分区，
以保证消息的顺序性。

创建 Kafka topic 通常是通过 Kafka 自身的工具或 Kafka 客户端库来完成的。
在 Kafka 中，topic 是消息发布和订阅的基本单位。通常情况下，Kafka 需要在发布消息之前预先创建 topic。
然而，Kafka 也支持自动创建 topic 的功能，但这需要在 Kafka broker 的配置中启用。
Kafka broker 的默认配置确实是允许在首次尝试发布消息到不存在的 topic 时自动创建该 topic。
auto.create.topics.enable：默认值：true
自动创建的 topic 会使用 Kafka broker 的默认分区数（通常是1）和副本因子（通常是1）。
这些默认值可以通过 num.partitions 和 default.replication.factor 配置项来设置。
生产环境建议：
    在生产环境中，通常建议显式创建 topic，以便更好地控制分区和副本配置。
	自动创建的 topic 可能不符合你的性能和可靠性需求。
*/


