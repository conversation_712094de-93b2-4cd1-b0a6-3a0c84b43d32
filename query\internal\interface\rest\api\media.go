package api

import (
	"context"
	"strconv"
	"time"

	_ "github.com/ChenBigdata421/jxt-core/sdk/pkg/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"jxt-evidence-system/evidence-management/shared/common/restapi"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/application/queryservice/port"
	"jxt-evidence-system/evidence-management/shared/common/di"
)

/*
    涉及到值类型和指针类型的选择。在这种情况下，通常推荐返回 *MediaApi（指针类型）而不是 MediaApi（值类型）。让我解释一下原因和好处：
返回 *MediaApi（指针类型）的优点：
    a. 效率：当结构体较大时，返回指针更高效，因为它避免了整个结构体的复制。
    b. 一致性：如果 MediaApi 有方法，使用指针接收器可以确保所有实例共享相同的方法集。
    c. 可变性：如果需要在 MediaApi 上进行修改操作，指针允许你直接修改原始对象。
    d. 空值表示：指针可以为 nil，这在某些情况下可能有用（例如，表示创建失败）。
    e. 接口兼容性：如果 MediaApi 实现了某个接口，使用指针可以确保方法集的一致性。
*/

func init() {
	registrations = append(registrations, registerMediaApiDependencies)
}

// jiyuanjie MediaApi的依赖注入
func registerMediaApiDependencies() {
	// 注册 MediaApi
	err := di.Provide(func(query port.MediaQuery) *MediaHandler {
		return &MediaHandler{
			queryService: query,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide MediaHandler: %v", err)
	}
}

type MediaHandler struct {
	restapi.RestApi
	queryService port.MediaQuery
}

// GetPage 通过查询条件获取Medie列表数据分页
// @Summary Medie列表数据
// @Description 获取JSON
// @Tags Media
// @Param mediaName query string false "mediaName"
// @Param mediaID query string false "mediaID"
// @Param mediaType query string false "dictType"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/media [get]
// @Security Bearer
func (e *MediaHandler) GetPage(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
	defer cancel()

	query := query.MediaPagedQuery{}

	err := c.ShouldBindQuery(&query)
	if err != nil {
		e.GetLogger(c).Error(err.Error())
		e.Error(c, 500, err, err.Error())
		return
	}
	e.GetLogger(c).Sugar().Infof("MediaPagedQuery: %+v", query)

	list, count, err := e.queryService.GetPage(ctx, &query)
	if err != nil {
		e.GetLogger(c).Error(err.Error())
		e.Error(c, 500, err, "查询失败")
		return
	}
	e.PageOK(c, list, int(count), query.GetPageIndex(), query.GetPageSize(), "查询成功")
}

// Get 通过媒体id获取媒体
// @Summary 通过媒体id获取媒体
// @Description 获取JSON
// @Tags 媒体
// @Param mediaId path int true "媒体id"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/media/{mediaId} [get]
// @Security Bearer
func (e *MediaHandler) GetByID(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
	defer cancel()

	// 获取有符号64位整数id
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)

	if err != nil {
		e.GetLogger(c).Error("bind MediaGetReq err", zap.Error(err))
		e.Error(c, 500, err, err.Error())
		return
	}

	model, err := e.queryService.GetByID(ctx, id)
	if err != nil {
		e.GetLogger(c).Error(err.Error())
		e.Error(c, 500, err, "查询失败")
		return
	}
	e.OK(c, model, "查询成功")
}
