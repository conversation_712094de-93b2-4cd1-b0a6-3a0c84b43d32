package router

import (
    "github.com/gin-gonic/gin"

    "go-admin/app/{{.PackageName}}/middleware"
    "go-admin/app/{{.PackageName}}/models"
    "go-admin/app/{{.PackageName}}/service/dto"
    "go-admin/common/actions"
)

func init()  {
	routerNoCheckRole = append(routerNoCheckRole, register{{.ClassName}}Router)
}

// 无需认证的路由代码
func register{{.ClassName}}Router(v1 *gin.RouterGroup) {
    r := v1.Group("/{{.ModuleName}}")
        {
            model := &models.{{.ClassName}}{}
            r.GET("", actions.IndexAction(model, new(dto.{{.ClassName}}Search), func() interface{} {
                list := make([]models.{{.ClassName}}, 0)
                return &list
            }))
            r.GET("/:id", actions.ViewAction(new(dto.{{.ClassName}}ById), nil))
            r.POST("", actions.CreateAction(new(dto.{{.ClassName}}Control)))
            r.PUT("/:id", actions.UpdateAction(new(dto.{{.ClassName}}Control)))
            r.DELETE("", actions.DeleteAction(new(dto.{{.ClassName}}ById)))
        }
}
