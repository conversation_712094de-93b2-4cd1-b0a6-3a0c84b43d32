// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: user.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 根据用户ID查询请求
type GetUserByIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      string                 `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"` // 租户ID，用于多租户系统
	UserId        int32                  `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`      // 用户唯一ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserByIdReq) Reset() {
	*x = GetUserByIdReq{}
	mi := &file_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByIdReq) ProtoMessage() {}

func (x *GetUserByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByIdReq.ProtoReflect.Descriptor instead.
func (*GetUserByIdReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{0}
}

func (x *GetUserByIdReq) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *GetUserByIdReq) GetUserId() int32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 根据警号查询请求
type GetUserByPoliceNoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      string                 `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"` // 租户ID，用于多租户系统
	PoliceNo      string                 `protobuf:"bytes,2,opt,name=police_no,json=policeNo,proto3" json:"police_no,omitempty"` // 警号
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserByPoliceNoReq) Reset() {
	*x = GetUserByPoliceNoReq{}
	mi := &file_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserByPoliceNoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByPoliceNoReq) ProtoMessage() {}

func (x *GetUserByPoliceNoReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByPoliceNoReq.ProtoReflect.Descriptor instead.
func (*GetUserByPoliceNoReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserByPoliceNoReq) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *GetUserByPoliceNoReq) GetPoliceNo() string {
	if x != nil {
		return x.PoliceNo
	}
	return ""
}

// 用户信息响应，字段与领域模型SysUser对应
type UserInfoReply struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	UserId    int32                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	UserName  string                 `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	PoliceNo  string                 `protobuf:"bytes,3,opt,name=police_no,json=policeNo,proto3" json:"police_no,omitempty"`
	Phone     string                 `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	RoleId    int32                  `protobuf:"varint,5,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	Avatar    string                 `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Sex       string                 `protobuf:"bytes,7,opt,name=sex,proto3" json:"sex,omitempty"`
	Email     string                 `protobuf:"bytes,8,opt,name=email,proto3" json:"email,omitempty"`
	OrgId     int32                  `protobuf:"varint,9,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	PostId    int32                  `protobuf:"varint,10,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Remark    string                 `protobuf:"bytes,11,opt,name=remark,proto3" json:"remark,omitempty"`
	Status    string                 `protobuf:"bytes,12,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt string                 `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt string                 `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// 关联信息
	Org           *OrgInfo  `protobuf:"bytes,15,opt,name=org,proto3" json:"org,omitempty"`
	Post          *PostInfo `protobuf:"bytes,16,opt,name=post,proto3" json:"post,omitempty"`
	Role          *RoleInfo `protobuf:"bytes,17,opt,name=role,proto3" json:"role,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfoReply) Reset() {
	*x = UserInfoReply{}
	mi := &file_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoReply) ProtoMessage() {}

func (x *UserInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoReply.ProtoReflect.Descriptor instead.
func (*UserInfoReply) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{2}
}

func (x *UserInfoReply) GetUserId() int32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserInfoReply) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *UserInfoReply) GetPoliceNo() string {
	if x != nil {
		return x.PoliceNo
	}
	return ""
}

func (x *UserInfoReply) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UserInfoReply) GetRoleId() int32 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *UserInfoReply) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserInfoReply) GetSex() string {
	if x != nil {
		return x.Sex
	}
	return ""
}

func (x *UserInfoReply) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInfoReply) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *UserInfoReply) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

func (x *UserInfoReply) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UserInfoReply) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UserInfoReply) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *UserInfoReply) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *UserInfoReply) GetOrg() *OrgInfo {
	if x != nil {
		return x.Org
	}
	return nil
}

func (x *UserInfoReply) GetPost() *PostInfo {
	if x != nil {
		return x.Post
	}
	return nil
}

func (x *UserInfoReply) GetRole() *RoleInfo {
	if x != nil {
		return x.Role
	}
	return nil
}

// 组织信息
type OrgInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	OrgName       string                 `protobuf:"bytes,2,opt,name=org_name,json=orgName,proto3" json:"org_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrgInfo) Reset() {
	*x = OrgInfo{}
	mi := &file_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrgInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrgInfo) ProtoMessage() {}

func (x *OrgInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrgInfo.ProtoReflect.Descriptor instead.
func (*OrgInfo) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{3}
}

func (x *OrgInfo) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *OrgInfo) GetOrgName() string {
	if x != nil {
		return x.OrgName
	}
	return ""
}

// 岗位信息
type PostInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PostId        int32                  `protobuf:"varint,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PostName      string                 `protobuf:"bytes,2,opt,name=post_name,json=postName,proto3" json:"post_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PostInfo) Reset() {
	*x = PostInfo{}
	mi := &file_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostInfo) ProtoMessage() {}

func (x *PostInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostInfo.ProtoReflect.Descriptor instead.
func (*PostInfo) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{4}
}

func (x *PostInfo) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

func (x *PostInfo) GetPostName() string {
	if x != nil {
		return x.PostName
	}
	return ""
}

// 角色信息
type RoleInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoleId        int32                  `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	RoleName      string                 `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoleInfo) Reset() {
	*x = RoleInfo{}
	mi := &file_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoleInfo) ProtoMessage() {}

func (x *RoleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoleInfo.ProtoReflect.Descriptor instead.
func (*RoleInfo) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{5}
}

func (x *RoleInfo) GetRoleId() int32 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *RoleInfo) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

var File_user_proto protoreflect.FileDescriptor

const file_user_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"user.proto\x12\x04user\"F\n" +
	"\x0eGetUserByIdReq\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\tR\btenantId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\x05R\x06userId\"P\n" +
	"\x14GetUserByPoliceNoReq\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\tR\btenantId\x12\x1b\n" +
	"\tpolice_no\x18\x02 \x01(\tR\bpoliceNo\"\xd8\x03\n" +
	"\rUserInfoReply\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x05R\x06userId\x12\x1b\n" +
	"\tuser_name\x18\x02 \x01(\tR\buserName\x12\x1b\n" +
	"\tpolice_no\x18\x03 \x01(\tR\bpoliceNo\x12\x14\n" +
	"\x05phone\x18\x04 \x01(\tR\x05phone\x12\x17\n" +
	"\arole_id\x18\x05 \x01(\x05R\x06roleId\x12\x16\n" +
	"\x06avatar\x18\x06 \x01(\tR\x06avatar\x12\x10\n" +
	"\x03sex\x18\a \x01(\tR\x03sex\x12\x14\n" +
	"\x05email\x18\b \x01(\tR\x05email\x12\x15\n" +
	"\x06org_id\x18\t \x01(\x05R\x05orgId\x12\x17\n" +
	"\apost_id\x18\n" +
	" \x01(\x05R\x06postId\x12\x16\n" +
	"\x06remark\x18\v \x01(\tR\x06remark\x12\x16\n" +
	"\x06status\x18\f \x01(\tR\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18\r \x01(\tR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x0e \x01(\tR\tupdatedAt\x12\x1f\n" +
	"\x03org\x18\x0f \x01(\v2\r.user.OrgInfoR\x03org\x12\"\n" +
	"\x04post\x18\x10 \x01(\v2\x0e.user.PostInfoR\x04post\x12\"\n" +
	"\x04role\x18\x11 \x01(\v2\x0e.user.RoleInfoR\x04role\";\n" +
	"\aOrgInfo\x12\x15\n" +
	"\x06org_id\x18\x01 \x01(\x05R\x05orgId\x12\x19\n" +
	"\borg_name\x18\x02 \x01(\tR\aorgName\"@\n" +
	"\bPostInfo\x12\x17\n" +
	"\apost_id\x18\x01 \x01(\x05R\x06postId\x12\x1b\n" +
	"\tpost_name\x18\x02 \x01(\tR\bpostName\"@\n" +
	"\bRoleInfo\x12\x17\n" +
	"\arole_id\x18\x01 \x01(\x05R\x06roleId\x12\x1b\n" +
	"\trole_name\x18\x02 \x01(\tR\broleName2\x95\x01\n" +
	"\x0fUserInfoService\x12:\n" +
	"\vGetUserById\x12\x14.user.GetUserByIdReq\x1a\x13.user.UserInfoReply\"\x00\x12F\n" +
	"\x11GetUserByPoliceNo\x12\x1a.user.GetUserByPoliceNoReq\x1a\x13.user.UserInfoReply\"\x00B\tZ\a.;protob\x06proto3"

var (
	file_user_proto_rawDescOnce sync.Once
	file_user_proto_rawDescData []byte
)

func file_user_proto_rawDescGZIP() []byte {
	file_user_proto_rawDescOnce.Do(func() {
		file_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_proto_rawDesc), len(file_user_proto_rawDesc)))
	})
	return file_user_proto_rawDescData
}

var file_user_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_user_proto_goTypes = []any{
	(*GetUserByIdReq)(nil),       // 0: user.GetUserByIdReq
	(*GetUserByPoliceNoReq)(nil), // 1: user.GetUserByPoliceNoReq
	(*UserInfoReply)(nil),        // 2: user.UserInfoReply
	(*OrgInfo)(nil),              // 3: user.OrgInfo
	(*PostInfo)(nil),             // 4: user.PostInfo
	(*RoleInfo)(nil),             // 5: user.RoleInfo
}
var file_user_proto_depIdxs = []int32{
	3, // 0: user.UserInfoReply.org:type_name -> user.OrgInfo
	4, // 1: user.UserInfoReply.post:type_name -> user.PostInfo
	5, // 2: user.UserInfoReply.role:type_name -> user.RoleInfo
	0, // 3: user.UserInfoService.GetUserById:input_type -> user.GetUserByIdReq
	1, // 4: user.UserInfoService.GetUserByPoliceNo:input_type -> user.GetUserByPoliceNoReq
	2, // 5: user.UserInfoService.GetUserById:output_type -> user.UserInfoReply
	2, // 6: user.UserInfoService.GetUserByPoliceNo:output_type -> user.UserInfoReply
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_user_proto_init() }
func file_user_proto_init() {
	if File_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_proto_rawDesc), len(file_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_proto_goTypes,
		DependencyIndexes: file_user_proto_depIdxs,
		MessageInfos:      file_user_proto_msgTypes,
	}.Build()
	File_user_proto = out.File
	file_user_proto_goTypes = nil
	file_user_proto_depIdxs = nil
}
