package eventbus

import (
	"context"
	"fmt"
	"jxt-evidence-system/evidence-management/command/internal/domain/event/publisher"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/eventbus"
	"jxt-evidence-system/evidence-management/shared/domain/event"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

// jiyuanjie 添加通过依赖注入创建service
func init() {
	registrations = append(registrations, registerKafkaEventPublisherDependencies)
}

// jiyuanjie GormMediaReadModelRepository的依赖注入
func registerKafkaEventPublisherDependencies() {
	if err := di.Provide(func() publisher.EventPublisher {
		return &KafkaEventPublisher{
			kafkaManager: eventbus.DefaultKafkaPublisherManager,
		}
	}); err != nil {
		logger.Fatalf("failed to provide KafkaEventPublisher: %v", err)
	}
}

// 实现publisher.EventPublisher接口
type KafkaEventPublisher struct {
	kafkaManager *eventbus.KafkaPublisherManager //由于 kafkaPublisherManager 在创建后不会改变，所以多个 goroutine 同时读取它是安全的。
}

// Publish 发布事件到 Kafka，正确使用 context, toptuc 就是  eventType
func (k *KafkaEventPublisher) Publish(ctx context.Context, topic string, event event.Event) error {
	if event == nil {
		return fmt.Errorf("event cannot be nil")
	}
	payload, err := event.MarshalJSON()
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}

	err = k.kafkaManager.PublishMessage(topic, event.GetEventID(), event.GetAggregateID(), payload)
	if err != nil {
		return fmt.Errorf("failed to publish message to Kafka: %w", err)
	}

	return nil
}

// RegisterReconnectCallback 注册一个在 Kafka 重连时调用的回调函数
func (k *KafkaEventPublisher) RegisterReconnectCallback(callback func(ctx context.Context) error) error {
	k.kafkaManager.RegisterReconnectCallback(callback)
	return nil
}

// jiyuanjie 在这里也可以实现基于其它组件例如NATS jetstream的eventpublisher
