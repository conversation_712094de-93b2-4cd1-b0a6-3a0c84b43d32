package repository

import (
	"context"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archive"
)

// ArchiveRepository defines the interface for Archive service
type ArchiveRepository interface {
	FindByID(ctx context.Context, id int64) (*archive.Archive, error)

	FindByCode(ctx context.Context, code string) (*archive.Archive, error)

	Create(ctx context.Context, model *archive.Archive) error

	// 只更新特定字段，即使字段值为 0，"", false 也会更新，且这样更新效率更高
	UpdateByID(ctx context.Context, id int64, updates map[string]interface{}) error

	DeleteByID(ctx context.Context, id int64) error

	// 批量更新
	BatchUpdateByIDs(ctx context.Context, ids []int64, updates map[string]interface{}) (rowsAffected int64, err error)

	// 批量删除
	BatchDeleteByIDs(ctx context.Context, ids []int64) (rowsAffected int64, err error)
}
