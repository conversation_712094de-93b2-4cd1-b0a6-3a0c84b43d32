package queryservice

import (
	"context"
	query "jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/application/queryservice/port"
	"jxt-evidence-system/evidence-management/query/internal/models"
	"jxt-evidence-system/evidence-management/query/internal/models/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"github.com/ChenBigdata421/jxt-core/sdk/service"
)

func init() {
	registrations = append(registrations, registerArchiveServiceDependencies)
}

func registerArchiveServiceDependencies() {
	err := di.Provide(func(repo repository.ArchiveReadModelRepository) port.ArchiveQuery {
		return &archiveQueryService{
			repo: repo,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide ArchiveService: %v", err)
	}
}

type archiveQueryService struct {
	service.Service
	repo repository.ArchiveReadModelRepository
}

// GetPage 获取档案分页列表
func (s *archiveQueryService) GetPage(ctx context.Context, r *query.ArchivePagedQuery) (*[]models.ArchiveReadModel, int64, error) {
	list, count, err := s.repo.GetPage(ctx, r)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

// GetByID 根据ID获取档案
func (s *archiveQueryService) GetByID(ctx context.Context, id int64) (*models.ArchiveReadModel, error) {
	return s.repo.FindByID(ctx, id)
}

// GetByCode 根据档案编码获取档案
func (s *archiveQueryService) GetByCode(ctx context.Context, code string) (*models.ArchiveReadModel, error) {
	return s.repo.FindByCode(ctx, code)
}
