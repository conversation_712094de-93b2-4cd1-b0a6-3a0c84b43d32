package eventhandler

import (
	"context"
	"fmt"
	"jxt-evidence-system/evidence-management/query/internal/models"
	"jxt-evidence-system/evidence-management/query/internal/models/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/global"
	"jxt-evidence-system/evidence-management/shared/domain/event"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"github.com/ThreeDotsLabs/watermill/message"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	registrations = append(registrations, registerEnforcementTypeEventHandlerDependencies)
}

func registerEnforcementTypeEventHandlerDependencies() {
	err := di.Provide(func(subscriber EventSubscriber, repo repository.EnforcementTypeReadModelRepository) *EnforcementTypeEventHandler {
		return NewEnforcementTypeEventHandler(subscriber, repo)
	})
	if err != nil {
		logger.Error("Failed to provide EnforcementTypeEventHandler", "error", err)
	}
}

type EnforcementTypeEventHandler struct {
	Subscriber EventSubscriber
	repo       repository.EnforcementTypeReadModelRepository
}

func NewEnforcementTypeEventHandler(subscriber EventSubscriber, repo repository.EnforcementTypeReadModelRepository) *EnforcementTypeEventHandler {
	return &EnforcementTypeEventHandler{
		Subscriber: subscriber,
		repo:       repo,
	}
}

func (h *EnforcementTypeEventHandler) ConsumeEvent(topic string) error {
	if err := h.Subscriber.Subscribe(topic, h.handleEnforcementTypeEvent, time.Second*30); err != nil {
		logger.Error("Failed to subscribe to topic", "topic", topic, "error", err)
		return err
	}

	return nil
}

func (h *EnforcementTypeEventHandler) handleEnforcementTypeEvent(msg *message.Message) error {
	// Step 1: 反序列化为领域事件结构体
	domainEvent := &event.DomainEvent{}

	err := domainEvent.UnmarshalJSON(msg.Payload)
	if err != nil {
		return fmt.Errorf("failed to unmarshal enforcement type event: %w", err)
	}

	// Step 2. 取出领域事件的租户id
	tenantID := domainEvent.GetTenantId()
	if tenantID == "" {
		return fmt.Errorf("租户ID不能为空")
	}

	// Step 3. 把租户id记录到context，传给repo
	ctx := context.WithValue(context.Background(), global.TenantIDKey, tenantID)

	// Step 4: 根据事件类型进行处理
	eventType := domainEvent.GetEventType()
	switch eventType {
	case event.EventTypeEnforcementTypeCreated:
		return h.handleEnforcementTypeCreatedEvent(ctx, domainEvent)
	case event.EventTypeEnforcementTypeUpdated:
		return h.handleEnforcementTypeUpdatedEvent(ctx, domainEvent)
	case event.EventTypeEnforcementTypeDeleted:
		return h.handleEnforcementTypeDeletedEvent(ctx, domainEvent)
	default:
		logger.Errorf("unknown enforcement type event type: %s", eventType)
		return fmt.Errorf("unknown enforcement type event type: %s", eventType)
	}
}

func (h *EnforcementTypeEventHandler) handleEnforcementTypeCreatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.EnforcementTypeCreatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling EnforcementTypeCreatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling EnforcementTypeCreatedPayload: %w", err)
	}

	logger.Info("Received EnforcementTypeCreatedEvent", "payload", payload)

	// 构建 EnforcementTypeReadModel
	enforcementTypeReadModel := &models.EnforcementTypeReadModel{
		ID:                  payload.EnforcementTypeID,
		EnforcementTypeCode: payload.EnforcementTypeCode,
		EnforcementTypeName: payload.EnforcementTypeName,
		EnforcementTypeDesc: payload.EnforcementTypeDesc,
		EnforcementTypePath: payload.EnforcementTypePath,
		ParentId:            payload.ParentID,
		Source:              payload.Source,
		Sort:                payload.Sort,
		ControlBy:           payload.ControlBy,
		ModelTime:           payload.ModelTime,
	}

	// 直接使用 repository 创建读模型
	err := h.repo.Create(ctx, enforcementTypeReadModel)
	if err != nil {
		logger.Error("创建执法类型读模型失败", "error", err)
		return err
	}

	return nil
}

func (h *EnforcementTypeEventHandler) handleEnforcementTypeUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.EnforcementTypeUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling EnforcementTypeUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling EnforcementTypeUpdatedPayload: %w", err)
	}

	logger.Info("Received EnforcementTypeUpdatedEvent", "payload", payload)

	// 直接使用 repository 更新读模型
	err := h.repo.Update(ctx, payload.EnforcementTypeID, payload.UpdatedFields)
	if err != nil {
		logger.Error("更新执法类型读模型失败", "error", err)
		return err
	}

	return nil
}

func (h *EnforcementTypeEventHandler) handleEnforcementTypeDeletedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.EnforcementTypeDeletedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling EnforcementTypeDeletedPayload", "error", err)
		return fmt.Errorf("error unmarshalling EnforcementTypeDeletedPayload: %w", err)
	}

	logger.Info("Received EnforcementTypeDeletedEvent", "payload", payload)

	// 直接使用 repository 删除读模型
	err := h.repo.Delete(ctx, payload.EnforcementTypeID)
	if err != nil {
		logger.Error("删除执法类型读模型失败", "error", err)
		return err
	}

	return nil
}
