package persistence

import (
	"context"
	"errors"
	"fmt"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archivemediarelation"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archivemediarelation/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
	"gorm.io/gorm"
)

// 添加通过依赖注入创建repository
func init() {
	registrations = append(registrations, registerArchiveMediaRelationRepoDependencies)
}

// GormArchiveMediaRelationRepository的依赖注入
func registerArchiveMediaRelationRepoDependencies() {
	if err := di.Provide(func() repository.ArchiveMediaRelationRepository {
		return &gormArchiveMediaRelationRepository{}
	}); err != nil {
		logger.Fatalf("failed to provide GormArchiveMediaRelationRepository: %v", err)
	}
}

type gormArchiveMediaRelationRepository struct {
	GormRepository
}

// FindByID 根据ID查找关联
func (repo *gormArchiveMediaRelationRepository) FindByID(ctx context.Context, id int64) (*archivemediarelation.ArchiveMediaRelation, error) {
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}

	var model archivemediarelation.ArchiveMediaRelation
	err = db.WithContext(ctx).First(&model, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 返回nil表示未找到，不是错误
		}
		return nil, err
	}

	return &model, nil
}

// FindByArchiveAndMedia 根据档案ID和媒体ID查找关联
func (repo *gormArchiveMediaRelationRepository) FindByArchiveAndMedia(ctx context.Context, archiveId, mediaId int64) (*archivemediarelation.ArchiveMediaRelation, error) {
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}

	var model archivemediarelation.ArchiveMediaRelation
	err = db.WithContext(ctx).Where("archive_id = ? AND media_id = ?", archiveId, mediaId).First(&model).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 返回nil表示未找到，不是错误
		}
		return nil, err
	}

	return &model, nil
}

// FindByArchiveId 根据档案ID查找所有关联
func (repo *gormArchiveMediaRelationRepository) FindByArchiveId(ctx context.Context, archiveId int64) ([]*archivemediarelation.ArchiveMediaRelation, error) {
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}

	var models []*archivemediarelation.ArchiveMediaRelation
	err = db.WithContext(ctx).Where("archive_id = ?", archiveId).Find(&models).Error
	if err != nil {
		return nil, err
	}

	return models, nil
}

// FindByMediaId 根据媒体ID查找所有关联
func (repo *gormArchiveMediaRelationRepository) FindByMediaId(ctx context.Context, mediaId int64) ([]*archivemediarelation.ArchiveMediaRelation, error) {
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}

	var models []*archivemediarelation.ArchiveMediaRelation
	err = db.WithContext(ctx).Where("media_id = ?", mediaId).Find(&models).Error
	if err != nil {
		return nil, err
	}

	return models, nil
}

// Create 创建关联
func (repo *gormArchiveMediaRelationRepository) Create(ctx context.Context, model *archivemediarelation.ArchiveMediaRelation) error {
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return err
	}

	// 检查关联关系是否已存在
	var count int64
	err = db.WithContext(ctx).Model(model).Where("archive_id = ? AND media_id = ?", model.ArchiveId, model.MediaId).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("档案[%d]与媒体[%d]的关联关系已经存在", model.ArchiveId, model.MediaId)
	}

	return db.WithContext(ctx).Create(model).Error
}

// DeleteByID 根据ID删除关联
func (repo *gormArchiveMediaRelationRepository) DeleteByID(ctx context.Context, id int64) error {
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return err
	}

	result := db.WithContext(ctx).Delete(&archivemediarelation.ArchiveMediaRelation{}, id)
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.New("要删除的关联不存在")
	}

	return nil
}

// BatchCreate 批量创建关联
func (repo *gormArchiveMediaRelationRepository) BatchCreate(ctx context.Context, models []*archivemediarelation.ArchiveMediaRelation) error {
	if len(models) == 0 {
		return nil
	}

	db, err := repo.GetOrm(ctx)
	if err != nil {
		return err
	}

	// 使用CreateInBatches批量创建，每批100条记录
	return db.WithContext(ctx).CreateInBatches(models, 100).Error
}

// BatchDeleteByIDs 批量删除关联
func (repo *gormArchiveMediaRelationRepository) BatchDeleteByIDs(ctx context.Context, ids []int64) (rowsAffected int64, err error) {
	if len(ids) == 0 {
		return 0, nil
	}

	db, err := repo.GetOrm(ctx)
	if err != nil {
		return 0, err
	}

	result := db.WithContext(ctx).Delete(&archivemediarelation.ArchiveMediaRelation{}, ids)
	if result.Error != nil {
		return 0, result.Error
	}

	if result.RowsAffected == 0 {
		return 0, errors.New("要删除的关联不存在")
	}

	return result.RowsAffected, nil
}

// DeleteByArchiveId 删除某档案的所有关联
func (repo *gormArchiveMediaRelationRepository) DeleteByArchiveId(ctx context.Context, archiveId int64) (rowsAffected int64, err error) {
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return 0, err
	}

	result := db.WithContext(ctx).Where("archive_id = ?", archiveId).Delete(&archivemediarelation.ArchiveMediaRelation{})
	return result.RowsAffected, result.Error
}

// DeleteByMediaId 删除某媒体的所有关联
func (repo *gormArchiveMediaRelationRepository) DeleteByMediaId(ctx context.Context, mediaId int64) (rowsAffected int64, err error) {
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return 0, err
	}

	result := db.WithContext(ctx).Where("media_id = ?", mediaId).Delete(&archivemediarelation.ArchiveMediaRelation{})
	return result.RowsAffected, result.Error
}
