package api

import (
	application "jxt-evidence-system/evidence-management/command/internal/application/service"
	domain_service "jxt-evidence-system/evidence-management/command/internal/domain/service"
	"jxt-evidence-system/evidence-management/command/internal/infrastructure/eventbus"
	grpcclient "jxt-evidence-system/evidence-management/command/internal/infrastructure/grpc_client"
	persistence "jxt-evidence-system/evidence-management/command/internal/infrastructure/persistence/gorm"
	"jxt-evidence-system/evidence-management/command/internal/infrastructure/recovery"
	"jxt-evidence-system/evidence-management/command/internal/interface/rest/api"
	"jxt-evidence-system/evidence-management/command/internal/interface/rest/router"
)

// jiyuanjie 注意import目录为正确的六边形架构的目录

func init() {
	//注册路由 fixme 其他应用的路由，在本目录新建文件放在init方法
	AppRouters = append(AppRouters, router.InitRouter)

	// jiyuanjie 添加依赖注入
	Registrations = append(Registrations, persistence.RegisterDependencies)
	Registrations = append(Registrations, domain_service.RegisterDependencies)
	Registrations = append(Registrations, grpcclient.RegisterDependencies)
	Registrations = append(Registrations, application.RegisterDependencies)
	Registrations = append(Registrations, api.RegisterDependencies)
	Registrations = append(Registrations, eventbus.RegisterDependencies)
	Registrations = append(Registrations, recovery.RegisterDependencies)

}
