package port

import (
	"context"
	"jxt-evidence-system/evidence-management/command/internal/application/command"
)

type EnforcementTypeService interface {
	// Create 创建执法类型
	CreateEnforcementType(ctx context.Context, r *command.CreateEnforcementTypeCommand) error
	// Update 更新执法类型
	UpdateEnforcementTypeByID(ctx context.Context, r *command.UpdateEnforcementTypeCommand) error
	// Delete 删除执法类型
	DeleteEnforcementTypeByID(ctx context.Context, id int64) error
	// BatchDelete 批量删除执法类型,如果存在子节点，则不允许删除，由于删除逻辑复杂，不提供批量删除接口
}
