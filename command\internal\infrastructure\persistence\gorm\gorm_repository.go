package persistence

import (
	"context"
	"errors"
	"jxt-evidence-system/evidence-management/shared/common/global"

	"github.com/ChenBigdata421/jxt-core/sdk"
	"gorm.io/gorm"
)

type GormRepository struct{}

// GetOrm 获取上下文提供的Orm DB
func (e *GormRepository) GetOrm(ctx context.Context) (*gorm.DB, error) {

	// 从上下文中获取tenantID
	tenantID, ok := ctx.Value(global.TenantIDKey).(string)
	if !ok {
		return nil, errors.New("tenant id not exist")
	}

	// 根据tenantID获取数据库连接
	db := sdk.Runtime.GetTenantCommandDB(tenantID)
	if db == nil {
		return nil, errors.New("db connect not exist for tenant: " + tenantID)
	}
	return db.WithContext(ctx), nil
}
