PROJECT := evidence-management

# 定义一个变量来存储可执行文件的名称  
EXEC := query-migrate  

# 定义一个变量来存储配置文件的路径  
CONFIG := config/settings.migrate.yml  

# 设置 GOPROXY 为阿里云代理  
 export GOPROXY=https://mirrors.aliyun.com/goproxy/,direct 
# 清华大学镜像站
#https://mirrors.tuna.tsinghua.edu.cn/goproxy/
# 中国科技大学镜像站
#https://mirrors.ustc.edu.cn/goproxy/
# 一个由社区维护的 Go 代理服务
 https://goproxy.cn
# https://goproxy.io



.PHONY: b
b:
	CGO_ENABLED=0 go build -ldflags="-w -s" -a -installsuffix "" -o $(EXEC) ./cmd
#	go build -o $(EXEC) ./cmd

# 定义 migrate 目标  
m:  
	./$(EXEC) migrate -c $(CONFIG)  

