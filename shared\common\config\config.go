package config

// Settings 顶层配置结构
type Settings struct {
	Application Application         `mapstructure:"application"`
	Logger      Logger              `mapstructure:"logger"`
	SSL         SSL                 `mapstructure:"ssl"`
	JWT         JWT                 `mapstructure:"jwt"`
	Database    Database            `mapstructure:"database"`
	Databases   map[string]Database `mapstructure:"databases"`
	Cache       Cache               `mapstructure:"cache"`
	Queue       Queue               `mapstructure:"queue"`
	EventBus    EventBus            `mapstructure:"eventBus"`
	Locker      Locker              `mapstructure:"locker"`
	Extend      Extend              `mapstructure:"extend"`
}
