package repository

import (
	"context"
	"jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/models"
)

// EnforcementTypeReadModelRepository 定义执法类型读模型仓储接口
type EnforcementTypeReadModelRepository interface {
	// 分页查询执法类型，为了实时性，执法类型是直接查询命令数据库，所以直接用执法类型的领域模型来查询
	GetPage(ctx context.Context, r *query.EnforcementTypePagedQuery) ([]*models.EnforcementType, int64, error)

	// GetAll 获取所有执法类型，为了实时性，执法类型是直接查询命令数据库，所以直接用执法类型的领域模型来查询
	GetAll(ctx context.Context) ([]*models.EnforcementType, int64, error)

	// 查询操作，为了实时性，执法类型是直接查询命令数据库，所以直接用执法类型的领域模型来查询
	FindByID(ctx context.Context, id interface{}) (*models.EnforcementType, error)
	FindByCode(ctx context.Context, code string) (*models.EnforcementType, error)

	List(ctx context.Context) ([]*models.EnforcementType, error)

	ListByParentID(ctx context.Context, parentID interface{}) ([]*models.EnforcementType, error)

	// Create 创建执法类型读模型
	Create(ctx context.Context, model *models.EnforcementTypeReadModel) error

	// Update 更新执法类型读模型
	Update(ctx context.Context, id interface{}, updates map[string]interface{}) error

	// Delete 删除执法类型读模型
	Delete(ctx context.Context, id interface{}) error
}
