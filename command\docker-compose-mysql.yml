version: '3.8'
services:
  mysql:
    image: mariadb:latest
    container_name: mysql3
    environment:
      MYSQL_ROOT_PASSWORD: 123456
    ports:
      - 3306:3306
    networks:
      - myweb
  go-admin-api:
    container_name: go-admin-api3
    image: go-admin-api:latest
    privileged: true
    restart: always
    ports:
      - 8000:8000
    volumes:
      - ./config/:/go-admin-api/config/
      - ./static/:/go-admin-api/static/
      - ./temp/:/go-admin-api/temp/
    networks:
      - myweb

networks:
  myweb:
    driver: bridge
