package api

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/application/queryservice/port"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/restapi"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

func init() {
	registrations = append(registrations, registerEnforcementTypeApiDependencies)
}

func registerEnforcementTypeApiDependencies() {
	err := di.Provide(func(service port.EnforcementTypeService) *EnforcementTypeHandler {
		return &EnforcementTypeHandler{
			enforcementTypeService: service,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide EnforcementTypeHandler: %v", err)
	}
}

type EnforcementTypeHandler struct {
	restapi.RestApi
	enforcementTypeService port.EnforcementTypeService
}

// GetByID 根据ID获取执法类型
// @Summary 根据ID获取执法类型
// @Description 根据ID获取执法类型
// @Tags 执法类型管理
// @Accept json
// @Produce json
// @Param id path int true "执法类型ID"
// @Success 200 {object} api.Response{data=dto.EnforcementTypeDTO}
// @Router /api/v1/enforce-types/{id} [get]
func (e *EnforcementTypeHandler) EnforcementTypeGetByID(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	// 获取有符号64位整数id
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		e.GetLogger(c).Error("invalid id", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, "无效的ID")
		return
	}

	enforceType, err := e.enforcementTypeService.EnforcementTypeGetByID(ctx, id)
	if err != nil {
		e.GetLogger(c).Error("Get EnforceType failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, "获取执法类型失败")
		return
	}

	e.OK(c, enforceType, "获取执法类型成功")
}

// List 获取执法类型列表
// @Summary 获取执法类型列表
// @Description 获取执法类型列表
// @Tags 执法类型管理
// @Accept json
// @Produce json
// @Success 200 {object} api.Response{data=[]dto.EnforcementTypeDTO}
// @Router /api/v1/enforce-types [get]
func (e *EnforcementTypeHandler) EnforcementTypeGetPage(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	query := query.EnforcementTypePagedQuery{}

	err := c.ShouldBindQuery(&query)
	if err != nil {
		e.GetLogger(c).Error(err.Error())
		e.Error(c, 500, err, err.Error())
		return
	}
	e.GetLogger(c).Sugar().Infof("EnforcementTypePagedQuery: %+v", query)

	list, count, err := e.enforcementTypeService.EnforcementTypeGetPage(ctx, &query)
	if err != nil {
		e.GetLogger(c).Error("List EnforcementType failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, "获取执法类型列表失败")
		return
	}

	e.PageOK(c, list, int(count), query.GetPageIndex(), query.GetPageSize(), "查询成功")
}

// ListTree 获取执法类型树形结构
// @Summary 获取执法类型树形结构
// @Description 获取执法类型树形结构
// @Tags 执法类型管理
// @Accept json
// @Produce json
// @Success 200 {object} api.Response{data=[]dto.EnforcementTypeDTO}
// @Router /api/v1/enforce-types/tree [get]
func (e *EnforcementTypeHandler) EnforcementTypeListTree(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	tree, err := e.enforcementTypeService.EnforcementTypeListTree(ctx)
	if err != nil {
		e.GetLogger(c).Error("ListTree EnforcementType failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, "获取执法类型树形结构失败")
		return
	}

	e.OK(c, tree, "获取执法类型树形结构成功")
}
