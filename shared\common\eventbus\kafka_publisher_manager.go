package eventbus

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg"
	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill-kafka/v2/pkg/kafka"
	"github.com/ThreeDotsLabs/watermill/message"
)

var (
	DefaultKafkaPublisherManager *KafkaPublisherManager
)

// reconnectCallback 存储注册的回调函数
type reconnectCallback func(ctx context.Context) error

type KafkaPublisherManagerConfig struct {
	KafkaPublisherConfig kafka.PublisherConfig
	HealthCheckTopic     string
	HealthCheckInterval  time.Duration
	MaxReconnectAttempts int
	MaxBackoff           time.Duration
}

func DefaultKafkaPublisherManagerConfig() KafkaPublisherManagerConfig {
	return KafkaPublisherManagerConfig{
		HealthCheckTopic:     HealthCheckTopic,
		HealthCheckInterval:  2 * time.Minute,
		MaxReconnectAttempts: 5,
		MaxBackoff:           time.Minute,
	}
}

type KafkaPublisherManager struct {
	Config            KafkaPublisherManagerConfig
	publisher         atomic.Value // *kafka.Publisher 不使用互斥锁的情况下安全地读取和更新 publisher
	logger            watermill.LoggerAdapter
	wg                sync.WaitGroup
	rootCtx           context.Context
	rootCancel        context.CancelFunc
	backoff           time.Duration
	reconnectCallback reconnectCallback
	failureCount      int // 新增: 记录连续失败次数
}

func NewKafkaPublisherManager(config KafkaPublisherManagerConfig) (*KafkaPublisherManager, error) {
	if len(config.KafkaPublisherConfig.Brokers) == 0 {
		return nil, fmt.Errorf("no Kafka brokers specified in config")
	}
	if config.HealthCheckInterval <= 0 {
		return nil, fmt.Errorf("invalid health check interval")
	}
	return &KafkaPublisherManager{
		Config:       config,
		logger:       watermill.NewStdLogger(false, false),
		backoff:      time.Second,
		failureCount: 0, // 初始化失败计数
	}, nil
}

func (km *KafkaPublisherManager) Start() error {
	km.rootCtx, km.rootCancel = context.WithCancel(context.Background())

	if err := km.initPublisher(); err != nil {
		return err
	}

	km.wg.Add(1)
	go km.healthCheckLoop(km.rootCtx) // 只需要一个一直运行的健康检查，确保它不会退出
	return nil
}

func (km *KafkaPublisherManager) initPublisher() error {
	// 关闭旧的 Publisher
	if oldPublisher := km.publisher.Load(); oldPublisher != nil {
		ctx, cancel := context.WithTimeout(km.rootCtx, 2*time.Minute)
		defer cancel()

		done := make(chan struct{})
		go func() {
			oldPublisher.(*kafka.Publisher).Close()
			close(done)
		}()

		select {
		case <-done:
			log.Println(pkg.Green("Publisher closed successfully"))
		case <-ctx.Done():
			log.Println(pkg.Yellow("Close operation timed out, may need manual intervention"))
			// 可能需要强制关闭或其他清理操作
		}
	}
	// 创建新的 Publisher
	publisher, err := kafka.NewPublisher(km.Config.KafkaPublisherConfig, km.logger)
	if err != nil {
		return err
	}
	km.publisher.Store(publisher)
	return nil
}

func (km *KafkaPublisherManager) healthCheckLoop(ctx context.Context) {
	defer km.wg.Done()
	ticker := time.NewTicker(km.Config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			log.Println(pkg.Red("Context cancelled, stopping health check"))
			return
		case <-ticker.C:
			if err := km.checkAndReconnect(); err != nil {
				log.Printf(pkg.Red("Kafka Health check failed: %v\n"), err)
			}
		}
	}
}

func (km *KafkaPublisherManager) checkAndReconnect() error {
	err := km.healthCheck()
	if err == nil {
		log.Printf(pkg.Green("Kafka publisher connection healthy! topic: %s"), km.Config.HealthCheckTopic)
		km.failureCount = 0 // 重置失败计数
		return nil
	}

	km.failureCount++ // 增加失败计数
	log.Printf(pkg.Yellow("Kafka health check failed: %v Failure count: %d\n"), err, km.failureCount)

	if km.failureCount >= 3 { // 连续三次及以上发布失败才进行重连
		log.Printf(pkg.Yellow("%d consecutive failures detected. Attempting to reconnect...\n"), km.failureCount)
		reconnectErr := km.reconnect()
		if reconnectErr != nil {
			log.Printf(pkg.Red("Kafka publisher reconnection failed: %v\n"), reconnectErr)
			return reconnectErr
		}

		log.Println(pkg.Green("Kafka publisher reconnection successful"))
		km.failureCount = 0        // 重连成功后重置失败计数
		km.callReconnectCallback() // 在成功创建新的发布者后调用回调函数
	}

	return nil
}

func (km *KafkaPublisherManager) healthCheck() error {
	publisher, ok := km.publisher.Load().(*kafka.Publisher)
	if !ok || publisher == nil {
		return fmt.Errorf("invalid publisher type or nil publisher")
	}

	msg := message.NewMessage(watermill.NewUUID(), []byte("health check"))

	err := publisher.Publish(km.Config.HealthCheckTopic, msg)
	if err != nil {
		return fmt.Errorf("publish test message failed: %w", err)
	}

	return nil
}

// 使用指数退避算法重新建立连接
func (km *KafkaPublisherManager) reconnect() error {
	initialBackoff := km.backoff // 保存初始退避时间
	totalWaitTime := time.Duration(0)

	for i := 0; i < km.Config.MaxReconnectAttempts; i++ {
		if err := km.initPublisher(); err == nil {
			km.backoff = initialBackoff // 重置退避时间
			return nil
		}

		//log.Printf("Reconnection attempt %d failed. Waiting %v before next attempt", i+1, km.backoff)
		time.Sleep(km.backoff)
		totalWaitTime += km.backoff

		km.backoff *= 2
		if km.backoff > km.Config.MaxBackoff {
			km.backoff = km.Config.MaxBackoff
		}
	}

	km.backoff = initialBackoff // 重置退避时间
	return fmt.Errorf("failed to reconnect after %d attempts over %v", km.Config.MaxReconnectAttempts, totalWaitTime)
}

func (km *KafkaPublisherManager) Shutdown(ctx context.Context) error {
	log.Println(pkg.Green("Shutting down Kafka manager..."))

	km.rootCancel()

	done := make(chan struct{})
	go func() {
		km.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Println(pkg.Green("All operations stopped"))
	case <-ctx.Done():
		return ctx.Err()
	}

	publisher := km.publisher.Load().(*kafka.Publisher)
	if publisher != nil {
		if err := publisher.Close(); err != nil {
			log.Printf(pkg.Red("Error closing Kafka publisher: %v"), err)
			return err
		}
	}

	log.Println(pkg.Green("Kafka manager shutdown completed"))
	return nil
}

func (km *KafkaPublisherManager) PublishMessage(topic string, uuid string, aggregateID interface{}, payload []byte) error {
	publisher, ok := km.publisher.Load().(*kafka.Publisher)
	if !ok || publisher == nil {
		return fmt.Errorf("invalid publisher type or nil publisher")
	}

	aggID := ""
	if id, ok := aggregateID.(int64); ok {
		aggID = strconv.FormatInt(id, 10) //整数转成字符串
	}

	msg := message.NewMessage(uuid, payload)
	msg.Metadata.Set("aggregate_id", aggID)
	return publisher.Publish(topic, msg)
	/*
		经过查阅 Watermill 的文档和源代码，我可以确认以下信息：
		Watermill 的 Kafka 发布器确实内置了重试机制。
		这个重试机制是通过 Sarama 库（Watermill 使用的 Kafka 客户端库）的配置来实现的。
		默认情况下，Sarama 配置了以下重试参数：
		Producer.Retry.Max: 默认值为 3，表示最大重试次数。
		Producer.Retry.Backoff: 默认值为 100ms，表示重试之间的等待时间。
	*/
}

// RegisterStartAndReconnectCallback 注册一个在 Kafka 重连成功时调用的回调函数
func (km *KafkaPublisherManager) RegisterReconnectCallback(callback func(ctx context.Context) error) {
	km.reconnectCallback = callback
}

// 在重连成功后调用注册的回调函数
func (km *KafkaPublisherManager) callReconnectCallback() {
	if km.reconnectCallback != nil {
		km.reconnectCallback(km.rootCtx)
	}
}
