package tests

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	commandTesthelpers "jxt-evidence-system/evidence-management/command/testhelpers"
	"jxt-evidence-system/evidence-management/tests/testhelpers"
)

func TestMediaApiOptimized(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "MediaApiOptimized Suite")
}

var _ = Describe("MediaApiOptimized", func() {
	var suite *testhelpers.TestSuite
	var mediaHelper *testhelpers.MediaTestHelper

	BeforeEach(func() {
		suite = testhelpers.SetupSuite(baseURL)
		suite.SetToken(token)
		mediaHelper = testhelpers.NewMediaTestHelper(dbCommand, dbQuery, suite)
	})

	AfterEach(func() {
		// 清理测试数据
		mediaHelper.CleanupTestData()
	})

	Describe("Insert", func() {
		It("成功创建一个新的媒体", func() {
			// 使用测试助手创建媒体
			mediaID, err := mediaHelper.CreateTestMedia("新增媒体")
			Expect(err).NotTo(HaveOccurred())
			Expect(mediaID).To(BeNumerically(">", 0))

			// 验证数据库记录存在性
			mediaHelper.VerifyMediaInCommandDB(mediaID)
			mediaHelper.VerifyMediaInQueryDB(mediaID)

			fmt.Printf("✅ 成功创建媒体 ID: %d\n", mediaID)
		})

		It("当尝试插入一个已存在的媒体时应该返回错误", func() {
			// 首先创建一个媒体，然后尝试创建重复的媒体名称
			existingMediaID, err := mediaHelper.CreateTestMedia("已存在的媒体")
			Expect(err).NotTo(HaveOccurred())
			Expect(existingMediaID).To(BeNumerically(">", 0))

			// 获取刚创建媒体的名称，用于重复测试
			existingMedia := commandTesthelpers.NewMediaBuilder().Build()
			err = dbCommand.Where("media_id = ?", existingMediaID).First(&existingMedia).Error
			Expect(err).NotTo(HaveOccurred())

			// 尝试创建一个与已存在媒体名称相同的新媒体
			uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
				WithMediaName(existingMedia.MediaName).
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithImportTime(&time.Time{}).
				Build()

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证错误响应
			mediaHelper.VerifyErrorResponse(resp, "已经存在")
		})
	})

	Describe("Update", func() {
		Context("单个媒体更新操作", func() {
			var createdMediaID int64

			BeforeEach(func() {
				var err error
				createdMediaID, err = mediaHelper.CreateTestMedia("待更新的媒体")
				Expect(err).NotTo(HaveOccurred())
			})

			It("应该成功更新指定的媒体", func() {
				// 创建更新命令
				updateMediaCommand := commandTesthelpers.NewUpdateMediaCommandBuilder().
					WithImportantLevel(3).
					WithComments("更新后的测试媒体").
					Build()

				// 发送更新请求
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "PUT", fmt.Sprintf("/api/v1/media/%d", createdMediaID), updateMediaCommand, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 验证更新响应
				mediaHelper.VerifyUpdateMediaResponse(resp)

				// 等待领域事件处理
				mediaHelper.WaitForEventProcessing()

				// 验证媒体属性更新
				mediaHelper.VerifyMediaProperty(createdMediaID, "ImportantLevel", 3)
				mediaHelper.VerifyMediaProperty(createdMediaID, "Comments", *updateMediaCommand.Comments)

				// 通过API验证更新
				resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", createdMediaID), nil, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				data := mediaHelper.VerifyGetMediaResponse(resp)
				Expect(data["importantLevel"]).To(Equal(float64(3)))
				Expect(data["comments"]).To(Equal(*updateMediaCommand.Comments))
			})

			It("当更新不存在的媒体时应该返回错误", func() {
				nonExistentID := int64(99999)
				updateCommand := commandTesthelpers.NewUpdateMediaCommandBuilder().
					WithComments("更新不存在的Media").
					Build()

				// 发送更新请求
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "PUT", fmt.Sprintf("/api/v1/media/%d", nonExistentID), updateCommand, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 验证错误响应
				mediaHelper.VerifyErrorResponse(resp, "更新媒体失败")
			})
		})
	})

	Describe("BatchUpdateMedia", func() {
		It("成功批量更新媒体信息", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand := time.Now().UnixNano() % 1000000
			randNum := 100000 + int(rand%900000)

			// 使用助手创建3个媒体 - 学习archive_media_relation_api_test.go的优雅方式
			mediaIDs := make([]int64, 3)
			for i := 0; i < 3; i++ {
				mediaName := fmt.Sprintf("批量更新测试媒体_%06d_%d", randNum, i+1)
				mediaID, err := mediaHelper.CreateTestMedia(mediaName)
				Expect(err).NotTo(HaveOccurred())
				mediaIDs[i] = mediaID
			}

			// 创建批量更新媒体命令
			newImportantLevel := 3
			newComments := "批量更新后的标注内容"
			newExpiryTime := time.Now().AddDate(1, 0, 0) // 一年后过期

			batchUpdateCommand := commandTesthelpers.NewBatchUpdateMediaCommandBuilder().
				WithIDs(mediaIDs).
				WithImportantLevel(newImportantLevel).
				WithComments(newComments).
				WithExpiryTime(newExpiryTime).
				Build()

			// 发送批量更新媒体请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "PUT", "/api/v1/media/batch", batchUpdateCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证批量更新响应
			mediaHelper.VerifyBatchUpdateMediaResponse(resp)

			// 等待领域事件处理
			mediaHelper.WaitForEventProcessing()

			// 验证媒体信息已更新
			for _, id := range mediaIDs {
				mediaHelper.VerifyMediaProperty(id, "ImportantLevel", newImportantLevel)
				mediaHelper.VerifyMediaProperty(id, "Comments", newComments)
			}

			fmt.Printf("✅ 成功批量更新媒体: MediaIDs=%v\n", mediaIDs)
		})

		It("当尝试批量更新不存在媒体时应该返回错误", func() {
			// 创建批量更新媒体命令，使用不存在的ID
			batchUpdateCommand := commandTesthelpers.NewBatchUpdateMediaCommandBuilder().
				WithIDs([]int64{99997, 99998, 99999}).
				WithImportantLevel(2).
				WithComments("更新不存在的媒体").
				Build()

			// 发送批量更新媒体请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "PUT", "/api/v1/media/batch", batchUpdateCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证错误响应
			mediaHelper.VerifyErrorResponse(resp, "没有可更新的媒体")
		})
	})

	Describe("MarkNonEnforcementMediaStatus", func() {
		Context("标记非执法媒体状态", func() {
			var createdMediaID int64

			BeforeEach(func() {
				var err error
				createdMediaID, err = mediaHelper.CreateTestMedia("被标注媒体")
				Expect(err).NotTo(HaveOccurred())
			})

			It("应该成功标注非执法媒体", func() {
				// 创建标记命令
				markCommand := commandTesthelpers.NewBatchUpdateNonEnforcementStatusCommandBuilder().
					WithIDs([]int64{createdMediaID}).
					WithIsNonEnforcementMedia(1).
					Build()

				// 发送标记请求
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/mark-no-enforcement-media-status", markCommand, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 验证标记响应
				mediaHelper.VerifyMarkNonEnforcementResponse(resp)

				// 等待领域事件处理
				mediaHelper.WaitForEventProcessing()

				// 验证媒体属性更新
				mediaHelper.VerifyMediaProperty(createdMediaID, "IsNonEnforcementMedia", markCommand.IsNonEnforcementMedia)
			})

			It("当标记不存在的媒体时应该返回错误", func() {
				markCommand := commandTesthelpers.NewBatchUpdateNonEnforcementStatusCommandBuilder().
					WithIDs([]int64{99999}).
					WithIsNonEnforcementMedia(0).
					Build()

				// 发送标记请求
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/mark-no-enforcement-media-status", markCommand, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 验证错误响应
				mediaHelper.VerifyErrorResponse(resp, "没有可设置非执法媒体状态的媒体")
			})
		})
	})

	Describe("BatchUpdateEnforceType", func() {
		It("成功批量更新执法类型", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand := time.Now().UnixNano() % 1000000
			randNum := 200000 + int(rand%900000)

			// 使用助手创建3个媒体 - 学习archive_media_relation_api_test.go的优雅方式
			mediaIDs := make([]int64, 3)
			for i := 0; i < 3; i++ {
				mediaName := fmt.Sprintf("执法类型测试媒体_%06d_%d", randNum, i+1)
				mediaID, err := mediaHelper.CreateTestMedia(mediaName)
				Expect(err).NotTo(HaveOccurred())
				mediaIDs[i] = mediaID
			}

			// 创建批量更新执法类型命令
			updateEnforceTypeCommand := commandTesthelpers.NewBatchUpdateEnforceTypeCommandBuilder().
				WithIDs(mediaIDs).
				WithEnforceType(2).
				Build()

			// 发送批量更新执法类型请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-enforce-type", updateEnforceTypeCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证批量更新执法类型响应
			mediaHelper.VerifyBatchUpdateEnforceTypeResponse(resp)

			// 等待领域事件处理
			mediaHelper.WaitForEventProcessing()

			// 验证媒体执法类型已更新
			for _, id := range mediaIDs {
				mediaHelper.VerifyMediaProperty(id, "EnforceType", updateEnforceTypeCommand.EnforceType)
			}

			fmt.Printf("✅ 成功批量更新执法类型: MediaIDs=%v\n", mediaIDs)
		})

		It("当尝试批量更新不存在媒体的执法类型时应该返回错误", func() {
			updateEnforceTypeCommand := commandTesthelpers.NewBatchUpdateEnforceTypeCommandBuilder().
				WithIDs([]int64{99997, 99998, 99999}).
				WithEnforceType(3).
				Build()

			// 发送批量更新执法类型请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-enforce-type", updateEnforceTypeCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证错误响应
			mediaHelper.VerifyErrorResponse(resp, "没有可更新执法类型的媒体")
		})
	})

	Describe("BatchUpdateIsLocked", func() {
		It("成功批量更新锁定状态", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand := time.Now().UnixNano() % 1000000
			randNum := 300000 + int(rand%900000)

			// 使用助手创建3个媒体 - 学习archive_media_relation_api_test.go的优雅方式
			mediaIDs := make([]int64, 3)
			for i := 0; i < 3; i++ {
				mediaName := fmt.Sprintf("锁定状态测试媒体_%06d_%d", randNum, i+1)
				mediaID, err := mediaHelper.CreateTestMedia(mediaName)
				Expect(err).NotTo(HaveOccurred())
				mediaIDs[i] = mediaID
			}

			// 创建批量更新锁定状态命令
			updateIsLockedCommand := commandTesthelpers.NewBatchUpdateIsLockedCommandBuilder().
				WithIDs(mediaIDs).
				WithIsLocked(1).
				Build()

			// 发送批量更新锁定状态请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-is-locked", updateIsLockedCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证批量更新锁定状态响应
			mediaHelper.VerifyBatchUpdateIsLockedResponse(resp)

			// 等待领域事件处理
			mediaHelper.WaitForEventProcessing()

			// 验证媒体锁定状态已更新
			for _, id := range mediaIDs {
				mediaHelper.VerifyMediaProperty(id, "IsLocked", updateIsLockedCommand.IsLocked)
			}

			fmt.Printf("✅ 成功批量更新锁定状态: MediaIDs=%v\n", mediaIDs)
		})

		It("当尝试批量更新不存在媒体的锁定状态时应该返回错误", func() {
			updateIsLockedCommand := commandTesthelpers.NewBatchUpdateIsLockedCommandBuilder().
				WithIDs([]int64{99997, 99998, 99999}).
				WithIsLocked(1).
				Build()

			// 发送批量更新锁定状态请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-is-locked", updateIsLockedCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证错误响应
			mediaHelper.VerifyErrorResponse(resp, "没有可更新锁定状态的媒体")
		})

		Context("锁定状态切换操作", func() {
			var createdMediaID int64

			BeforeEach(func() {
				var err error
				createdMediaID, err = mediaHelper.CreateTestMedia("锁定状态测试媒体")
				Expect(err).NotTo(HaveOccurred())
			})

			It("应该成功将媒体从解锁状态锁定", func() {
				// 锁定媒体
				lockCommand := commandTesthelpers.NewBatchUpdateIsLockedCommandBuilder().
					WithIDs([]int64{createdMediaID}).
					WithIsLocked(1).
					Build()

				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-is-locked", lockCommand, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				mediaHelper.VerifyBatchUpdateIsLockedResponse(resp)
				mediaHelper.WaitForEventProcessing()

				// 验证锁定状态
				mediaHelper.VerifyMediaProperty(createdMediaID, "IsLocked", 1)
			})

			It("应该成功将媒体从锁定状态解锁", func() {
				// 先锁定媒体
				lockCommand := commandTesthelpers.NewBatchUpdateIsLockedCommandBuilder().
					WithIDs([]int64{createdMediaID}).
					WithIsLocked(1).
					Build()

				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-is-locked", lockCommand, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				mediaHelper.WaitForEventProcessing()

				// 再解锁媒体
				unlockCommand := commandTesthelpers.NewBatchUpdateIsLockedCommandBuilder().
					WithIDs([]int64{createdMediaID}).
					WithIsLocked(0).
					Build()

				resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-is-locked", unlockCommand, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				mediaHelper.VerifyBatchUpdateIsLockedResponse(resp)
				mediaHelper.WaitForEventProcessing()

				// 验证解锁状态
				mediaHelper.VerifyMediaProperty(createdMediaID, "IsLocked", 0)
			})
		})
	})

	Describe("Delete", func() {
		It("成功批量删除已存在媒体", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand := time.Now().UnixNano() % 1000000
			randNum := 400000 + int(rand%900000)

			// 使用助手创建3个媒体 - 学习archive_media_relation_api_test.go的优雅方式
			mediaIDs := make([]int64, 3)
			for i := 0; i < 3; i++ {
				mediaName := fmt.Sprintf("待删除媒体_%06d_%d", randNum, i+1)
				mediaID, err := mediaHelper.CreateTestMedia(mediaName)
				Expect(err).NotTo(HaveOccurred())
				mediaIDs[i] = mediaID
			}

			// 验证媒体存在
			for _, id := range mediaIDs {
				mediaHelper.VerifyMediaInCommandDB(id)
				mediaHelper.VerifyMediaInQueryDB(id)
			}

			// 创建删除命令
			deleteCommand := commandTesthelpers.NewBatchDeleteMediaCommandBuilder().WithIDs(mediaIDs).Build()

			// 发送删除请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "DELETE", "/api/v1/media/batch", deleteCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证删除响应
			mediaHelper.VerifyBatchDeleteMediaResponse(resp)

			// 等待领域事件处理
			mediaHelper.WaitForEventProcessing()

			// 验证媒体已删除
			for _, id := range mediaIDs {
				mediaHelper.VerifyMediaNotExistsInCommandDB(id)
				mediaHelper.VerifyMediaNotExistsInQueryDB(id)
			}

			fmt.Printf("✅ 成功批量删除媒体: MediaIDs=%v\n", mediaIDs)
		})

		It("当尝试批量删除不存在的媒体时应该返回错误", func() {
			// 创建删除命令，使用不存在的ID
			deleteCommand := commandTesthelpers.NewBatchDeleteMediaCommandBuilder().
				WithIDs([]int64{99997, 99998, 99999}).
				Build()

			// 发送删除请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "DELETE", "/api/v1/media/batch", deleteCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证错误响应
			mediaHelper.VerifyErrorResponse(resp, "没有可删除的媒体")
		})
	})

	Describe("Query", func() {
		Context("媒体查询操作", func() {
			var createdMediaID int64

			BeforeEach(func() {
				var err error
				createdMediaID, err = mediaHelper.CreateTestMedia("查询测试媒体")
				Expect(err).NotTo(HaveOccurred())
			})

			It("应该成功查询指定的媒体", func() {
				// 发送查询请求
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", createdMediaID), nil, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 验证查询响应
				data := mediaHelper.VerifyGetMediaResponse(resp)
				Expect(data["mediaId"]).To(Equal(float64(createdMediaID)))
				Expect(data["mediaName"]).NotTo(BeEmpty())
			})

			It("当查询不存在的媒体时应该返回适当的响应", func() {
				nonExistentID := int64(99999)

				// 发送查询请求
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", nonExistentID), nil, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 检查响应 - 查询不存在的媒体可能返回200但data为nil，或返回错误
				body, err := testhelpers.GetResponseBody(resp)
				Expect(err).NotTo(HaveOccurred())

				if resp.StatusCode == http.StatusOK {
					// 如果返回200，data应该为nil或查询结果为空
					if data, exists := body["data"]; exists && data != nil {
						Fail(fmt.Sprintf("期望查询不存在的媒体返回空数据，但得到: %+v", data))
					}
				} else {
					// 如果返回错误状态，检查错误消息
					Expect(body["msg"]).To(ContainSubstring("not found"))
				}
			})
		})
	})
})
