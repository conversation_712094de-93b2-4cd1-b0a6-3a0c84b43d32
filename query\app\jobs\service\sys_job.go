package service

import (
	"errors"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/service"
	"github.com/gin-gonic/gin"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"

	"jxt-evidence-system/evidence-management/query/app/jobs"
	"jxt-evidence-system/evidence-management/query/app/jobs/models"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	cQuery "jxt-evidence-system/evidence-management/shared/common/query"
)

type SysJob struct {
	service.Service
	Cron *cron.Cron
}

// jiyuanjie add: GetLogger 获取上下文提供的日志器,代替掉api中的GetLogger
func (e *SysJob) GetLogger(c *gin.Context) *zap.Logger {
	ctx := c.Request.Context()
	// 从上下文中获取 logger
	requestLogger, ok := ctx.Value(logger.LoggerKey).(*zap.Logger)
	if !ok {
		// 如果没有找到 logger，使用默认 logger
		requestLogger = logger.Logger
	}
	return requestLogger
}

// RemoveJob 删除job
func (e *SysJob) RemoveJob(ctx *gin.Context, c *cQuery.GeneralDelDto) error {
	var err error
	var data models.SysJob
	err = e.Orm.Table(data.TableName()).First(&data, c.Id).Error
	if err != nil {
		e.GetLogger(ctx).Error("db error: %s", zap.Error(err))
		return err
	}
	cn := jobs.Remove(e.Cron, data.EntryId)

	select {
	case res := <-cn:
		if res {
			err = e.Orm.Table(data.TableName()).Where("entry_id = ?", data.EntryId).Update("entry_id", 0).Error
			if err != nil {
				e.GetLogger(ctx).Error("db error: %s", zap.Error(err))
			}
			return err
		}
	case <-time.After(time.Second * 1):
		e.Msg = "操作超时！"
		return nil
	}
	return nil
}

// StartJob 启动任务
func (e *SysJob) StartJob(ctx *gin.Context, c *cQuery.GeneralGetDto) error {
	var data models.SysJob
	var err error
	err = e.Orm.Table(data.TableName()).First(&data, c.Id).Error
	if err != nil {
		e.GetLogger(ctx).Error("db error: %s", zap.Error(err))
		return err
	}

	if data.Status == 1 {
		err = errors.New("当前Job是关闭状态不能被启动，请先启用。")
		return err
	}

	if data.JobType == 1 {
		var j = &jobs.HttpJob{}
		j.InvokeTarget = data.InvokeTarget
		j.CronExpression = data.CronExpression
		j.JobId = data.JobId
		j.Name = data.JobName
		data.EntryId, err = jobs.AddJob(e.Cron, j)
		if err != nil {
			e.GetLogger(ctx).Error("jobs AddJob[HttpJob] error: %s", zap.Error(err))
		}
	} else {
		var j = &jobs.ExecJob{}
		j.InvokeTarget = data.InvokeTarget
		j.CronExpression = data.CronExpression
		j.JobId = data.JobId
		j.Name = data.JobName
		j.Args = data.Args
		data.EntryId, err = jobs.AddJob(e.Cron, j)
		if err != nil {
			e.GetLogger(ctx).Error("jobs AddJob[ExecJob] error: %s", zap.Error(err))
		}
	}
	if err != nil {
		return err
	}

	err = e.Orm.Table(data.TableName()).Where(c.Id).Updates(&data).Error
	if err != nil {
		e.GetLogger(ctx).Error("db error: %s", zap.Error(err))
	}
	return err
}
