package api

import (
	"context"
	"strconv"
	"time"

	_ "github.com/ChenBigdata421/jxt-core/sdk/pkg/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"jxt-evidence-system/evidence-management/shared/common/restapi"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"jxt-evidence-system/evidence-management/query/internal/application/queryservice"
	"jxt-evidence-system/evidence-management/shared/common/di"
)

func init() {
	registrations = append(registrations, registerArchiveMediaRelationHandlerDependencies)
}

// ArchiveMediaRelationHandler的依赖注入
func registerArchiveMediaRelationHandlerDependencies() {
	// 注册 ArchiveMediaRelationHandler
	err := di.Provide(func(queryService *queryservice.ArchiveMediaRelationQueryService) *ArchiveMediaRelationHandler {
		return &ArchiveMediaRelationHandler{
			queryService: queryService,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide ArchiveMediaRelationHandler: %v", err)
	}
}

type ArchiveMediaRelationHandler struct {
	restapi.RestApi
	queryService *queryservice.ArchiveMediaRelationQueryService
}

// GetByID 根据ID获取档案媒体关联详情
// @Summary 通过关联ID获取档案媒体关联详情
// @Description 获取JSON
// @Tags ArchiveMediaRelation
// @Param id path int true "关联ID"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/archive-media-relations/{id} [get]
// @Security Bearer
func (e *ArchiveMediaRelationHandler) GetByID(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
	defer cancel()

	// 获取有符号64位整数id
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		e.GetLogger(c).Error("parse relation id err", zap.Error(err))
		e.Error(c, 500, err, err.Error())
		return
	}

	relation, err := e.queryService.GetRelationByID(ctx, id)
	if err != nil {
		e.GetLogger(c).Error(err.Error())
		e.Error(c, 500, err, "查询档案媒体关联失败")
		return
	}

	e.OK(c, relation, "查询成功")
}

// GetRelationsByArchiveID 根据档案ID获取关联的媒体列表
// @Summary 根据档案ID获取关联的媒体列表
// @Description 获取JSON
// @Tags ArchiveMediaRelation
// @Param archiveId path int true "档案ID"
// @Param page query int false "页码"
// @Param pageSize query int false "页条数"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/archives/{archiveId}/media-relations [get]
// @Security Bearer
func (e *ArchiveMediaRelationHandler) GetRelationsByArchiveID(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
	defer cancel()

	archiveIdStr := c.Param("archiveId")
	archiveId, err := strconv.ParseInt(archiveIdStr, 10, 64)
	if err != nil {
		e.GetLogger(c).Error("parse archive id err", zap.Error(err))
		e.Error(c, 400, err, "档案ID格式错误")
		return
	}

	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil {
		pageSize = 20
	}

	relations, err := e.queryService.GetRelationsByArchiveID(ctx, archiveId, page, pageSize)
	if err != nil {
		e.GetLogger(c).Error(err.Error())
		e.Error(c, 500, err, "查询档案关联媒体失败")
		return
	}

	e.OK(c, relations, "查询成功")
}

// GetRelationsByMediaID 根据媒体ID获取关联的档案列表
// @Summary 根据媒体ID获取关联的档案列表
// @Description 获取JSON
// @Tags ArchiveMediaRelation
// @Param mediaId path int true "媒体ID"
// @Param page query int false "页码"
// @Param pageSize query int false "页条数"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/media/{mediaId}/archive-relations [get]
// @Security Bearer
func (e *ArchiveMediaRelationHandler) GetRelationsByMediaID(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
	defer cancel()

	mediaIdStr := c.Param("mediaId")
	mediaId, err := strconv.ParseInt(mediaIdStr, 10, 64)
	if err != nil {
		e.GetLogger(c).Error("parse media id err", zap.Error(err))
		e.Error(c, 400, err, "媒体ID格式错误")
		return
	}

	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil {
		pageSize = 20
	}

	relations, err := e.queryService.GetRelationsByMediaID(ctx, mediaId, page, pageSize)
	if err != nil {
		e.GetLogger(c).Error(err.Error())
		e.Error(c, 500, err, "查询媒体关联档案失败")
		return
	}

	e.OK(c, relations, "查询成功")
}

// GetPage 分页查询档案媒体关联列表
// @Summary 档案媒体关联列表数据
// @Description 获取JSON
// @Tags ArchiveMediaRelation
// @Param archiveId query int false "档案ID"
// @Param mediaId query int false "媒体ID"
// @Param mediaCate query int false "媒体类型"
// @Param orgId query int false "机构ID"
// @Param policeId query int false "警员ID"
// @Param recorderId query int false "执法仪ID"
// @Param archiveType query int false "档案类型"
// @Param relationType query string false "关联类型"
// @Param pageSize query int false "页条数"
// @Param page query int false "页码"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/archive-media-relations [get]
// @Security Bearer
func (e *ArchiveMediaRelationHandler) GetPage(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
	defer cancel()

	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil {
		pageSize = 20
	}

	// 构建过滤条件
	filters := make(map[string]interface{})

	if archiveId := c.Query("archiveId"); archiveId != "" {
		if id, err := strconv.ParseInt(archiveId, 10, 64); err == nil {
			filters["archive_id"] = id
		}
	}

	if mediaId := c.Query("mediaId"); mediaId != "" {
		if id, err := strconv.ParseInt(mediaId, 10, 64); err == nil {
			filters["media_id"] = id
		}
	}

	if mediaCate := c.Query("mediaCate"); mediaCate != "" {
		if cate, err := strconv.Atoi(mediaCate); err == nil {
			filters["media_cate"] = cate
		}
	}

	if orgId := c.Query("orgId"); orgId != "" {
		if id, err := strconv.Atoi(orgId); err == nil {
			filters["org_id"] = id
		}
	}

	if policeId := c.Query("policeId"); policeId != "" {
		if id, err := strconv.Atoi(policeId); err == nil {
			filters["police_id"] = id
		}
	}

	if recorderId := c.Query("recorderId"); recorderId != "" {
		if id, err := strconv.Atoi(recorderId); err == nil {
			filters["recorder_id"] = id
		}
	}

	if archiveType := c.Query("archiveType"); archiveType != "" {
		if aType, err := strconv.Atoi(archiveType); err == nil {
			filters["archive_type"] = aType
		}
	}

	if relationType := c.Query("relationType"); relationType != "" {
		filters["relation_type"] = relationType
	}

	relations, err := e.queryService.GetRelationWithPagination(ctx, page, pageSize, filters)
	if err != nil {
		e.GetLogger(c).Error(err.Error())
		e.Error(c, 500, err, "分页查询档案媒体关联失败")
		return
	}

	e.PageOK(c, relations.List, int(relations.Total), page, pageSize, "查询成功")
}

// CheckRelationExists 检查档案和媒体的关联关系是否存在
// @Summary 检查档案和媒体的关联关系是否存在
// @Description 检查关联关系
// @Tags ArchiveMediaRelation
// @Param archiveId query int true "档案ID"
// @Param mediaId query int true "媒体ID"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/archive-media-relations/check [get]
// @Security Bearer
func (e *ArchiveMediaRelationHandler) CheckRelationExists(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
	defer cancel()
	archiveIdStr := c.Query("archiveId")
	mediaIdStr := c.Query("mediaId")

	if archiveIdStr == "" || mediaIdStr == "" {
		e.Error(c, 400, nil, "档案ID和媒体ID不能为空")
		return
	}

	archiveId, err := strconv.ParseInt(archiveIdStr, 10, 64)
	if err != nil {
		e.GetLogger(c).Error("parse archive id err", zap.Error(err))
		e.Error(c, 400, err, "档案ID格式错误")
		return
	}

	mediaId, err := strconv.ParseInt(mediaIdStr, 10, 64)
	if err != nil {
		e.GetLogger(c).Error("parse media id err", zap.Error(err))
		e.Error(c, 400, err, "媒体ID格式错误")
		return
	}

	exists, err := e.queryService.CheckRelationExists(ctx, archiveId, mediaId)
	if err != nil {
		e.GetLogger(c).Error(err.Error())
		e.Error(c, 500, err, "检查关联关系失败")
		return
	}

	result := map[string]interface{}{
		"exists":    exists,
		"archiveId": archiveId,
		"mediaId":   mediaId,
	}

	e.OK(c, result, "检查成功")
}

// GetArchiveMediaSummary 获取档案媒体关联汇总信息
// @Summary 获取档案媒体关联汇总信息
// @Description 获取JSON
// @Tags ArchiveMediaRelation
// @Param archiveId path int true "档案ID"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/archives/{archiveId}/media-summary [get]
// @Security Bearer
func (e *ArchiveMediaRelationHandler) GetArchiveMediaSummary(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
	defer cancel()

	archiveIdStr := c.Param("archiveId")
	archiveId, err := strconv.ParseInt(archiveIdStr, 10, 64)
	if err != nil {
		e.GetLogger(c).Error("parse archive id err", zap.Error(err))
		e.Error(c, 400, err, "档案ID格式错误")
		return
	}

	summary, err := e.queryService.GetArchiveMediaSummary(ctx, archiveId)
	if err != nil {
		e.GetLogger(c).Error(err.Error())
		e.Error(c, 500, err, "获取档案媒体汇总信息失败")
		return
	}

	e.OK(c, summary, "查询成功")
}

// GetMediaArchiveSummary 获取媒体档案关联汇总信息
// @Summary 获取媒体档案关联汇总信息
// @Description 获取JSON
// @Tags ArchiveMediaRelation
// @Param mediaId path int true "媒体ID"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/media/{mediaId}/archive-summary [get]
// @Security Bearer
func (e *ArchiveMediaRelationHandler) GetMediaArchiveSummary(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
	defer cancel()

	mediaIdStr := c.Param("mediaId")
	mediaId, err := strconv.ParseInt(mediaIdStr, 10, 64)
	if err != nil {
		e.GetLogger(c).Error("parse media id err", zap.Error(err))
		e.Error(c, 400, err, "媒体ID格式错误")
		return
	}

	summary, err := e.queryService.GetMediaArchiveSummary(ctx, mediaId)
	if err != nil {
		e.GetLogger(c).Error(err.Error())
		e.Error(c, 500, err, "获取媒体档案汇总信息失败")
		return
	}

	e.OK(c, summary, "查询成功")
}

// HealthCheck 健康检查接口
// @Summary 健康检查
// @Description 检查服务状态
// @Tags ArchiveMediaRelation
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/archive-media-relations/health [get]
func (e *ArchiveMediaRelationHandler) HealthCheck(c *gin.Context) {
	result := map[string]interface{}{
		"status":    "ok",
		"service":   "archive-media-relation-query",
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}

	e.OK(c, result, "服务运行正常")
}
