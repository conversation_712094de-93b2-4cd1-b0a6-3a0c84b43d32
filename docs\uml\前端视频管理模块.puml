@startuml

:         **video_management.html**
                    查询视频列表
             //__enfor/queryEnforTyps__//
                  //__media/getDatas__//
                  标注不是执法视频
//__media/updateRemarkNoEnforVideo__//
                              播放
         //__media/queryVideosByMark__//
            //__media/queryEncryptFile__//
          //__media/downloadMediaFile__//;
floating note
  前端视频
  管理模块
end note
if(  **标注类型**?   ) then (手工标注)
   :手工选择视频;
else (智能标注)
   :                        **index.html**
                       查询视频集
              //__media/getCalcVideos__//
              //__media/getVideosByIds__//
              //__media/getVideoCount__//
                视频集-添加/删除视频
              //__media/addToCurVideos__//
 //__media/deleteRemoveToCurVideos__//
                  标注不是执法视频
//__media/updateRemarkNoEnforVideo__//
                               播放
          //__media/queryPlayVideosByIds__//
             //__media/queryEncryptFile__//
           //__media/downloadMediaFile__//;

endif

:**enforce_behavior_relevance.html**
                查询执法数据列表
            //__enfor/queryEnforTyps__//
      //__alarm/getDicNamebyAlrmId__//
               //__alarmEs/getDatas__//
                     查询执法数据
            //__alarm/getAlarmByCode__//
         //--alarm/queryAllStatusName--//;

:            **file_information.html**
          查询创建档案需要的数据
             //__enfor/queryEnforType__//
               //__doc/getRandDocNo__//
                //__alarm/getAlarm__//
             //__media/getVideosByIds__//
                     添加视频到档案
             //__media/addToCurVideos__//
                     删除视频从档案
//__doc/deleteRemoveToCurDocTemp__//
                               播放
           //__media/queryVideosByMark__//
            //__media/queryEncryptFile__//
           //__media/downloadMediaFile__//
                           保存档案
                       //__doc/saveDoc__//;


@enduml
