package archive

import (
	"errors"
	"jxt-evidence-system/evidence-management/shared/common/models"
	"jxt-evidence-system/evidence-management/shared/domain/event"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
	jsoniter "github.com/json-iterator/go"
)

// Archive 档案聚合根
type Archive struct {
	ID int64 `json:"archiveId" gorm:"primaryKey;column:archive_id;autoIncrement;comment:档案ID"`

	// 基本信息
	ArchiveCode  string `json:"archiveCode" gorm:"size:128;column:archive_code;comment:档案编号"`
	ArchiveTitle string `json:"archiveTitle" gorm:"size:255;column:archive_title;comment:档案标题"`
	ArchiveType  int    `json:"archiveType" gorm:"size:4;column:archive_type;comment:档案类型"`
	Description  string `json:"description" gorm:"size:1024;column:description;comment:档案描述"`

	// 组织信息
	OrgID int `json:"orgId" gorm:"column:org_id;comment:管理部门ID"`

	// 业务时间信息
	StorageDuration int        `json:"storageDuration" gorm:"column:storage_duration;comment:保存期限(月)"`
	ExpirationTime  *time.Time `json:"expirationTime" gorm:"column:expiration_time;default:NULL;comment:过期时间"`

	// 状态信息
	Status  int    `json:"status" gorm:"size:4;column:status;comment:档案状态(0:正常,1:删除,2:其它)"`
	Remarks string `json:"remarks" gorm:"size:512;column:remarks;comment:备注信息"`

	// 审计字段
	models.ControlBy
	models.ModelTime

	// 领域事件
	events []event.Event `gorm:"-"` // 使用 gorm:"-" 标签明确告诉 GORM 忽略这个字段
}

func (*Archive) TableName() string {
	return "t_evidence_archives"
}

func (e *Archive) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *Archive) GetId() interface{} {
	return e.ID
}

func (e *Archive) AddEvent(event event.Event) {
	e.events = append(e.events, event)
}

func (e *Archive) Events() []event.Event {
	return e.events
}

func (e *Archive) ClearEvents() {
	e.events = []event.Event{}
}

// CreateArchiveAndSave 创建档案并保存事件
func (e *Archive) CreateArchiveAndSave() error {
	domainEvent := e.createArchiveCreatedEvent()
	e.AddEvent(domainEvent)
	return nil
}

// UpdateArchive 更新档案
func (e *Archive) UpdateArchive(updates map[string]interface{}) error {
	// 检查档案状态：已删除的档案不能更新
	if e.Status == 1 {
		return errors.New("档案已删除，无法修改")
	}
	domainEvent := e.createArchiveUpdatedEvent(updates)
	e.AddEvent(domainEvent)
	return nil
}

// DeleteArchive 删除档案
func (e *Archive) DeleteArchive() error {
	// 检查档案状态：已删除的档案不能再次删除
	if e.Status == 1 {
		return errors.New("档案已删除状态，无法再次删除")
	}
	domainEvent := e.createArchiveDeletedEvent()
	e.AddEvent(domainEvent)
	return nil
}

// createArchiveCreatedEvent 创建档案创建事件
func (e *Archive) createArchiveCreatedEvent() *event.DomainEvent {
	payload := &event.ArchiveCreatedPayload{
		// 基本信息
		ArchiveID:    e.ID,
		ArchiveCode:  e.ArchiveCode,
		ArchiveTitle: e.ArchiveTitle,
		ArchiveType:  e.ArchiveType,
		Description:  e.Description,

		// 组织信息
		OrgID: e.OrgID,

		// 业务时间信息
		StorageDuration: e.StorageDuration,
		ExpirationTime:  e.ExpirationTime,

		// 状态信息
		Status:  e.Status,
		Remarks: e.Remarks,

		// 审计字段
		CreateBy:  e.CreateBy,
		UpdateBy:  e.UpdateBy,
		CreatedAt: e.CreatedAt,
		UpdatedAt: e.UpdatedAt,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("序列化 ArchiveCreatedPayload 失败", "error", err)
		return nil
	}

	return event.NewDomainEvent(event.EventTypeArchiveCreated, e.ID, "Archive", payloadJSON)
}

// createArchiveUpdatedEvent 创建档案更新事件
func (e *Archive) createArchiveUpdatedEvent(updates map[string]interface{}) *event.DomainEvent {
	payload := event.ArchiveUpdatedPayload{
		ArchiveID:     e.ID,
		UpdatedFields: updates,
		UpdateBy:      e.UpdateBy,
		UpdatedAt:     e.UpdatedAt,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 ArchiveUpdatedPayload 转换为 JSON", "error", err)
		return nil
	}

	return event.NewDomainEvent(event.EventTypeArchiveUpdated, e.ID, "Archive", payloadJSON)
}

// createArchiveDeletedEvent 创建档案删除事件
func (e *Archive) createArchiveDeletedEvent() *event.DomainEvent {
	payload := event.ArchiveDeletedPayload{
		ArchiveID: e.ID,
		UpdateBy:  e.UpdateBy,
		DeletedAt: e.DeletedAt.Time,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 ArchiveDeletedPayload 转换为 JSON", "error", err)
		return nil
	}

	return event.NewDomainEvent(event.EventTypeArchiveDeleted, e.ID, "Archive", payloadJSON)
}
