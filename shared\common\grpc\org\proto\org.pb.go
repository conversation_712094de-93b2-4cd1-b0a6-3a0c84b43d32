// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: org.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 根据组织ID查询请求
type GetOrgByIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      string                 `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"` // 租户ID，用于多租户系统
	OrgId         int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`         // 组织唯一ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrgByIdReq) Reset() {
	*x = GetOrgByIdReq{}
	mi := &file_org_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrgByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrgByIdReq) ProtoMessage() {}

func (x *GetOrgByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_org_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrgByIdReq.ProtoReflect.Descriptor instead.
func (*GetOrgByIdReq) Descriptor() ([]byte, []int) {
	return file_org_proto_rawDescGZIP(), []int{0}
}

func (x *GetOrgByIdReq) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *GetOrgByIdReq) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

// 根据组织编码查询请求
type GetOrgByCodeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      string                 `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"` // 租户ID，用于多租户系统
	OrgCode       string                 `protobuf:"bytes,2,opt,name=org_code,json=orgCode,proto3" json:"org_code,omitempty"`    // 组织编码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrgByCodeReq) Reset() {
	*x = GetOrgByCodeReq{}
	mi := &file_org_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrgByCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrgByCodeReq) ProtoMessage() {}

func (x *GetOrgByCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_org_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrgByCodeReq.ProtoReflect.Descriptor instead.
func (*GetOrgByCodeReq) Descriptor() ([]byte, []int) {
	return file_org_proto_rawDescGZIP(), []int{1}
}

func (x *GetOrgByCodeReq) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *GetOrgByCodeReq) GetOrgCode() string {
	if x != nil {
		return x.OrgCode
	}
	return ""
}

// 根据组织名称查询请求
type GetOrgByNameReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      string                 `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"` // 租户ID，用于多租户系统
	OrgName       string                 `protobuf:"bytes,2,opt,name=org_name,json=orgName,proto3" json:"org_name,omitempty"`    // 组织名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrgByNameReq) Reset() {
	*x = GetOrgByNameReq{}
	mi := &file_org_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrgByNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrgByNameReq) ProtoMessage() {}

func (x *GetOrgByNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_org_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrgByNameReq.ProtoReflect.Descriptor instead.
func (*GetOrgByNameReq) Descriptor() ([]byte, []int) {
	return file_org_proto_rawDescGZIP(), []int{2}
}

func (x *GetOrgByNameReq) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *GetOrgByNameReq) GetOrgName() string {
	if x != nil {
		return x.OrgName
	}
	return ""
}

// 获取组织全名请求
type GetOrgFullNameReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      string                 `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"` // 租户ID，用于多租户系统
	OrgId         int32                  `protobuf:"varint,2,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`         // 组织ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOrgFullNameReq) Reset() {
	*x = GetOrgFullNameReq{}
	mi := &file_org_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOrgFullNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOrgFullNameReq) ProtoMessage() {}

func (x *GetOrgFullNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_org_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOrgFullNameReq.ProtoReflect.Descriptor instead.
func (*GetOrgFullNameReq) Descriptor() ([]byte, []int) {
	return file_org_proto_rawDescGZIP(), []int{3}
}

func (x *GetOrgFullNameReq) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *GetOrgFullNameReq) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

// 组织信息响应，字段与领域模型SysOrg对应
type OrgInfoReply struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	OrgId     int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`              // 组织ID
	ParentId  int32                  `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`     // 上级单位ID
	OrgPath   string                 `protobuf:"bytes,3,opt,name=org_path,json=orgPath,proto3" json:"org_path,omitempty"`         // 单位ID路径
	OrgCode   string                 `protobuf:"bytes,4,opt,name=org_code,json=orgCode,proto3" json:"org_code,omitempty"`         // 单位编码
	OrgName   string                 `protobuf:"bytes,5,opt,name=org_name,json=orgName,proto3" json:"org_name,omitempty"`         // 单位名称
	OrgNameJc string                 `protobuf:"bytes,6,opt,name=org_name_jc,json=orgNameJc,proto3" json:"org_name_jc,omitempty"` // 单位简称
	OrgType   string                 `protobuf:"bytes,7,opt,name=org_type,json=orgType,proto3" json:"org_type,omitempty"`         // 单位类型
	Region    string                 `protobuf:"bytes,8,opt,name=region,proto3" json:"region,omitempty"`                          // 区域
	Sort      int32                  `protobuf:"varint,9,opt,name=sort,proto3" json:"sort,omitempty"`                             // 排序
	Leader    string                 `protobuf:"bytes,10,opt,name=leader,proto3" json:"leader,omitempty"`                         // 单位负责人
	Phone     string                 `protobuf:"bytes,11,opt,name=phone,proto3" json:"phone,omitempty"`                           // 负责人手机
	Email     string                 `protobuf:"bytes,12,opt,name=email,proto3" json:"email,omitempty"`                           // 负责人邮箱
	Status    int32                  `protobuf:"varint,13,opt,name=status,proto3" json:"status,omitempty"`                        // 状态
	CreatedAt string                 `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`  // 创建时间
	UpdatedAt string                 `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`  // 更新时间
	// 关联信息
	ParentOrg     *OrgInfoReply `protobuf:"bytes,16,opt,name=parent_org,json=parentOrg,proto3" json:"parent_org,omitempty"` // 上级组织信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrgInfoReply) Reset() {
	*x = OrgInfoReply{}
	mi := &file_org_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrgInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrgInfoReply) ProtoMessage() {}

func (x *OrgInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_org_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrgInfoReply.ProtoReflect.Descriptor instead.
func (*OrgInfoReply) Descriptor() ([]byte, []int) {
	return file_org_proto_rawDescGZIP(), []int{4}
}

func (x *OrgInfoReply) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *OrgInfoReply) GetParentId() int32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *OrgInfoReply) GetOrgPath() string {
	if x != nil {
		return x.OrgPath
	}
	return ""
}

func (x *OrgInfoReply) GetOrgCode() string {
	if x != nil {
		return x.OrgCode
	}
	return ""
}

func (x *OrgInfoReply) GetOrgName() string {
	if x != nil {
		return x.OrgName
	}
	return ""
}

func (x *OrgInfoReply) GetOrgNameJc() string {
	if x != nil {
		return x.OrgNameJc
	}
	return ""
}

func (x *OrgInfoReply) GetOrgType() string {
	if x != nil {
		return x.OrgType
	}
	return ""
}

func (x *OrgInfoReply) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *OrgInfoReply) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *OrgInfoReply) GetLeader() string {
	if x != nil {
		return x.Leader
	}
	return ""
}

func (x *OrgInfoReply) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *OrgInfoReply) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *OrgInfoReply) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *OrgInfoReply) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *OrgInfoReply) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

func (x *OrgInfoReply) GetParentOrg() *OrgInfoReply {
	if x != nil {
		return x.ParentOrg
	}
	return nil
}

// 组织全名响应
type OrgFullNameReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`         // 组织ID
	FullName      string                 `protobuf:"bytes,2,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"` // 组织全名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrgFullNameReply) Reset() {
	*x = OrgFullNameReply{}
	mi := &file_org_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrgFullNameReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrgFullNameReply) ProtoMessage() {}

func (x *OrgFullNameReply) ProtoReflect() protoreflect.Message {
	mi := &file_org_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrgFullNameReply.ProtoReflect.Descriptor instead.
func (*OrgFullNameReply) Descriptor() ([]byte, []int) {
	return file_org_proto_rawDescGZIP(), []int{5}
}

func (x *OrgFullNameReply) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *OrgFullNameReply) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

var File_org_proto protoreflect.FileDescriptor

const file_org_proto_rawDesc = "" +
	"\n" +
	"\torg.proto\x12\x03org\"C\n" +
	"\rGetOrgByIdReq\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\tR\btenantId\x12\x15\n" +
	"\x06org_id\x18\x02 \x01(\x05R\x05orgId\"I\n" +
	"\x0fGetOrgByCodeReq\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\tR\btenantId\x12\x19\n" +
	"\borg_code\x18\x02 \x01(\tR\aorgCode\"I\n" +
	"\x0fGetOrgByNameReq\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\tR\btenantId\x12\x19\n" +
	"\borg_name\x18\x02 \x01(\tR\aorgName\"G\n" +
	"\x11GetOrgFullNameReq\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\tR\btenantId\x12\x15\n" +
	"\x06org_id\x18\x02 \x01(\x05R\x05orgId\"\xc6\x03\n" +
	"\fOrgInfoReply\x12\x15\n" +
	"\x06org_id\x18\x01 \x01(\x05R\x05orgId\x12\x1b\n" +
	"\tparent_id\x18\x02 \x01(\x05R\bparentId\x12\x19\n" +
	"\borg_path\x18\x03 \x01(\tR\aorgPath\x12\x19\n" +
	"\borg_code\x18\x04 \x01(\tR\aorgCode\x12\x19\n" +
	"\borg_name\x18\x05 \x01(\tR\aorgName\x12\x1e\n" +
	"\vorg_name_jc\x18\x06 \x01(\tR\torgNameJc\x12\x19\n" +
	"\borg_type\x18\a \x01(\tR\aorgType\x12\x16\n" +
	"\x06region\x18\b \x01(\tR\x06region\x12\x12\n" +
	"\x04sort\x18\t \x01(\x05R\x04sort\x12\x16\n" +
	"\x06leader\x18\n" +
	" \x01(\tR\x06leader\x12\x14\n" +
	"\x05phone\x18\v \x01(\tR\x05phone\x12\x14\n" +
	"\x05email\x18\f \x01(\tR\x05email\x12\x16\n" +
	"\x06status\x18\r \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18\x0e \x01(\tR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x0f \x01(\tR\tupdatedAt\x120\n" +
	"\n" +
	"parent_org\x18\x10 \x01(\v2\x11.org.OrgInfoReplyR\tparentOrg\"F\n" +
	"\x10OrgFullNameReply\x12\x15\n" +
	"\x06org_id\x18\x01 \x01(\x05R\x05orgId\x12\x1b\n" +
	"\tfull_name\x18\x02 \x01(\tR\bfullName2\x80\x02\n" +
	"\x0eOrgInfoService\x125\n" +
	"\n" +
	"GetOrgById\x12\x12.org.GetOrgByIdReq\x1a\x11.org.OrgInfoReply\"\x00\x129\n" +
	"\fGetOrgByCode\x12\x14.org.GetOrgByCodeReq\x1a\x11.org.OrgInfoReply\"\x00\x129\n" +
	"\fGetOrgByName\x12\x14.org.GetOrgByNameReq\x1a\x11.org.OrgInfoReply\"\x00\x12A\n" +
	"\x0eGetOrgFullName\x12\x16.org.GetOrgFullNameReq\x1a\x15.org.OrgFullNameReply\"\x00B\tZ\a.;protob\x06proto3"

var (
	file_org_proto_rawDescOnce sync.Once
	file_org_proto_rawDescData []byte
)

func file_org_proto_rawDescGZIP() []byte {
	file_org_proto_rawDescOnce.Do(func() {
		file_org_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_org_proto_rawDesc), len(file_org_proto_rawDesc)))
	})
	return file_org_proto_rawDescData
}

var file_org_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_org_proto_goTypes = []any{
	(*GetOrgByIdReq)(nil),     // 0: org.GetOrgByIdReq
	(*GetOrgByCodeReq)(nil),   // 1: org.GetOrgByCodeReq
	(*GetOrgByNameReq)(nil),   // 2: org.GetOrgByNameReq
	(*GetOrgFullNameReq)(nil), // 3: org.GetOrgFullNameReq
	(*OrgInfoReply)(nil),      // 4: org.OrgInfoReply
	(*OrgFullNameReply)(nil),  // 5: org.OrgFullNameReply
}
var file_org_proto_depIdxs = []int32{
	4, // 0: org.OrgInfoReply.parent_org:type_name -> org.OrgInfoReply
	0, // 1: org.OrgInfoService.GetOrgById:input_type -> org.GetOrgByIdReq
	1, // 2: org.OrgInfoService.GetOrgByCode:input_type -> org.GetOrgByCodeReq
	2, // 3: org.OrgInfoService.GetOrgByName:input_type -> org.GetOrgByNameReq
	3, // 4: org.OrgInfoService.GetOrgFullName:input_type -> org.GetOrgFullNameReq
	4, // 5: org.OrgInfoService.GetOrgById:output_type -> org.OrgInfoReply
	4, // 6: org.OrgInfoService.GetOrgByCode:output_type -> org.OrgInfoReply
	4, // 7: org.OrgInfoService.GetOrgByName:output_type -> org.OrgInfoReply
	5, // 8: org.OrgInfoService.GetOrgFullName:output_type -> org.OrgFullNameReply
	5, // [5:9] is the sub-list for method output_type
	1, // [1:5] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_org_proto_init() }
func file_org_proto_init() {
	if File_org_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_org_proto_rawDesc), len(file_org_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_org_proto_goTypes,
		DependencyIndexes: file_org_proto_depIdxs,
		MessageInfos:      file_org_proto_msgTypes,
	}.Build()
	File_org_proto = out.File
	file_org_proto_goTypes = nil
	file_org_proto_depIdxs = nil
}
