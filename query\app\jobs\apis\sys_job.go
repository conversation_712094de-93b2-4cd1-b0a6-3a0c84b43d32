package apis

import (
	"net/http"

	"github.com/ChenBigdata421/jxt-core/sdk"
	"github.com/ChenBigdata421/jxt-core/sdk/pkg"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"jxt-evidence-system/evidence-management/query/app/jobs/service"
	"jxt-evidence-system/evidence-management/shared/common/global"
	cQuery "jxt-evidence-system/evidence-management/shared/common/query"
	"jxt-evidence-system/evidence-management/shared/common/restapi"
)

type SysJob struct {
	restapi.RestApi // jiyuanjie add: 继承 RestApi 结构体, 为了并发安全
}

// RemoveJobForService 调用service实现
func (e SysJob) RemoveJobForService(c *gin.Context) {
	v := cQuery.GeneralDelDto{}
	s := service.SysJob{}
	s.Cache = sdk.Runtime.GetCacheAdapter()

	err := e.Bind(c, &v) //为了并发安全，去掉 MakeContext(c). MakeOrm().
	if err != nil {
		e.GetLogger(c).Error("RemoveJob error, %s", zap.Error(err))
		return
	}

	if err != nil {
		e.GetLogger(c).Error("RemoveJob error, %s", zap.Error(err))
		return
	}

	// 从上下文中获取tenantID
	ctx := c.Request.Context()
	tenantID, ok := ctx.Value(global.TenantIDKey).(string)
	if !ok {
		e.GetLogger(c).Error("获取租户ID失败，请检查配置文件")
		e.Error(c, http.StatusInternalServerError, err, "获取租户ID失败，请检查配置文件")
		return
	}

	s.Cron = sdk.Runtime.GetTenantCrontab(tenantID)
	err = s.RemoveJob(c, &v)
	if err != nil {
		e.GetLogger(c).Error("RemoveJob error, %s", zap.Error(err))
		e.Error(c, 500, err, "")
		return
	}
	e.OK(c, nil, s.Msg)
}

// StartJobForService 启动job service实现
func (e SysJob) StartJobForService(c *gin.Context) {
	//log := e.GetLogger(c) jiyuanjie 注释掉

	var v cQuery.GeneralGetDto
	err := c.BindUri(&v)
	if err != nil {
		e.GetLogger(c).Error("参数验证错误, error: %s", zap.Error(err))
		e.Error(c, http.StatusUnprocessableEntity, err, "参数验证失败")
		return
	}
	s := service.SysJob{}

	db, err := pkg.GetOrm(c)
	if err != nil {
		e.Error(c, http.StatusInternalServerError, err, "数据库连接获取失败")
		return
	}
	s.Orm = db
	//s.Log = log  // jiyuanjie 注释掉

	// 从上下文中获取tenantID
	ctx := c.Request.Context()
	tenantID, ok := ctx.Value(global.TenantIDKey).(string)
	if !ok {
		e.GetLogger(c).Error("获取租户ID失败，请检查配置文件")
		e.Error(c, http.StatusInternalServerError, err, "获取租户ID失败，请检查配置文件")
		return
	}

	s.Cron = sdk.Runtime.GetTenantCrontab(tenantID)
	err = s.StartJob(c, &v)
	if err != nil {
		e.GetLogger(c).Error("GetCrontabKey error, %s", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, err.Error())
		return
	}
	e.OK(c, nil, s.Msg)
}
