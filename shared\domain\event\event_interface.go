package event

import "time"

type Event interface {
	// TenantId 返回事件所属的租户ID
	GetTenantId() string

	// EventID 返回事件的唯一标识符
	GetEventID() string

	// EventType 返回事件的类型或名称
	GetEventType() string

	// OccurredAt 返回事件发生的时间
	GetOccurredAt() time.Time

	// Version 返回事件的版本号，用于处理事件演化
	GetVersion() int

	// AggregateID 返回与事件相关的聚合根的标识符
	GetAggregateID() interface{}

	// AggregateType 返回与事件相关的聚合根的类型
	GetAggregateType() string

	MarshalJSON() ([]byte, error)

	UnmarshalJSON([]byte) error

	SetTenantId(string) // 设置事件所属的租户ID
}
