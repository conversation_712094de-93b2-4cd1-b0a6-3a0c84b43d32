package api

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/jwtauth/user"
	_ "github.com/ChenBigdata421/jxt-core/sdk/pkg/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"jxt-evidence-system/evidence-management/command/internal/application/command"
	"jxt-evidence-system/evidence-management/command/internal/application/service/port"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/restapi"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

func init() {
	registrations = append(registrations, registerMediaApiDependencies)
}

func registerMediaApiDependencies() {
	err := di.Provide(func(usecase port.MediaService) *MediaHandler {
		return &MediaHandler{
			mediaService: usecase,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide MediaHandler: %v", err)
	}
}

type MediaHandler struct {
	restapi.RestApi
	mediaService port.MediaService
}

// @Summary 创建媒体
// @Description 创建新的媒体
// @Tags Media
// @Accept json
// @Produce json
// @Param request body dto.UploadMediaCommand true "请求体"
// @Success 200 {object} dto.UploadMediaCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "创建媒体失败"
// @Router /media [post]
// @Example POST /media
// @ExampleRequest
//
//	{
//	  "mediaName": "exampleMedia",
//	  "importantLevel": 1,
//	  "mediaCate": "video",
//	  "mediaSuffix": "mp4",
//	  "shotTimeStart": "2023-01-01T00:00:00Z",
//	  "shotTime": "2023-01-01T01:00:00Z",
//	  "videoClarity": 1,
//	  "videoDuration": 3600000,
//	  "fileSize": 1024,
//	  "orgId": 1,
//	  "storageType": 1,
//	  "siteId": 1,
//	  "storageId": 1,
//	  "siteClientId": 1,
//	  "trialId": 1,
//	  "importTime": "2023-01-01T02:00:00Z",
//	  "comments": "example comments"
//	}
func (e MediaHandler) MediaUpload(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	req := command.UploadMediaCommand{}

	if err := c.ShouldBindJSON(&req); err != nil {
		e.GetLogger(c).Error("bind UploadMediaCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("创建媒体失败，参数验证失败: %s", err.Error()))
		return
	}

	req.SetCreateBy(user.GetUserId(c))
	if err := e.mediaService.UploadMedia(ctx, &req); err != nil {
		e.GetLogger(c).Error("Create Media failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("创建媒体失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Media created successfully", zap.String("mediaName", req.MediaName))
	e.OK(c, req.MediaName, "创建媒体成功")
}

// @Summary 更新媒体
// @Description 更新指定的媒体
// @Tags Media
// @Accept json
// @Produce json
// @Param request body dto.UpdateMediaCommand true "请求体"
// @Success 200 {object} dto.UpdateMediaCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "更新媒体失败"
// @Router /media/{id} [put]
// @Example PUT /media/1
// @ExampleRequest
//
//	{
//	  "mediaName": "exampleMedia",
//	  "importantLevel": 1,
//	  "mediaCate": "video",
//	  "mediaSuffix": "mp4",
//	  "shotTimeStart": "2023-01-01T00:00:00Z",
//	  "shotTime": "2023-01-01T01:00:00Z",
//	  "videoClarity": 1,
//	  "videoDuration": 3600000,
//	  "fileSize": 1024,
//	  "orgId": 1,
//	  "storageType": 1,
//	  "siteId": 1,
//	  "storageId": 1,
//	  "siteClientId": 1,
//	  "trialId": 1,
//	  "importTime": "2023-01-01T02:00:00Z",
//	  "comments": "updated comments"
//	}
func (e MediaHandler) MediaUpdateByID(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	command := command.UpdateMediaCommand{}

	// Binding URI and JSON together
	if err := c.ShouldBindUri(&command); err != nil {
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("更新媒体失败，参数验证失败: %s", err.Error()))
		return
	}

	// 保留command.ID的值，因为会在ShouldBindJSON后被覆盖
	commandID := command.GetId()

	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind UpdateMediaCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("更新媒体失败，参数验证失败: %s", err.Error()))
		return
	}
	// 将保留的commandID赋值给command
	command.SetId(commandID)
	command.SetUpdateBy(user.GetUserId(c))
	if err := e.mediaService.UpdateMediaByID(ctx, &command); err != nil {
		e.GetLogger(c).Error("Update Media failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("更新媒体失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Media updated successfully", zap.Any("mediaId", command.GetId()))
	e.OK(c, command.GetId(), "Media updated successfully")
}

// @Summary 更新媒体
// @Description 更新指定的媒体
// @Tags Media
// @Accept json
// @Produce json
// @Param request body dto.UpdateMediaCommand true "请求体"
// @Success 200 {object} dto.UpdateMediaCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "更新媒体失败"
// @Router /media/{id} [put]
// @Example PUT /media/1
// @ExampleRequest
//
//	{
//	  "mediaName": "exampleMedia",
//	  "importantLevel": 1,
//	  "mediaCate": "video",
//	  "mediaSuffix": "mp4",
//	  "shotTimeStart": "2023-01-01T00:00:00Z",
//	  "shotTime": "2023-01-01T01:00:00Z",
//	  "videoClarity": 1,
//	  "videoDuration": 3600000,
//	  "fileSize": 1024,
//	  "orgId": 1,
//	  "storageType": 1,
//	  "siteId": 1,
//	  "storageId": 1,
//	  "siteClientId": 1,
//	  "trialId": 1,
//	  "importTime": "2023-01-01T02:00:00Z",
//	  "comments": "updated comments"
//	}
func (e MediaHandler) MediaBatchUpdate(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	command := command.BatchUpdateMediaCommand{}

	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind UpdateMediaCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("批量更新媒体失败，参数验证失败: %s", err.Error()))
		return
	}

	// 验证ID列表不能为空
	if len(command.IDs) == 0 {
		e.GetLogger(c).Error("empty media ID list")
		e.Error(c, http.StatusBadRequest, nil, "批量更新媒体失败，参数验证失败: 媒体ID列表不能为空")
		return
	}

	command.SetUpdateBy(user.GetUserId(c))
	if err := e.mediaService.BatchUpdateMedia(ctx, &command); err != nil {
		e.GetLogger(c).Error("Update Media failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("批量更新媒体失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Media updated successfully", zap.Any("mediaId", command.GetIds()))
	e.OK(c, command.GetIds(), "批量更新媒体成功")
}

// @Summary 删除媒体
// @Description 删除指定的媒体
// @Tags Media
// @Accept json
// @Produce json
// @Param id path int true "媒体ID"
// @Success 200 {object} int64 "成功"
// @Failure 400 {string} string "无效的ID参数"
// @Failure 500 {string} string "删除媒体失败"
// @Router /media/{id} [delete]
// @Example DELETE /media/1
func (e MediaHandler) MediaDeleteByID(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	// 从URL路径获取ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		e.GetLogger(c).Error("invalid media ID", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, "删除媒体失败，ID必须是有效的数字")
		return
	}

	// 调用服务删除媒体
	if err := e.mediaService.DeleteMediaByID(ctx, id); err != nil {
		e.GetLogger(c).Error("Delete Media failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("删除媒体失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Media deleted successfully", zap.Int64("mediaId", id))
	e.OK(c, id, "媒体删除成功")
}

// @Summary 删除媒体
// @Description 删除指定的媒体
// @Tags Media
// @Accept json
// @Produce json
// @Param request body dto.DeleteMediaCommand true "请求体"
// @Success 200 {object} dto.DeleteMediaCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "删除媒体失败"
// @Router /media [delete]
// @Example DELETE /media
// @ExampleRequest
//
//	{
//	  "IDs": [1, 2, 3],
//	}
func (e MediaHandler) MediaBatchDelete(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	command := command.BatchDeleteMediaCommand{}
	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind DeleteManyMediaCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("批量删除媒体失败，参数验证失败: %s", err.Error()))
		return
	}

	command.SetUpdateBy(user.GetUserId(c))
	if err := e.mediaService.BatchDeleteMedia(ctx, &command); err != nil {
		e.GetLogger(c).Error("BatchDelete Media failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("批量删除媒体失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Media batchdeleted successfully", zap.Any("mediaIds", command.GetIds()))
	e.OK(c, command.GetIds(), "批量删除媒体成功")
}

// @Summary 标注非执法视频
// @Description 标注指定的视频为非执法视频
// @Tags Media
// @Accept json
// @Produce json
// @Param request body dto.RemarkNoEnforMediaCommand true "请求体"
// @Success 200 {object} dto.RemarkNoEnforMediaCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "标注非执法视频失败"
// @Router /media/remark-no-enfor [post]
// @Example POST /media/remark-no-enfor
// @ExampleRequest
//
//	{
//	  "ids": [1, 2, 3],
//	  "isEnforMedia": 0
//	}
func (e MediaHandler) MarkNonEnforcementMediaStatus(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	command := command.BatchUpdateNonEnforcementStatusCommand{}
	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind BatchUpdateNonEnforcementStatusCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("标注非执法视频失败，参数验证失败: %s", err.Error()))
		return
	}

	command.SetUpdateBy(user.GetUserId(c))

	if err := e.mediaService.BatchUpdateMediaNonEnforcementStatus(ctx, &command); err != nil {
		e.GetLogger(c).Error("MarkNonEnforcementMediaStatus failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("标注非执法视频失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Non-enforcement media marked successfully", zap.Any("mediaIds", command.GetIds()))
	e.OK(c, command.GetIds(), "标注非执法视频成功")
}

// @Summary 批量更新执法类型
// @Description 批量更新指定媒体的执法类型
// @Tags Media
// @Accept json
// @Produce json
// @Param request body dto.BatchUpdateEnforceTypeCommand true "请求体"
// @Success 200 {object} dto.BatchUpdateEnforceTypeCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "批量更新执法类型失败"
// @Router /media/batch/update-enforce-type [post]
// @Example POST /media/batch/update-enforce-type
// @ExampleRequest
//
//	{
//	  "ids": [1, 2, 3],
//	  "enforceType": 1
//	}
func (e MediaHandler) BatchUpdateEnforceType(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	command := command.BatchUpdateEnforceTypeCommand{}
	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind BatchUpdateEnforceTypeCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("批量更新执法类型失败，参数验证失败: %s", err.Error()))
		return
	}

	command.SetUpdateBy(user.GetUserId(c))

	if err := e.mediaService.BatchUpdateEnforceType(ctx, &command); err != nil {
		e.GetLogger(c).Error("BatchUpdateEnforceType failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("批量更新执法类型失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Enforce type updated successfully", zap.Any("mediaIds", command.GetIds()))
	e.OK(c, command.GetIds(), "批量更新执法类型成功")
}

// @Summary 批量更新锁定状态
// @Description 批量更新指定媒体的锁定状态
// @Tags Media
// @Accept json
// @Produce json
// @Param request body dto.BatchUpdateIsLockedCommand true "请求体"
// @Success 200 {object} dto.BatchUpdateIsLockedCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "批量更新锁定状态失败"
// @Router /media/batch/update-is-locked [post]
// @Example POST /media/batch/update-is-locked
// @ExampleRequest
//
//	{
//	  "ids": [1, 2, 3],
//	  "isLocked": 1
//	}
func (e MediaHandler) BatchUpdateIsLocked(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	command := command.BatchUpdateIsLockedCommand{}
	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind BatchUpdateIsLockedCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("批量更新锁定状态失败，参数验证失败: %s", err.Error()))
		return
	}

	command.SetUpdateBy(user.GetUserId(c))

	if err := e.mediaService.BatchUpdateIsLocked(ctx, &command); err != nil {
		e.GetLogger(c).Error("BatchUpdateIsLocked failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("批量更新锁定状态失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Locked status updated successfully", zap.Any("mediaIds", command.GetIds()))
	e.OK(c, command.GetIds(), "批量更新锁定状态成功")
}
