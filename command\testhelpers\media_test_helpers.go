package testhelpers

import (
	"jxt-evidence-system/evidence-management/command/internal/application/command"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/media"
	"time"
)

// UploadMediaCommandBuilder 上传媒体构建器

type UploadMediaCommandBuilder struct {
	command *command.UploadMediaCommand
}

func NewUploadMediaCommandBuilder() *UploadMediaCommandBuilder {
	mediaCate := 1
	now := time.Now()
	return &UploadMediaCommandBuilder{
		command: &command.UploadMediaCommand{
			MediaName:       "media1",
			MediaCate:       &mediaCate,
			PoliceNo:        "123456",
			RecorderNo:      "REC001",
			AuthKey:         "md5hashhere",
			RequestIdentity: "REQ001",
			ImportantLevel:  1,
			MediaSuffix:     "mp4",
			PoliceName:      "张三",
			ShotTimeStart:   time.Now(),
			ShotTime:        time.Now().Add(time.Hour),
			VideoClarity:    1,
			VideoDuration:   3600000,
			FileSize:        1024,
			OrgID:           1,
			OrgName:         "公安局",
			StorageType:     0,
			SiteID:          1,
			StorageID:       1,
			SiteClientID:    1,
			TrialID:         1,
			ImportTime:      &now,
			AcquisitionTime: &now,
			Comments:        "",
		},
	}
}

func (b *UploadMediaCommandBuilder) WithMediaName(mediaName string) *UploadMediaCommandBuilder {
	b.command.MediaName = mediaName
	return b
}

func (b *UploadMediaCommandBuilder) WithMediaCate(mediaCate int) *UploadMediaCommandBuilder {
	b.command.MediaCate = &mediaCate
	return b
}

func (b *UploadMediaCommandBuilder) WithPoliceNo(policeNo string) *UploadMediaCommandBuilder {
	b.command.PoliceNo = policeNo
	return b
}

func (b *UploadMediaCommandBuilder) WithRecorderNo(recorderNo string) *UploadMediaCommandBuilder {
	b.command.RecorderNo = recorderNo
	return b
}

func (b *UploadMediaCommandBuilder) WithAuthKey(authKey string) *UploadMediaCommandBuilder {
	b.command.AuthKey = authKey
	return b
}

func (b *UploadMediaCommandBuilder) WithRequestIdentity(requestIdentity string) *UploadMediaCommandBuilder {
	b.command.RequestIdentity = requestIdentity
	return b
}

func (b *UploadMediaCommandBuilder) WithImportantLevel(importantLevel int) *UploadMediaCommandBuilder {
	b.command.ImportantLevel = importantLevel
	return b
}

func (b *UploadMediaCommandBuilder) WithMediaSuffix(mediaSuffix string) *UploadMediaCommandBuilder {
	b.command.MediaSuffix = mediaSuffix
	return b
}

func (b *UploadMediaCommandBuilder) WithPoliceName(policeName string) *UploadMediaCommandBuilder {
	b.command.PoliceName = policeName
	return b
}

func (b *UploadMediaCommandBuilder) WithShotTimeStart(shotTimeStart time.Time) *UploadMediaCommandBuilder {
	b.command.ShotTimeStart = shotTimeStart
	return b
}

func (b *UploadMediaCommandBuilder) WithShotTime(shotTime time.Time) *UploadMediaCommandBuilder {
	b.command.ShotTime = shotTime
	return b
}

func (b *UploadMediaCommandBuilder) WithVideoClarity(videoClarity int) *UploadMediaCommandBuilder {
	b.command.VideoClarity = videoClarity
	return b
}

func (b *UploadMediaCommandBuilder) WithVideoDuration(videoDuration int) *UploadMediaCommandBuilder {
	b.command.VideoDuration = videoDuration
	return b
}

func (b *UploadMediaCommandBuilder) WithFileSize(fileSize int64) *UploadMediaCommandBuilder {
	b.command.FileSize = fileSize
	return b
}

func (b *UploadMediaCommandBuilder) WithOrgID(orgID int) *UploadMediaCommandBuilder {
	b.command.OrgID = orgID
	return b
}

func (b *UploadMediaCommandBuilder) WithOrgName(orgName string) *UploadMediaCommandBuilder {
	b.command.OrgName = orgName
	return b
}

func (b *UploadMediaCommandBuilder) WithStorageType(storageType int) *UploadMediaCommandBuilder {
	b.command.StorageType = storageType
	return b
}

func (b *UploadMediaCommandBuilder) WithSiteID(siteID int) *UploadMediaCommandBuilder {
	b.command.SiteID = siteID
	return b
}

func (b *UploadMediaCommandBuilder) WithStorageID(storageID int) *UploadMediaCommandBuilder {
	b.command.StorageID = storageID
	return b
}

func (b *UploadMediaCommandBuilder) WithSiteClientID(siteClientID int) *UploadMediaCommandBuilder {
	b.command.SiteClientID = siteClientID
	return b
}

func (b *UploadMediaCommandBuilder) WithTrialID(trialID int) *UploadMediaCommandBuilder {
	b.command.TrialID = trialID
	return b
}

func (b *UploadMediaCommandBuilder) WithImportTime(importTime *time.Time) *UploadMediaCommandBuilder {
	b.command.ImportTime = importTime
	return b
}

func (b *UploadMediaCommandBuilder) WithComments(comments string) *UploadMediaCommandBuilder {
	b.command.Comments = comments
	return b
}

func (b *UploadMediaCommandBuilder) Build() *command.UploadMediaCommand {
	return b.command
}

// UpdateMediaCommandBuilder 更新媒体构建器

type UpdateMediaCommandBuilder struct {
	command *command.UpdateMediaCommand
}

func NewUpdateMediaCommandBuilder() *UpdateMediaCommandBuilder {
	return &UpdateMediaCommandBuilder{
		command: &command.UpdateMediaCommand{
			ID: 1, // 设置一个默认ID值
		},
	}
}

func (b *UpdateMediaCommandBuilder) WithID(id int64) *UpdateMediaCommandBuilder {
	b.command.ID = id
	return b
}

func (b *UpdateMediaCommandBuilder) WithMediaCate(mediaCate int) *UpdateMediaCommandBuilder {
	b.command.MediaCate = &mediaCate
	return b
}

func (b *UpdateMediaCommandBuilder) WithPoliceNo(policeNo string) *UpdateMediaCommandBuilder {
	b.command.PoliceNo = &policeNo
	return b
}

func (b *UpdateMediaCommandBuilder) WithRecordeNo(recordeNo string) *UpdateMediaCommandBuilder {
	b.command.RecordeNo = &recordeNo
	return b
}

func (b *UpdateMediaCommandBuilder) WithAuthKey(authKey string) *UpdateMediaCommandBuilder {
	b.command.AuthKey = authKey
	return b
}

func (b *UpdateMediaCommandBuilder) WithRequestIdentity(requestIdentity string) *UpdateMediaCommandBuilder {
	b.command.RequestIdentity = requestIdentity
	return b
}

func (b *UpdateMediaCommandBuilder) WithImportantLevel(importantLevel int) *UpdateMediaCommandBuilder {
	b.command.ImportantLevel = &importantLevel
	return b
}

func (b *UpdateMediaCommandBuilder) WithMediaSuffix(mediaSuffix string) *UpdateMediaCommandBuilder {
	b.command.MediaSuffix = &mediaSuffix
	return b
}

func (b *UpdateMediaCommandBuilder) WithPoliceName(policeName string) *UpdateMediaCommandBuilder {
	b.command.PoliceName = &policeName
	return b
}

func (b *UpdateMediaCommandBuilder) WithShotTimeStart(shotTimeStart time.Time) *UpdateMediaCommandBuilder {
	b.command.ShotTimeStart = &shotTimeStart
	return b
}

func (b *UpdateMediaCommandBuilder) WithShotTime(shotTime time.Time) *UpdateMediaCommandBuilder {
	b.command.ShotTime = &shotTime
	return b
}

func (b *UpdateMediaCommandBuilder) WithVideoClarity(videoClarity int) *UpdateMediaCommandBuilder {
	b.command.VideoClarity = &videoClarity
	return b
}

func (b *UpdateMediaCommandBuilder) WithVideoDuration(videoDuration int) *UpdateMediaCommandBuilder {
	b.command.VideoDuration = &videoDuration
	return b
}

func (b *UpdateMediaCommandBuilder) WithFileSize(fileSize int64) *UpdateMediaCommandBuilder {
	b.command.FileSize = &fileSize
	return b
}

func (b *UpdateMediaCommandBuilder) WithOrgID(orgID int) *UpdateMediaCommandBuilder {
	b.command.OrgID = &orgID
	return b
}

func (b *UpdateMediaCommandBuilder) WithOrgName(orgName string) *UpdateMediaCommandBuilder {
	b.command.OrgName = &orgName
	return b
}

func (b *UpdateMediaCommandBuilder) WithStorageType(storageType int) *UpdateMediaCommandBuilder {
	b.command.StorageType = &storageType
	return b
}

func (b *UpdateMediaCommandBuilder) WithSiteID(siteID int) *UpdateMediaCommandBuilder {
	b.command.SiteID = &siteID
	return b
}

func (b *UpdateMediaCommandBuilder) WithStorageID(storageID int) *UpdateMediaCommandBuilder {
	b.command.StorageID = &storageID
	return b
}

func (b *UpdateMediaCommandBuilder) WithSiteClientID(siteClientID int) *UpdateMediaCommandBuilder {
	b.command.SiteClientID = &siteClientID
	return b
}

func (b *UpdateMediaCommandBuilder) WithTrialID(trialID int) *UpdateMediaCommandBuilder {
	b.command.TrialID = &trialID
	return b
}

func (b *UpdateMediaCommandBuilder) WithComments(comments string) *UpdateMediaCommandBuilder {
	b.command.Comments = &comments
	return b
}

func (b *UpdateMediaCommandBuilder) Build() *command.UpdateMediaCommand {
	return b.command
}

// BatchUpdateMediaCommandBuilder 批量更新媒体构建器

type BatchUpdateMediaCommandBuilder struct {
	command *command.BatchUpdateMediaCommand
}

func NewBatchUpdateMediaCommandBuilder() *BatchUpdateMediaCommandBuilder {
	return &BatchUpdateMediaCommandBuilder{
		command: &command.BatchUpdateMediaCommand{},
	}
}

func (b *BatchUpdateMediaCommandBuilder) WithIDs(ids []int64) *BatchUpdateMediaCommandBuilder {
	b.command.IDs = ids
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithImportantLevel(importantLevel int) *BatchUpdateMediaCommandBuilder {
	b.command.ImportantLevel = &importantLevel
	return b
}

/*
func (b *BatchUpdateMediaCommandBuilder) WithMediaCate(mediaCate int) *BatchUpdateMediaCommandBuilder {
	b.command.MediaCate = &mediaCate
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithMediaSuffix(mediaSuffix string) *BatchUpdateMediaCommandBuilder {
	b.command.MediaSuffix = &mediaSuffix
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithShotTimeStart(shotTimeStart time.Time) *BatchUpdateMediaCommandBuilder {
	b.command.ShotTimeStart = &shotTimeStart
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithShotTime(shotTime time.Time) *BatchUpdateMediaCommandBuilder {
	b.command.ShotTime = &shotTime
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithVideoClarity(videoClarity int) *BatchUpdateMediaCommandBuilder {
	b.command.VideoClarity = &videoClarity
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithVideoDuration(videoDuration int) *BatchUpdateMediaCommandBuilder {
	b.command.VideoDuration = &videoDuration
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithFileSize(fileSize int64) *BatchUpdateMediaCommandBuilder {
	b.command.FileSize = &fileSize
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithOrgID(orgID int) *BatchUpdateMediaCommandBuilder {
	b.command.OrgID = &orgID
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithStorageType(storageType int) *BatchUpdateMediaCommandBuilder {
	b.command.StorageType = &storageType
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithSiteID(siteID int) *BatchUpdateMediaCommandBuilder {
	b.command.SiteID = &siteID
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithStorageID(storageID int) *BatchUpdateMediaCommandBuilder {
	b.command.StorageID = &storageID
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithSiteClientID(siteClientID int) *BatchUpdateMediaCommandBuilder {
	b.command.SiteClientID = &siteClientID
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithTrialID(trialID int) *BatchUpdateMediaCommandBuilder {
	b.command.TrialID = &trialID
	return b
}
*/

func (b *BatchUpdateMediaCommandBuilder) WithComments(comments string) *BatchUpdateMediaCommandBuilder {
	b.command.Comments = &comments
	return b
}

func (b *BatchUpdateMediaCommandBuilder) WithExpiryTime(expiryTime time.Time) *BatchUpdateMediaCommandBuilder {
	b.command.ExpiryTime = &expiryTime
	return b
}

func (b *BatchUpdateMediaCommandBuilder) Build() *command.BatchUpdateMediaCommand {
	return b.command
}

// BatchDeleteMediaCommandBuilder 批量删除媒体构建器

type BatchDeleteMediaCommandBuilder struct {
	command *command.BatchDeleteMediaCommand
}

func NewBatchDeleteMediaCommandBuilder() *BatchDeleteMediaCommandBuilder {
	return &BatchDeleteMediaCommandBuilder{
		command: &command.BatchDeleteMediaCommand{},
	}
}

func (b *BatchDeleteMediaCommandBuilder) WithIDs(ids []int64) *BatchDeleteMediaCommandBuilder {
	b.command.IDs = ids
	return b
}

func (b *BatchDeleteMediaCommandBuilder) Build() *command.BatchDeleteMediaCommand {
	return b.command
}

// RemarkNoEnforMediaCommandBuilder 标记非执法媒体构建器

type BatchUpdateNonEnforcementStatusCommandBuilder struct {
	command *command.BatchUpdateNonEnforcementStatusCommand
}

func NewBatchUpdateNonEnforcementStatusCommandBuilder() *BatchUpdateNonEnforcementStatusCommandBuilder {
	return &BatchUpdateNonEnforcementStatusCommandBuilder{
		command: &command.BatchUpdateNonEnforcementStatusCommand{},
	}
}

func (b *BatchUpdateNonEnforcementStatusCommandBuilder) WithIDs(ids []int64) *BatchUpdateNonEnforcementStatusCommandBuilder {
	b.command.IDs = ids
	return b
}

func (b *BatchUpdateNonEnforcementStatusCommandBuilder) WithIsNonEnforcementMedia(isNonEnforcementMedia int) *BatchUpdateNonEnforcementStatusCommandBuilder {
	b.command.IsNonEnforcementMedia = isNonEnforcementMedia
	return b
}

func (b *BatchUpdateNonEnforcementStatusCommandBuilder) Build() *command.BatchUpdateNonEnforcementStatusCommand {
	return b.command
}

// MediaBuilder 媒体构建器

type MediaBuilder struct {
	media *media.Media
}

func NewMediaBuilder() *MediaBuilder {
	now := time.Now()
	return &MediaBuilder{
		media: &media.Media{
			PoliceID:       1,
			RecorderID:     1,
			ImportantLevel: 1,
			MediaSuffix:    "mp4",
			ShotTimeStart:  now,
			ShotTime:       now.Add(time.Hour),
			VideoClarity:   1,
			VideoDuration:  3600000,
			FileSize:       1024,
			OrgID:          1,
			StorageType:    0,
			SiteID:         1,
			StorageID:      1,
			SiteClientID:   1,
			TrialID:        1,
			Comments:       "测试媒体",
		},
	}
}

func (b *MediaBuilder) WithMediaName(mediaName string) *MediaBuilder {
	b.media.MediaName = mediaName
	return b
}

func (b *MediaBuilder) WithMediaCate(mediaCate int) *MediaBuilder {
	b.media.MediaCate = mediaCate
	return b
}

func (b *MediaBuilder) WithPoliceID(policeID int) *MediaBuilder {
	b.media.PoliceID = policeID
	return b
}

func (b *MediaBuilder) WithRecorderID(recorderId int) *MediaBuilder {
	b.media.RecorderID = recorderId
	return b
}

func (b *MediaBuilder) WithImportantLevel(importantLevel int) *MediaBuilder {
	b.media.ImportantLevel = importantLevel
	return b
}

func (b *MediaBuilder) WithMediaSuffix(mediaSuffix string) *MediaBuilder {
	b.media.MediaSuffix = mediaSuffix
	return b
}

func (b *MediaBuilder) WithShotTimeStart(shotTimeStart time.Time) *MediaBuilder {
	b.media.ShotTimeStart = shotTimeStart
	return b
}

func (b *MediaBuilder) WithShotTime(shotTime time.Time) *MediaBuilder {
	b.media.ShotTime = shotTime
	return b
}

func (b *MediaBuilder) WithVideoClarity(videoClarity int) *MediaBuilder {
	b.media.VideoClarity = videoClarity
	return b
}

func (b *MediaBuilder) WithVideoDuration(videoDuration int) *MediaBuilder {
	b.media.VideoDuration = videoDuration
	return b
}

func (b *MediaBuilder) WithFileSize(fileSize int64) *MediaBuilder {
	b.media.FileSize = fileSize
	return b
}

func (b *MediaBuilder) WithOrgID(orgID int) *MediaBuilder {
	b.media.OrgID = orgID
	return b
}

func (b *MediaBuilder) WithStorageType(storageType int) *MediaBuilder {
	b.media.StorageType = storageType
	return b
}

func (b *MediaBuilder) WithSiteID(siteID int) *MediaBuilder {
	b.media.SiteID = siteID
	return b
}

func (b *MediaBuilder) WithStorageID(storageID int) *MediaBuilder {
	b.media.StorageID = storageID
	return b
}

func (b *MediaBuilder) WithSiteClientID(siteClientID int) *MediaBuilder {
	b.media.SiteClientID = siteClientID
	return b
}

func (b *MediaBuilder) WithTrialID(trialID int) *MediaBuilder {
	b.media.TrialID = trialID
	return b
}

func (b *MediaBuilder) WithImportTime(importTime *time.Time) *MediaBuilder {
	b.media.ImportTime = importTime
	return b
}

func (b *MediaBuilder) WithComments(comments string) *MediaBuilder {
	b.media.Comments = comments
	return b
}

func (b *MediaBuilder) WithFileIdentity(fileIdentity string) *MediaBuilder {
	b.media.FileIdentity = fileIdentity
	return b
}

func (b *MediaBuilder) WithFileName(fileName string) *MediaBuilder {
	b.media.FileName = fileName
	return b
}

func (b *MediaBuilder) WithFileMd5(fileMd5 string) *MediaBuilder {
	b.media.FileMd5 = fileMd5
	return b
}

func (b *MediaBuilder) WithFileType(fileType int) *MediaBuilder {
	b.media.FileType = fileType
	return b
}

func (b *MediaBuilder) WithThumbnail(thumbnail string) *MediaBuilder {
	b.media.Thumbnail = thumbnail
	return b
}

func (b *MediaBuilder) WithTraceCode(traceCode string) *MediaBuilder {
	b.media.TraceCode = traceCode
	return b
}

func (b *MediaBuilder) WithRequestIdentity(requestIdentity string) *MediaBuilder {
	b.media.RequestIdentity = requestIdentity
	return b
}

func (b *MediaBuilder) WithAuthKey(authKey string) *MediaBuilder {
	b.media.AuthKey = authKey
	return b
}

func (b *MediaBuilder) WithAcquisitionTime(acquisitionTime *time.Time) *MediaBuilder {
	b.media.AcquisitionTime = acquisitionTime
	return b
}

func (b *MediaBuilder) Build() *media.Media {
	return b.media
}

// MarkLawEnforMediaStatusCommandBuilder 标记执法媒体状态命令构建器
type MarkLawEnforMediaStatusCommandBuilder struct {
	command *command.BatchUpdateNonEnforcementStatusCommand
}

// NewMarkLawEnforMediaStatusCommandBuilder 创建一个新的标记执法媒体状态命令构建器
func NewMarkLawEnforMediaStatusCommandBuilder() *MarkLawEnforMediaStatusCommandBuilder {
	return &MarkLawEnforMediaStatusCommandBuilder{
		command: &command.BatchUpdateNonEnforcementStatusCommand{},
	}
}

// WithIDs 设置媒体ID列表
func (b *MarkLawEnforMediaStatusCommandBuilder) WithIDs(ids []int64) *MarkLawEnforMediaStatusCommandBuilder {
	b.command.IDs = ids
	return b
}

// WithIsEnforMedia 设置是否为执法媒体（0-非执法媒体，1-执法媒体）
func (b *MarkLawEnforMediaStatusCommandBuilder) WithIsNonEnforcementMedia(isEnforMedia int) *MarkLawEnforMediaStatusCommandBuilder {
	b.command.IsNonEnforcementMedia = isEnforMedia
	return b
}

// WithUpdateBy 设置更新者ID
func (b *MarkLawEnforMediaStatusCommandBuilder) WithUpdateBy(updateBy int) *MarkLawEnforMediaStatusCommandBuilder {
	b.command.UpdateBy = updateBy
	return b
}

// Build 构建命令对象
func (b *MarkLawEnforMediaStatusCommandBuilder) Build() *command.BatchUpdateNonEnforcementStatusCommand {
	// 确保必要字段已设置
	if b.command.IDs == nil {
		b.command.IDs = []int64{}
	}
	// 确保 IsNonEnforcementMedia 字段被设置为有效值（0或1）
	// 1表示非执法媒体，0表示执法媒体
	if b.command.IsNonEnforcementMedia != 0 && b.command.IsNonEnforcementMedia != 1 {
		b.command.IsNonEnforcementMedia = 0 // 默认设置为非执法媒体
	}
	// 确保 UpdateBy 字段被设置
	if b.command.UpdateBy == 0 {
		b.command.UpdateBy = 1 // 默认设置为系统管理员
	}
	return b.command
}

// BatchUpdateEnforceTypeCommandBuilder 批量更新执法类型构建器
type BatchUpdateEnforceTypeCommandBuilder struct {
	command *command.BatchUpdateEnforceTypeCommand
}

func NewBatchUpdateEnforceTypeCommandBuilder() *BatchUpdateEnforceTypeCommandBuilder {
	return &BatchUpdateEnforceTypeCommandBuilder{
		command: &command.BatchUpdateEnforceTypeCommand{
			IDs:         []int64{1}, // 默认包含一个ID
			EnforceType: 0,          // 默认执法类型为0
		},
	}
}

func (b *BatchUpdateEnforceTypeCommandBuilder) WithIDs(ids []int64) *BatchUpdateEnforceTypeCommandBuilder {
	b.command.IDs = ids
	return b
}

func (b *BatchUpdateEnforceTypeCommandBuilder) WithEnforceType(enforceType int) *BatchUpdateEnforceTypeCommandBuilder {
	b.command.EnforceType = enforceType
	return b
}

func (b *BatchUpdateEnforceTypeCommandBuilder) Build() *command.BatchUpdateEnforceTypeCommand {
	return b.command
}

// BatchUpdateIsLockedCommandBuilder 批量更新锁定状态构建器
type BatchUpdateIsLockedCommandBuilder struct {
	command *command.BatchUpdateIsLockedCommand
}

func NewBatchUpdateIsLockedCommandBuilder() *BatchUpdateIsLockedCommandBuilder {
	return &BatchUpdateIsLockedCommandBuilder{
		command: &command.BatchUpdateIsLockedCommand{
			IDs:      []int64{1}, // 默认包含一个ID
			IsLocked: 0,          // 默认锁定状态为0（未锁定）
		},
	}
}

func (b *BatchUpdateIsLockedCommandBuilder) WithIDs(ids []int64) *BatchUpdateIsLockedCommandBuilder {
	b.command.IDs = ids
	return b
}

func (b *BatchUpdateIsLockedCommandBuilder) WithIsLocked(isLocked int) *BatchUpdateIsLockedCommandBuilder {
	b.command.IsLocked = isLocked
	return b
}

func (b *BatchUpdateIsLockedCommandBuilder) Build() *command.BatchUpdateIsLockedCommand {
	return b.command
}
