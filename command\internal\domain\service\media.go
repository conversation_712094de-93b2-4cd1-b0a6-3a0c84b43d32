package domain_service

import (
	"context"
	"errors"
	"fmt"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/media/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"
	domain_event "jxt-evidence-system/evidence-management/shared/domain/event"
	"strings"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	jsoniter "github.com/json-iterator/go"

	"go.uber.org/zap"
)

func init() {
	registrations = append(registrations, registerMediaDomainServiceDependencies)
}

func registerMediaDomainServiceDependencies() {
	err := di.Provide(func(repo repository.MediaRepository) MediaDomainService {
		return &mediaDomainService{
			repo:   repo,
			events: make([]domain_event.Event, 0),
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide MediaDomainService: %v", err)
	}
}

// MediaDomainService 媒体领域服务接口
type MediaDomainService interface {
	// BatchUpdateMedia 批量更新媒体
	BatchUpdateMedia(ctx context.Context, ids []int64, updates map[string]interface{}) (context.Context, []int64, error)

	// BatchDeleteMedia 批量删除媒体
	BatchDeleteMedia(ctx context.Context, ids []int64, updateBy int) (context.Context, []int64, error)

	// BatchUpdateNonEnforcementStatus 批量更新非执法媒体状态
	BatchUpdateNonEnforcementStatus(ctx context.Context, ids []int64, isNonEnforcementMedia int, updateBy int, updatedAt time.Time) (context.Context, []int64, error)

	// BatchUpdateEnforceType 批量更新执法类型
	BatchUpdateEnforceType(ctx context.Context, ids []int64, enforceType int, updateBy int, updatedAt time.Time) (context.Context, []int64, error)

	// BatchUpdateIsLocked 批量更新锁定状态
	BatchUpdateIsLocked(ctx context.Context, ids []int64, isLocked int, updateBy int, updatedAt time.Time) (context.Context, []int64, error)
}

// mediaDomainService 媒体领域服务实现，不负责持久化操作，领域服务必须无状态
type mediaDomainService struct {
	repo   repository.MediaRepository
	events []domain_event.Event
}

func NewMediaDomainService(repo repository.MediaRepository) MediaDomainService {
	return &mediaDomainService{
		repo:   repo,
		events: make([]domain_event.Event, 0),
	}
}

// BatchUpdateMedia 批量更新媒体
func (s *mediaDomainService) BatchUpdateMedia(ctx context.Context, ids []int64, updates map[string]interface{}) (context.Context, []int64, error) {
	if len(ids) == 0 {
		return ctx, nil, errors.New("媒体ID列表不能为空")
	}

	if len(updates) == 0 {
		return ctx, nil, errors.New("更新字段不能为空")
	}

	// 批量更新的领域逻辑
	// 1. 验证更新字段的合法性
	//for field := range updates {
	//	// 这里可以添加字段验证逻辑
	//	// 例如，检查字段名是否合法，字段值是否符合业务规则等
	//}

	// 验证媒体是否上锁,过滤掉已上锁的媒体
	var unlockedIds []int64
	for _, id := range ids {
		media, err := s.repo.FindByID(ctx, id)
		if err != nil {
			logger.Error("查询媒体失败", zap.Error(err), zap.Int64("mediaId", id))
			continue
		}

		if media == nil {
			logger.Warn("媒体不存在，无法更新", zap.Int64("mediaId", id))
			continue
		}

		// 如果媒体未上锁,则加入到待更新列表
		if media.IsLocked == 1 {
			logger.Warn("媒体已上锁,跳过更新", zap.Int64("mediaId", id))
			continue
		}
		unlockedIds = append(unlockedIds, id)

	}

	// 更新ids为未上锁的媒体ID列表
	ids = unlockedIds

	// 如果过滤后没有可更新的媒体,则返回
	if len(ids) == 0 {
		logger.Error("没有可更新的媒体")
		return ctx, nil, errors.New("没有可更新的媒体")
	}

	// 2. 添加审计字段
	if _, ok := updates["UpdateBy"]; !ok {
		// 如果没有提供更新人，可以从上下文中获取或设置默认值
		updates["UpdateBy"] = 0 // 默认值，实际应用中应从上下文获取
	}

	if _, ok := updates["UpdatedAt"]; !ok {
		updates["UpdatedAt"] = time.Now()
	}

	// 创建批量更新事件
	updateEvent := s.createMediaBatchUpdatedEvent(ids, updates)
	if updateEvent == nil {
		logger.Error("创建批量更新事件失败")
		return ctx, nil, errors.New("创建批量更新事件失败")
	}

	// 将事件添加到上下文中
	newCtx := domain_event.AddEventToContext(ctx, updateEvent)

	return newCtx, ids, nil
}

// BatchDeleteMedia 批量删除媒体
func (s *mediaDomainService) BatchDeleteMedia(ctx context.Context, ids []int64, updateBy int) (context.Context, []int64, error) {
	if len(ids) == 0 {
		logger.Error("媒体ID列表不能为空")
		return ctx, nil, errors.New("媒体ID列表不能为空")
	}

	var deleteIds []int64

	// 批量删除的领域逻辑
	// 1. 验证是否可以删除
	for _, id := range ids {
		// 查询媒体是否存在
		media, err := s.repo.FindByID(ctx, id)
		if err != nil {
			logger.Error("查询媒体失败", zap.Error(err), zap.Int64("mediaId", id))
			continue
		}

		if media == nil {
			logger.Warn("媒体不存在，无法删除", zap.Int64("mediaId", id))
			continue
		}

		// 已上锁的媒体不能删除
		if media.IsLocked == 1 {
			logger.Warn("媒体已上锁，不能删除", zap.Int64("mediaId", id))
			continue
		}
		// 已归档的媒体不能删除
		if media.IsArchived == 1 {
			logger.Warn("媒体已归档，不能删除", zap.Int64("mediaId", id))
			continue
		}

		deleteIds = append(deleteIds, id)
	}

	// 如果没有可删除的媒体，返回错误
	if len(deleteIds) == 0 {
		logger.Error("没有可删除的媒体")
		return ctx, nil, errors.New("没有可删除的媒体")
	}

	// 创建批量删除事件
	deleteEvent := s.createMediaBatchDeletedEvent(deleteIds, updateBy)
	if deleteEvent == nil {
		logger.Error("创建批量删除事件失败")
		return ctx, nil, errors.New("创建批量删除事件失败")
	}

	// 将事件添加到上下文中
	newCtx := domain_event.AddEventToContext(ctx, deleteEvent)

	return newCtx, deleteIds, nil
}

// BatchUpdateNonEnforcementStatus 批量更新非执法媒体状态
func (s *mediaDomainService) BatchUpdateNonEnforcementStatus(ctx context.Context, ids []int64, isNonEnforcementMedia int, updateBy int, updatedAt time.Time) (context.Context, []int64, error) {
	if len(ids) == 0 {
		return ctx, nil, errors.New("媒体ID列表不能为空")
	}

	// 验证isNonEnforcementMedia参数
	if isNonEnforcementMedia != 0 && isNonEnforcementMedia != 1 {
		return ctx, nil, errors.New("非执法媒体状态参数无效，必须为0或1")
	}

	// 验证媒体是否上锁,过滤掉已上锁的媒体
	var unlockedIds []int64
	for _, id := range ids {
		media, err := s.repo.FindByID(ctx, id)
		if err != nil {
			logger.Error("查询媒体失败", zap.Error(err), zap.Int64("mediaId", id))
			continue
		}

		if media == nil {
			logger.Warn("媒体不存在，无法设置非执法媒体状态", zap.Int64("mediaId", id))
			continue
		}

		if media.IsLocked == 1 {
			logger.Warn("媒体已上锁，无法设置非执法媒体状态", zap.Int64("mediaId", id))
			continue
		}

		if media.IsArchived == 1 {
			logger.Warn("媒体已归档，无法设置非执法媒体状态", zap.Int64("mediaId", id))
			continue
		}

		unlockedIds = append(unlockedIds, id)
	}

	// 更新ids为未上锁的媒体ID列表
	ids = unlockedIds

	// 如果过滤后没有可更新的媒体,则返回
	if len(ids) == 0 {
		logger.Error("没有可设置非执法媒体状态的媒体")
		return ctx, nil, errors.New("没有可设置非执法媒体状态的媒体")
	}

	// 创建批量设置执法媒体状态事件
	lawEnforcementEvent := s.createMediaBatchNonEnforcementSetEvent(ids, isNonEnforcementMedia, updateBy, updatedAt)
	if lawEnforcementEvent == nil {
		logger.Error("创建批量设置执法媒体状态事件失败")
		return ctx, nil, errors.New("创建批量设置执法媒体状态事件失败")
	}

	// 将事件添加到上下文中并返回新的上下文
	newCtx := domain_event.AddEventToContext(ctx, lawEnforcementEvent)

	return newCtx, ids, nil
}

// BatchUpdateEnforceType 批量更新执法类型
func (s *mediaDomainService) BatchUpdateEnforceType(ctx context.Context, ids []int64, enforceType int, updateBy int, updatedAt time.Time) (context.Context, []int64, error) {
	if len(ids) == 0 {
		return ctx, nil, errors.New("媒体ID列表不能为空")
	}

	var unlockedIds []int64

	// 过滤掉已锁定的媒体
	for _, id := range ids {
		media, err := s.repo.FindByID(ctx, id)
		if err != nil {
			logger.Error("查询媒体失败", zap.Error(err), zap.Int64("mediaId", id))
			continue
		}

		if media == nil {
			logger.Warn("媒体不存在，无法更新", zap.Int64("mediaId", id))
			continue
		}

		// 如果媒体未上锁，则加入到待更新列表
		if media.IsLocked == 1 {
			logger.Warn("媒体已上锁,跳过更新", zap.Int64("mediaId", id))
			continue
		}
		unlockedIds = append(unlockedIds, id)
	}

	if len(unlockedIds) == 0 {
		logger.Error("没有可更新执法类型的媒体")
		return ctx, nil, errors.New("没有可更新执法类型的媒体")
	}

	// 创建批量更新执法类型事件
	enforceTypeEvent := s.createMediaBatchEnforceTypeUpdatedEvent(unlockedIds, enforceType, updateBy, updatedAt)
	if enforceTypeEvent == nil {
		logger.Error("创建批量更新执法类型事件失败")
		return ctx, nil, errors.New("创建批量更新执法类型事件失败")
	}

	// 将事件添加到上下文中
	newCtx := domain_event.AddEventToContext(ctx, enforceTypeEvent)

	return newCtx, ids, nil
}

// BatchUpdateIsLocked 批量更新锁定状态 - 在领域服务中协调批量业务逻辑
func (s *mediaDomainService) BatchUpdateIsLocked(ctx context.Context, ids []int64, isLocked int, updateBy int, updatedAt time.Time) (context.Context, []int64, error) {
	if len(ids) == 0 {
		return ctx, nil, errors.New("媒体ID列表不能为空")
	}

	var validIds []int64
	var errorMessages []string

	// 在领域服务中协调批量操作的业务逻辑
	for _, id := range ids {
		media, err := s.repo.FindByID(ctx, id)
		if err != nil {
			errorMessages = append(errorMessages, fmt.Sprintf("媒体ID:%d查询失败:%s", id, err.Error()))
			logger.Error("查询媒体失败", zap.Error(err), zap.Int64("mediaId", id))
			continue
		}

		if media == nil {
			errorMessages = append(errorMessages, fmt.Sprintf("媒体ID:%d不存在", id))
			logger.Warn("媒体不存在，无法更新", zap.Int64("mediaId", id))
			continue
		}

		// 调用聚合根方法验证业务规则（权限检查等）
		err = media.UpdateIsLockedStatus(updateBy, isLocked)
		if err != nil {
			errorMessages = append(errorMessages, fmt.Sprintf("媒体ID:%d更新失败:%s", id, err.Error()))
			logger.Warn("媒体更新锁定状态失败", zap.Error(err), zap.Int64("mediaId", id))
			continue
		}

		validIds = append(validIds, id)
	}

	if len(validIds) == 0 {
		errorMessage := fmt.Sprintf("没有可更新锁定状态的媒体: %s", strings.Join(errorMessages, "; "))
		logger.Error("批量更新锁定状态失败", zap.String("errors", errorMessage))
		return ctx, nil, errors.New(errorMessage)
	}

	// 创建批量更新锁定状态事件
	isLockedEvent := s.createMediaBatchIsLockedUpdatedEvent(validIds, isLocked, updateBy, updatedAt)
	if isLockedEvent == nil {
		logger.Error("创建批量更新锁定状态事件失败")
		return ctx, nil, errors.New("创建批量更新锁定状态事件失败")
	}

	// 将事件添加到上下文中
	newCtx := domain_event.AddEventToContext(ctx, isLockedEvent)

	// 如果有部分失败，记录到上下文中供应用服务处理
	if len(errorMessages) > 0 {
		newCtx = context.WithValue(newCtx, "partialErrors", errorMessages)
	}

	return newCtx, validIds, nil
}

func (s *mediaDomainService) createMediaBatchUpdatedEvent(ids []int64, updates map[string]interface{}) domain_event.Event {
	if len(ids) == 0 {
		logger.Error("媒体ID列表为空，无法创建批量更新事件")
		return nil
	}

	payload := domain_event.MediaBatchUpdatedPayload{
		MediaIDs:      ids,
		UpdatedFields: updates,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 MediaBatchUpdatedPayload 转换为 JSON", "error", err)
		return nil
	}

	// 使用第一个ID作为聚合根ID
	aggregateID := ids[0]
	return domain_event.NewDomainEvent(domain_event.EventTypeMediaBatchUpdated, aggregateID, "Media", payloadJSON)
}

func (s *mediaDomainService) createMediaBatchDeletedEvent(ids []int64, updateBy int) domain_event.Event {
	if len(ids) == 0 {
		logger.Error("媒体ID列表为空，无法创建批量删除事件")
		return nil
	}

	payload := domain_event.MediaBatchDeletedPayload{
		MediaIDs:  ids,
		UpdateBy:  updateBy,
		DeletedAt: time.Now(),
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 MediaBatchDeletedPayload 转换为 JSON", zap.Error(err))
		return nil
	}

	// 使用第一个ID作为聚合根ID
	aggregateID := ids[0]
	return domain_event.NewDomainEvent(domain_event.EventTypeMediaBatchDeleted, aggregateID, "Media", payloadJSON)
}

func (s *mediaDomainService) createMediaBatchNonEnforcementSetEvent(ids []int64, isNonEnforcementMedia int, updateBy int, updatedAt time.Time) domain_event.Event {
	payload := domain_event.MediaBatchIsNonLawEnforcementMediaUpdatedPayload{
		MediaIDs:     ids,
		IsEnforMedia: isNonEnforcementMedia,
		UpdateBy:     updateBy,
		UpdatedAt:    updatedAt,
	}
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		logger.Error("无法将 MediaBatchIsNonLawEnforcementMediaUpdatedPayload 转换为 JSON", "error", err)
		return nil
	}

	// 使用第一个ID作为聚合根ID
	aggregateID := ids[0]
	return domain_event.NewDomainEvent(domain_event.EventTypeMediaBatchIsNonLawEnforcementMediaUpdated, aggregateID, "Media", payloadJSON)
}

func (s *mediaDomainService) createMediaBatchEnforceTypeUpdatedEvent(ids []int64, enforceType int, updateBy int, updatedAt time.Time) domain_event.Event {
	if len(ids) == 0 {
		logger.Error("媒体ID列表为空，无法创建批量更新执法类型事件")
		return nil
	}

	payload := domain_event.MediaBatchEnforceTypeUpdatedPayload{
		MediaIDs:    ids,
		EnforceType: enforceType,
		UpdateBy:    updateBy,
		UpdatedAt:   updatedAt,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 MediaBatchEnforceTypeUpdatedPayload 转换为 JSON", "error", err)
		return nil
	}

	// 使用第一个ID作为聚合根ID
	aggregateID := ids[0]
	return domain_event.NewDomainEvent(domain_event.EventTypeMediaBatchEnforceTypeUpdated, aggregateID, "Media", payloadJSON)
}

func (s *mediaDomainService) createMediaBatchIsLockedUpdatedEvent(ids []int64, isLocked int, updateBy int, updatedAt time.Time) domain_event.Event {
	if len(ids) == 0 {
		logger.Error("媒体ID列表为空，无法创建批量更新锁定状态事件")
		return nil
	}

	payload := domain_event.MediaBatchIsLockedUpdatedPayload{
		MediaIDs:  ids,
		IsLocked:  isLocked,
		UpdateBy:  updateBy,
		UpdatedAt: updatedAt,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 MediaBatchIsLockedUpdatedPayload 转换为 JSON", "error", err)
		return nil
	}

	// 使用第一个ID作为聚合根ID
	aggregateID := ids[0]
	return domain_event.NewDomainEvent(domain_event.EventTypeMediaBatchIsLockedUpdated, aggregateID, "Media", payloadJSON)
}
