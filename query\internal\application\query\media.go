package query

import (
	"jxt-evidence-system/evidence-management/shared/common/query"
	"time"
)

type MediaPagedQuery struct {
	query.Pagination `search:"-"`
	//MediaId        []int  `form:"mediaId" search:"type:in;column:media_id;table:evidence_media"`
	MediaName       string    `form:"mediaName" search:"type:icontains;column:media_name;table:evidence_media"`
	MediaCate       int       `form:"mediaCate" search:"type:exact;column:media_cate;table:evidence_media"`
	ShotTimeStart   time.Time `form:"shotTimeStart" search:"type:gte;column:shot_time_start;table:evidence_media"`
	ShotTimeEnd     time.Time `form:"shotTimeEnd" search:"type:lte;column:shot_time_start;table:evidence_media"`
	ImportTimeStart time.Time `form:"importTimeStart" search:"type:gte;column:created_at;table:evidence_media"`
	ImportTimeEnd   time.Time `form:"importTimeEnd" search:"type:lte;column:created_at;table:evidence_media"`
	//SiteName        string    `form:"siteName" search:"type:icontains;column:site_name;table:app_site"`
	//StorageType     int    `form:"storageType" search:"type:exact;column:storage_type;table:evidence_media"`
	//DataSource      string `form:"dataSource" search:"type:icontains;column:data_source;table:evidence_media"`
	//RecorderNo      string `form:"recorderNo" search:"type:icontains;column:recorder_id;table:evidence_media"`
	//PoliceID        string `form:"policeId" search:"type:icontains;column:police_id;table:evidence_media"`
	//OrgID           string `form:"orgId" search:"type:icontains;column:org_id;table:evidence_media"`
	//IncludeSubUnits bool   `form:"includeSubUnits" search:"type:exact;column:include_sub_units;table:evidence_media"`
	MediaOrder
}

type MediaOrder struct {
	MediaName      string `search:"type:order;column:media_name;table:evidence_media" form:"mediaNameOrder"`
	MediaSuffix    string `search:"type:order;column:media_suffix;table:evidence_media" form:"mediaSuffixOrder"`
	CreatedAtOrder string `search:"type:order;column:created_at;table:evidence_media" form:"createdAtOrder"`
}

func (m *MediaPagedQuery) GetNeedSearch() interface{} {
	return *m
}
