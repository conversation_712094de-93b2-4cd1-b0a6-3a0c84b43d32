// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: equipment_management/interface/grpc/lawcamera/proto/lawcamera.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	LawcameraInfoService_GetLawcameraById_FullMethodName               = "/lawcamera.LawcameraInfoService/GetLawcameraById"
	LawcameraInfoService_GetLawcameraByNo_FullMethodName               = "/lawcamera.LawcameraInfoService/GetLawcameraByNo"
	LawcameraInfoService_GetLawcamerasByManagerId_FullMethodName       = "/lawcamera.LawcameraInfoService/GetLawcamerasByManagerId"
	LawcameraInfoService_GetLawcamerasByRequisitionerId_FullMethodName = "/lawcamera.LawcameraInfoService/GetLawcamerasByRequisitionerId"
)

// LawcameraInfoServiceClient is the client API for LawcameraInfoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 执法记录仪信息服务
type LawcameraInfoServiceClient interface {
	// 根据执法记录仪ID查询信息
	GetLawcameraById(ctx context.Context, in *GetLawcameraByIdReq, opts ...grpc.CallOption) (*LawcameraInfoReply, error)
	// 根据执法记录仪编号查询信息
	GetLawcameraByNo(ctx context.Context, in *GetLawcameraByNoReq, opts ...grpc.CallOption) (*LawcameraInfoReply, error)
	// 根据管理员ID查询执法记录仪列表
	GetLawcamerasByManagerId(ctx context.Context, in *GetLawcamerasByManagerIdReq, opts ...grpc.CallOption) (*LawcameraListReply, error)
	// 根据领用人ID查询执法记录仪列表
	GetLawcamerasByRequisitionerId(ctx context.Context, in *GetLawcamerasByRequisitionerIdReq, opts ...grpc.CallOption) (*LawcameraListReply, error)
}

type lawcameraInfoServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLawcameraInfoServiceClient(cc grpc.ClientConnInterface) LawcameraInfoServiceClient {
	return &lawcameraInfoServiceClient{cc}
}

func (c *lawcameraInfoServiceClient) GetLawcameraById(ctx context.Context, in *GetLawcameraByIdReq, opts ...grpc.CallOption) (*LawcameraInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LawcameraInfoReply)
	err := c.cc.Invoke(ctx, LawcameraInfoService_GetLawcameraById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lawcameraInfoServiceClient) GetLawcameraByNo(ctx context.Context, in *GetLawcameraByNoReq, opts ...grpc.CallOption) (*LawcameraInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LawcameraInfoReply)
	err := c.cc.Invoke(ctx, LawcameraInfoService_GetLawcameraByNo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lawcameraInfoServiceClient) GetLawcamerasByManagerId(ctx context.Context, in *GetLawcamerasByManagerIdReq, opts ...grpc.CallOption) (*LawcameraListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LawcameraListReply)
	err := c.cc.Invoke(ctx, LawcameraInfoService_GetLawcamerasByManagerId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lawcameraInfoServiceClient) GetLawcamerasByRequisitionerId(ctx context.Context, in *GetLawcamerasByRequisitionerIdReq, opts ...grpc.CallOption) (*LawcameraListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LawcameraListReply)
	err := c.cc.Invoke(ctx, LawcameraInfoService_GetLawcamerasByRequisitionerId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LawcameraInfoServiceServer is the server API for LawcameraInfoService service.
// All implementations must embed UnimplementedLawcameraInfoServiceServer
// for forward compatibility.
//
// 执法记录仪信息服务
type LawcameraInfoServiceServer interface {
	// 根据执法记录仪ID查询信息
	GetLawcameraById(context.Context, *GetLawcameraByIdReq) (*LawcameraInfoReply, error)
	// 根据执法记录仪编号查询信息
	GetLawcameraByNo(context.Context, *GetLawcameraByNoReq) (*LawcameraInfoReply, error)
	// 根据管理员ID查询执法记录仪列表
	GetLawcamerasByManagerId(context.Context, *GetLawcamerasByManagerIdReq) (*LawcameraListReply, error)
	// 根据领用人ID查询执法记录仪列表
	GetLawcamerasByRequisitionerId(context.Context, *GetLawcamerasByRequisitionerIdReq) (*LawcameraListReply, error)
	mustEmbedUnimplementedLawcameraInfoServiceServer()
}

// UnimplementedLawcameraInfoServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLawcameraInfoServiceServer struct{}

func (UnimplementedLawcameraInfoServiceServer) GetLawcameraById(context.Context, *GetLawcameraByIdReq) (*LawcameraInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLawcameraById not implemented")
}
func (UnimplementedLawcameraInfoServiceServer) GetLawcameraByNo(context.Context, *GetLawcameraByNoReq) (*LawcameraInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLawcameraByNo not implemented")
}
func (UnimplementedLawcameraInfoServiceServer) GetLawcamerasByManagerId(context.Context, *GetLawcamerasByManagerIdReq) (*LawcameraListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLawcamerasByManagerId not implemented")
}
func (UnimplementedLawcameraInfoServiceServer) GetLawcamerasByRequisitionerId(context.Context, *GetLawcamerasByRequisitionerIdReq) (*LawcameraListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLawcamerasByRequisitionerId not implemented")
}
func (UnimplementedLawcameraInfoServiceServer) mustEmbedUnimplementedLawcameraInfoServiceServer() {}
func (UnimplementedLawcameraInfoServiceServer) testEmbeddedByValue()                              {}

// UnsafeLawcameraInfoServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LawcameraInfoServiceServer will
// result in compilation errors.
type UnsafeLawcameraInfoServiceServer interface {
	mustEmbedUnimplementedLawcameraInfoServiceServer()
}

func RegisterLawcameraInfoServiceServer(s grpc.ServiceRegistrar, srv LawcameraInfoServiceServer) {
	// If the following call pancis, it indicates UnimplementedLawcameraInfoServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&LawcameraInfoService_ServiceDesc, srv)
}

func _LawcameraInfoService_GetLawcameraById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLawcameraByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LawcameraInfoServiceServer).GetLawcameraById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LawcameraInfoService_GetLawcameraById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LawcameraInfoServiceServer).GetLawcameraById(ctx, req.(*GetLawcameraByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LawcameraInfoService_GetLawcameraByNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLawcameraByNoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LawcameraInfoServiceServer).GetLawcameraByNo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LawcameraInfoService_GetLawcameraByNo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LawcameraInfoServiceServer).GetLawcameraByNo(ctx, req.(*GetLawcameraByNoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LawcameraInfoService_GetLawcamerasByManagerId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLawcamerasByManagerIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LawcameraInfoServiceServer).GetLawcamerasByManagerId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LawcameraInfoService_GetLawcamerasByManagerId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LawcameraInfoServiceServer).GetLawcamerasByManagerId(ctx, req.(*GetLawcamerasByManagerIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LawcameraInfoService_GetLawcamerasByRequisitionerId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLawcamerasByRequisitionerIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LawcameraInfoServiceServer).GetLawcamerasByRequisitionerId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LawcameraInfoService_GetLawcamerasByRequisitionerId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LawcameraInfoServiceServer).GetLawcamerasByRequisitionerId(ctx, req.(*GetLawcamerasByRequisitionerIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

// LawcameraInfoService_ServiceDesc is the grpc.ServiceDesc for LawcameraInfoService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LawcameraInfoService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "lawcamera.LawcameraInfoService",
	HandlerType: (*LawcameraInfoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLawcameraById",
			Handler:    _LawcameraInfoService_GetLawcameraById_Handler,
		},
		{
			MethodName: "GetLawcameraByNo",
			Handler:    _LawcameraInfoService_GetLawcameraByNo_Handler,
		},
		{
			MethodName: "GetLawcamerasByManagerId",
			Handler:    _LawcameraInfoService_GetLawcamerasByManagerId_Handler,
		},
		{
			MethodName: "GetLawcamerasByRequisitionerId",
			Handler:    _LawcameraInfoService_GetLawcamerasByRequisitionerId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "equipment_management/interface/grpc/lawcamera/proto/lawcamera.proto",
}
