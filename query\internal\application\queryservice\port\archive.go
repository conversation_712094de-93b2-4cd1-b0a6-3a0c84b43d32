package port

import (
	"context"
	query "jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/models"
)

// ArchiveQuery 档案查询接口
type ArchiveQuery interface {
	// GetPage 获取档案分页列表
	GetPage(ctx context.Context, r *query.ArchivePagedQuery) (list *[]models.ArchiveReadModel, count int64, err error)
	// GetByID 根据ID获取档案
	GetByID(ctx context.Context, id int64) (*models.ArchiveReadModel, error)
	// GetByCode 根据档案编码获取档案
	GetByCode(ctx context.Context, code string) (*models.ArchiveReadModel, error)
}
