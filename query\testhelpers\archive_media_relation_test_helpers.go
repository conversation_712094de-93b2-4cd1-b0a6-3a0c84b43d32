package testhelpers

import (
	"jxt-evidence-system/evidence-management/query/internal/models"
	commonModels "jxt-evidence-system/evidence-management/shared/common/models"
	"time"
)

// ArchiveMediaRelationReadModelBuilder 档案媒体关联读模型构建器
type ArchiveMediaRelationReadModelBuilder struct {
	model *models.ArchiveMediaRelationReadModel
}

func NewArchiveMediaRelationReadModelBuilder() *ArchiveMediaRelationReadModelBuilder {
	now := time.Now()
	return &ArchiveMediaRelationReadModelBuilder{
		model: &models.ArchiveMediaRelationReadModel{
			ID:        1,
			ArchiveId: 1,
			MediaId:   1,
			ControlBy: commonModels.ControlBy{
				CreateBy: 1,
				UpdateBy: 1,
			},
			ModelTime: commonModels.ModelTime{
				CreatedAt: now,
				UpdatedAt: now,
			},
		},
	}
}

func (b *ArchiveMediaRelationReadModelBuilder) WithID(id int64) *ArchiveMediaRelationReadModelBuilder {
	b.model.ID = id
	return b
}

func (b *ArchiveMediaRelationReadModelBuilder) WithArchiveId(archiveId int64) *ArchiveMediaRelationReadModelBuilder {
	b.model.ArchiveId = archiveId
	return b
}

func (b *ArchiveMediaRelationReadModelBuilder) WithMediaId(mediaId int64) *ArchiveMediaRelationReadModelBuilder {
	b.model.MediaId = mediaId
	return b
}

func (b *ArchiveMediaRelationReadModelBuilder) WithCreateBy(createBy int) *ArchiveMediaRelationReadModelBuilder {
	b.model.CreateBy = createBy
	return b
}

func (b *ArchiveMediaRelationReadModelBuilder) WithUpdateBy(updateBy int) *ArchiveMediaRelationReadModelBuilder {
	b.model.UpdateBy = updateBy
	return b
}

func (b *ArchiveMediaRelationReadModelBuilder) WithCreatedAt(createdAt time.Time) *ArchiveMediaRelationReadModelBuilder {
	b.model.CreatedAt = createdAt
	return b
}

func (b *ArchiveMediaRelationReadModelBuilder) WithUpdatedAt(updatedAt time.Time) *ArchiveMediaRelationReadModelBuilder {
	b.model.UpdatedAt = updatedAt
	return b
}

func (b *ArchiveMediaRelationReadModelBuilder) Build() *models.ArchiveMediaRelationReadModel {
	return b.model
}
