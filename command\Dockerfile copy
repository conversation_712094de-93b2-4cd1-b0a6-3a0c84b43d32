FROM golang:alpine as builder

LABEL maintainer="jxt"

ENV GOPROXY https://goproxy.cn/

WORKDIR /go/release
#RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk update && apk add tzdata

COPY go.mod ./go.mod

RUN go mod tidy
COPY . .
RUN pwd && ls

RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-w -s" -a -installsuffix cgo -o jxt-evidence-management .

FROM alpine

COPY --from=builder /go/release/jxt-evidence-management /

COPY --from=builder /go/release/config/settings.yml /config/settings.yml

COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 创建应用程序的日志目录
RUN mkdir -p /var/log/jxt-evidence-management

EXPOSE 8001

CMD ["/jxt-evidence-management","server","-c", "/config/settings.yml"]