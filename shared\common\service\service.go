package service

import (
	"context"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"go.uber.org/zap"
)

type Service struct{}

// GetLogger 获取上下文提供的日志
func (e *Service) GetLogger(ctx context.Context) *zap.Logger {
	// 从上下文中获取 logger
	requestLogger, ok := ctx.Value(logger.LoggerKey).(*zap.Logger)
	if !ok {
		// 如果没有找到 logger，使用默认 logger
		requestLogger = logger.Logger
	}
	return requestLogger
}
