package testhelpers

import (
	"net/http"

	. "github.com/onsi/gomega"
)

// TestSuite 测试套件基础结构
type TestSuite struct {
	BaseURL string
	Client  *http.Client
	Token   string
}

// SetupSuite 设置测试套件
func SetupSuite(baseURL string) *TestSuite {
	return &TestSuite{
		BaseURL: baseURL,
		Client:  &http.Client{},
	}
}

// SetToken 设置认证令牌
func (s *TestSuite) SetToken(token string) {
	s.Token = token
}

// AssertResponseSuccess 断言响应成功
func AssertResponseSuccess(response *http.Response) {
	Expect(response.StatusCode).To(BeNumerically(">=", 200))
	Expect(response.StatusCode).To(BeNumerically("<", 300))
}

// AssertResponseError 断言响应错误
func AssertResponseError(response *http.Response, expectedCode int) {
	Expect(response.StatusCode).To(Equal(expectedCode))
}

// AssertErrorMessage 断言错误消息
func AssertErrorMessage(response *http.Response, expectedMessage string) {
	body, err := GetResponseBody(response)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["msg"]).To(ContainSubstring(expectedMessage))
}
