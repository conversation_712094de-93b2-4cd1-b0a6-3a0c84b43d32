package router

import (
	"jxt-evidence-system/evidence-management/query/internal/interface/rest/api"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/middleware"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	jwt "github.com/ChenBigdata421/jxt-core/sdk/pkg/jwtauth"
	"github.com/gin-gonic/gin"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerEnforcementTypeRouter)
}

func registerEnforcementTypeRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	err := di.Invoke(func(handler *api.EnforcementTypeHandler) {
		if handler != nil {
			r := v1.Group("/enforcement-types").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
			{
				r.GET("/:id", handler.EnforcementTypeGetByID)
				r.GET("", handler.EnforcementTypeGetPage)
				r.GET("/tree", handler.EnforcementTypeListTree)
			}
		} else {
			logger.Fatal("EnforcementTypeHandler is nil after resolution")
		}
	})

	if err != nil {
		logger.Fatalf("Failed to resolve EnforcementTypeHandler: %v", err)
	}
}
