package eventhandler

import (
	"context"
	"fmt"
	"jxt-evidence-system/evidence-management/query/internal/models"
	"jxt-evidence-system/evidence-management/query/internal/models/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/global"
	client "jxt-evidence-system/evidence-management/shared/common/grpc/client/port"
	"jxt-evidence-system/evidence-management/shared/domain/event"
	"strings"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
	"github.com/ThreeDotsLabs/watermill/message"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	registrations = append(registrations, registerArchiveMediaRelationEventHandlerDependencies)
}

func registerArchiveMediaRelationEventHandlerDependencies() {
	err := di.Provide(func(subscriber EventSubscriber,
		relationRepo repository.ArchiveMediaRelationReadRepository,
		mediaRepo repository.MediaReadModelRepository,
		archiveRepo repository.ArchiveReadModelRepository,
		userInfoClient client.UserInfoServiceClient,
		orgInfoClient client.OrgInfoServiceClient,
		lawcameraInfoClient client.LawcameraInfoServiceClient) *ArchiveMediaRelationEventHandler {
		return NewArchiveMediaRelationEventHandler(subscriber, relationRepo, mediaRepo, archiveRepo, userInfoClient, orgInfoClient, lawcameraInfoClient)
	})
	if err != nil {
		logger.Error("Failed to provide ArchiveMediaRelationEventHandler", "error", err)
	}
}

type ArchiveMediaRelationEventHandler struct {
	Subscriber          EventSubscriber
	relationRepo        repository.ArchiveMediaRelationReadRepository
	mediaRepo           repository.MediaReadModelRepository
	archiveRepo         repository.ArchiveReadModelRepository
	userInfoClient      client.UserInfoServiceClient
	orgInfoClient       client.OrgInfoServiceClient
	lawcameraInfoClient client.LawcameraInfoServiceClient
}

func NewArchiveMediaRelationEventHandler(subscriber EventSubscriber,
	relationRepo repository.ArchiveMediaRelationReadRepository,
	mediaRepo repository.MediaReadModelRepository,
	archiveRepo repository.ArchiveReadModelRepository,
	userInfoClient client.UserInfoServiceClient,
	orgInfoClient client.OrgInfoServiceClient,
	lawcameraInfoClient client.LawcameraInfoServiceClient,
) *ArchiveMediaRelationEventHandler {
	return &ArchiveMediaRelationEventHandler{
		Subscriber:          subscriber,
		relationRepo:        relationRepo,
		mediaRepo:           mediaRepo,
		archiveRepo:         archiveRepo,
		userInfoClient:      userInfoClient,
		orgInfoClient:       orgInfoClient,
		lawcameraInfoClient: lawcameraInfoClient,
	}
}

// isConnectionClosingError 检查是否是连接关闭错误
func (h *ArchiveMediaRelationEventHandler) isConnectionClosingError(err error) bool {
	if err == nil {
		return false
	}
	errMsg := err.Error()
	return strings.Contains(errMsg, "connection is closing") ||
		strings.Contains(errMsg, "client connection is closing") ||
		strings.Contains(errMsg, "transport is closing") ||
		strings.Contains(errMsg, "context canceled") ||
		strings.Contains(errMsg, "rpc error: code = Canceled")
}

// retryWithExponentialBackoff 使用指数退避进行重试
func (h *ArchiveMediaRelationEventHandler) retryWithExponentialBackoff(ctx context.Context, operation func() error, maxRetries int) error {
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		err := operation()
		if err == nil {
			return nil
		}

		lastErr = err

		// 如果不是连接关闭错误，直接返回
		if !h.isConnectionClosingError(err) {
			logger.Warn("非连接错误，停止重试", "error", err, "attempt", attempt+1)
			return err
		}

		// 计算退避时间：1s, 2s, 4s, 8s, 16s
		backoffDuration := time.Duration(1<<uint(attempt)) * time.Second
		if backoffDuration > 30*time.Second {
			backoffDuration = 30 * time.Second // 最大30秒
		}

		logger.Warn("检测到连接关闭错误，将重试",
			"error", err,
			"attempt", attempt+1,
			"maxRetries", maxRetries,
			"backoff", backoffDuration)

		// 如果不是最后一次尝试，则等待
		if attempt < maxRetries-1 {
			timer := time.NewTimer(backoffDuration)
			select {
			case <-ctx.Done():
				timer.Stop()
				return ctx.Err()
			case <-timer.C:
				// 继续下一次重试
			}
		}
	}

	logger.Error("达到最大重试次数，仍然失败", "maxRetries", maxRetries, "lastError", lastErr)
	return fmt.Errorf("达到最大重试次数 %d，最后错误: %w", maxRetries, lastErr)
}

func (h *ArchiveMediaRelationEventHandler) ConsumeEvent(topic string) error {
	if err := h.Subscriber.Subscribe(topic, h.handleArchiveMediaRelationEvent, time.Second*30); err != nil {
		logger.Error("Failed to subscribe to topic", "topic", topic, "error", err)
		return err
	}

	return nil
}

func (h *ArchiveMediaRelationEventHandler) handleArchiveMediaRelationEvent(msg *message.Message) error {
	// Step 1: 反序列化为领域事件结构体
	domainEvent := &event.DomainEvent{}

	err := domainEvent.UnmarshalJSON(msg.Payload)
	if err != nil {
		return fmt.Errorf("failed to unmarshal archive media relation event: %w", err)
	}

	// Step 2. 取出领域事件的租户id
	tenantID := domainEvent.GetTenantId()
	if tenantID == "" {
		return fmt.Errorf("租户ID不能为空")
	}

	// Step 3. 把租户id记录到context，传给repo
	ctx := context.WithValue(context.Background(), global.TenantIDKey, tenantID)

	// Step 4: 根据事件类型进行处理
	eventType := domainEvent.GetEventType()
	switch eventType {
	case event.EventTypeArchiveMediaRelationCreated:
		return h.handleArchiveMediaRelationCreatedEvent(ctx, domainEvent)
	case event.EventTypeArchiveMediaRelationDeleted:
		return h.handleArchiveMediaRelationDeletedEvent(ctx, domainEvent)
	case event.EventTypeArchiveMediaRelationBatchCreated:
		return h.handleArchiveMediaRelationBatchCreatedEvent(ctx, domainEvent)
	case event.EventTypeArchiveMediaRelationBatchDeleted:
		return h.handleArchiveMediaRelationBatchDeletedEvent(ctx, domainEvent)
	// 处理媒体更新事件，同步稳定字段到关联读模型
	case event.EventTypeMediaUpdated:
		return h.handleMediaUpdatedEvent(ctx, domainEvent)
	// 处理档案更新事件，同步稳定字段到关联读模型
	case event.EventTypeArchiveUpdated:
		return h.handleArchiveUpdatedEvent(ctx, domainEvent)
	default:
		logger.Errorf("unknown archive media relation event type: %s", eventType)
		return fmt.Errorf("unknown archive media relation event type: %s", eventType)
	}
}

// handleArchiveMediaRelationCreatedEvent 处理档案媒体关联创建事件
func (h *ArchiveMediaRelationEventHandler) handleArchiveMediaRelationCreatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveMediaRelationCreatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveMediaRelationCreatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveMediaRelationCreatedPayload: %w", err)
	}

	// 构建关联读模型
	relationReadModel, err := h.buildArchiveMediaRelationReadModel(ctx, &payload)
	if err != nil {
		logger.Error("Failed to build ArchiveMediaRelationReadModel", "error", err, "relationId", payload.ID)
		return fmt.Errorf("failed to build ArchiveMediaRelationReadModel: %w", err)
	}

	// 创建关联读模型
	if err := h.relationRepo.Create(ctx, relationReadModel); err != nil {
		logger.Error("Failed to create ArchiveMediaRelationReadModel", "error", err, "relationId", payload.ID)
		return fmt.Errorf("failed to create ArchiveMediaRelationReadModel: %w", err)
	}

	logger.Info("Successfully created ArchiveMediaRelationReadModel", "relationId", payload.ID, "archiveId", payload.ArchiveId, "mediaId", payload.MediaId)
	return nil
}

// handleArchiveMediaRelationDeletedEvent 处理档案媒体关联删除事件
func (h *ArchiveMediaRelationEventHandler) handleArchiveMediaRelationDeletedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveMediaRelationDeletedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveMediaRelationDeletedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveMediaRelationDeletedPayload: %w", err)
	}

	// 删除关联读模型
	if err := h.relationRepo.Delete(ctx, payload.ID); err != nil {
		logger.Error("Failed to delete ArchiveMediaRelationReadModel", "error", err, "relationId", payload.ID)
		return fmt.Errorf("failed to delete ArchiveMediaRelationReadModel: %w", err)
	}

	logger.Info("Successfully deleted ArchiveMediaRelationReadModel", "relationId", payload.ID)
	return nil
}

// handleArchiveMediaRelationBatchCreatedEvent 处理档案媒体关联批量创建事件
func (h *ArchiveMediaRelationEventHandler) handleArchiveMediaRelationBatchCreatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveMediaRelationBatchCreatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveMediaRelationBatchCreatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveMediaRelationBatchCreatedPayload: %w", err)
	}

	// 构建批量关联读模型
	var relationReadModels []*models.ArchiveMediaRelationReadModel
	for _, relationPayload := range payload.Relations {
		relationReadModel, err := h.buildArchiveMediaRelationReadModel(ctx, &relationPayload)
		if err != nil {
			logger.Error("Failed to build ArchiveMediaRelationReadModel", "error", err, "relationId", relationPayload.ID)
			continue // 跳过失败的，继续处理其他的
		}
		relationReadModels = append(relationReadModels, relationReadModel)
	}

	// 批量创建关联读模型
	if len(relationReadModels) > 0 {
		if err := h.relationRepo.BatchCreate(ctx, relationReadModels); err != nil {
			logger.Error("Failed to batch create ArchiveMediaRelationReadModels", "error", err, "count", len(relationReadModels))
			return fmt.Errorf("failed to batch create ArchiveMediaRelationReadModels: %w", err)
		}
		logger.Info("Successfully batch created ArchiveMediaRelationReadModels", "count", len(relationReadModels))
	}

	return nil
}

// handleArchiveMediaRelationBatchDeletedEvent 处理档案媒体关联批量删除事件
func (h *ArchiveMediaRelationEventHandler) handleArchiveMediaRelationBatchDeletedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveMediaRelationBatchDeletedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveMediaRelationBatchDeletedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveMediaRelationBatchDeletedPayload: %w", err)
	}

	// 批量删除关联读模型
	if err := h.relationRepo.BatchDelete(ctx, payload.IDs); err != nil {
		logger.Error("Failed to batch delete ArchiveMediaRelationReadModels", "error", err, "ids", payload.IDs)
		return fmt.Errorf("failed to batch delete ArchiveMediaRelationReadModels: %w", err)
	}

	logger.Info("Successfully batch deleted ArchiveMediaRelationReadModels", "count", len(payload.IDs))
	return nil
}

// handleMediaUpdatedEvent 处理媒体更新事件，同步稳定字段到关联读模型
func (h *ArchiveMediaRelationEventHandler) handleMediaUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.MediaUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling MediaUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling MediaUpdatedPayload: %w", err)
	}

	// 检查是否需要同步到关联读模型（只同步稳定字段）
	changedFields := make([]string, 0, len(payload.UpdatedFields))
	for field := range payload.UpdatedFields {
		changedFields = append(changedFields, field)
	}
	if !h.needsSyncMediaToRelationModel(changedFields) {
		logger.Debug("Media update does not require sync to relation model", "mediaId", payload.MediaID, "changedFields", changedFields)
		return nil
	}

	// 查找所有关联此媒体的关联关系
	relations, err := h.relationRepo.FindByMediaID(ctx, payload.MediaID, 1, 1000) // 假设单个媒体不会关联超过1000个档案
	if err != nil {
		logger.Error("Failed to find relations by media ID", "error", err, "mediaId", payload.MediaID)
		return fmt.Errorf("failed to find relations by media ID: %w", err)
	}

	if len(relations.List) == 0 {
		logger.Debug("No relations found for media", "mediaId", payload.MediaID)
		return nil
	}

	// 获取媒体详细信息
	mediaReadModel, err := h.mediaRepo.FindByID(ctx, payload.MediaID)
	if err != nil {
		logger.Error("Failed to get media read model", "error", err, "mediaId", payload.MediaID)
		return fmt.Errorf("failed to get media read model: %w", err)
	}

	// 更新所有关联关系中的媒体稳定字段
	for _, relation := range relations.List {
		h.syncMediaFieldsToRelation(&relation, mediaReadModel)
		if err := h.relationRepo.Update(ctx, &relation); err != nil {
			logger.Error("Failed to update relation with media fields", "error", err, "relationId", relation.ID)
			// 继续处理其他关联关系，不中断整个流程
		}
	}

	logger.Info("Successfully synced media fields to relations", "mediaId", payload.MediaID, "relationCount", len(relations.List))
	return nil
}

// handleArchiveUpdatedEvent 处理档案更新事件，同步稳定字段到关联读模型
func (h *ArchiveMediaRelationEventHandler) handleArchiveUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveUpdatedPayload: %w", err)
	}

	// 检查是否需要同步到关联读模型（只同步稳定字段）
	changedFields := make([]string, 0, len(payload.UpdatedFields))
	for field := range payload.UpdatedFields {
		changedFields = append(changedFields, field)
	}
	if !h.needsSyncArchiveToRelationModel(changedFields) {
		logger.Debug("Archive update does not require sync to relation model", "archiveId", payload.ArchiveID, "changedFields", changedFields)
		return nil
	}

	// 查找所有关联此档案的关联关系
	relations, err := h.relationRepo.FindByArchiveID(ctx, payload.ArchiveID, 1, 1000) // 假设单个档案不会关联超过1000个媒体
	if err != nil {
		logger.Error("Failed to find relations by archive ID", "error", err, "archiveId", payload.ArchiveID)
		return fmt.Errorf("failed to find relations by archive ID: %w", err)
	}

	if len(relations.List) == 0 {
		logger.Debug("No relations found for archive", "archiveId", payload.ArchiveID)
		return nil
	}

	// 获取档案详细信息
	archiveReadModel, err := h.archiveRepo.FindByID(ctx, payload.ArchiveID)
	if err != nil {
		logger.Error("Failed to get archive read model", "error", err, "archiveId", payload.ArchiveID)
		return fmt.Errorf("failed to get archive read model: %w", err)
	}

	// 更新所有关联关系中的档案稳定字段
	for _, relation := range relations.List {
		h.syncArchiveFieldsToRelation(&relation, archiveReadModel)
		if err := h.relationRepo.Update(ctx, &relation); err != nil {
			logger.Error("Failed to update relation with archive fields", "error", err, "relationId", relation.ID)
			// 继续处理其他关联关系，不中断整个流程
		}
	}

	logger.Info("Successfully synced archive fields to relations", "archiveId", payload.ArchiveID, "relationCount", len(relations.List))
	return nil
}

// buildArchiveMediaRelationReadModel 构建档案媒体关联读模型
func (h *ArchiveMediaRelationEventHandler) buildArchiveMediaRelationReadModel(ctx context.Context, payload *event.ArchiveMediaRelationCreatedPayload) (*models.ArchiveMediaRelationReadModel, error) {
	// 获取媒体信息
	mediaReadModel, err := h.mediaRepo.FindByID(ctx, payload.MediaId)
	if err != nil {
		return nil, fmt.Errorf("failed to get media read model: %w", err)
	}

	// 获取档案信息
	archiveReadModel, err := h.archiveRepo.FindByID(ctx, payload.ArchiveId)
	if err != nil {
		return nil, fmt.Errorf("failed to get archive read model: %w", err)
	}

	// 构建关联读模型
	relationReadModel := &models.ArchiveMediaRelationReadModel{
		ID:        payload.ID,
		ArchiveId: payload.ArchiveId,
		MediaId:   payload.MediaId,

		// 档案稳定字段
		ArchiveCode:  archiveReadModel.ArchiveCode,
		ArchiveTitle: archiveReadModel.ArchiveTitle,
		ArchiveType:  archiveReadModel.ArchiveType,

		// 媒体稳定字段
		MediaName:     mediaReadModel.MediaName,
		MediaCate:     mediaReadModel.MediaCate,
		MediaSuffix:   mediaReadModel.MediaSuffix,
		FileSize:      mediaReadModel.FileSize,
		VideoDuration: mediaReadModel.VideoDuration,

		// 时间信息
		ShotTime:      mediaReadModel.ShotTime,
		ShotTimeStart: mediaReadModel.ShotTimeStart,
		ImportTime:    mediaReadModel.ImportTime,
		ExpiryTime:    mediaReadModel.ExpiryTime,

		// 组织信息
		OrgID:   mediaReadModel.OrgID,
		OrgName: mediaReadModel.OrgName,

		// 警员信息
		PoliceID:   mediaReadModel.PoliceID,
		PoliceName: mediaReadModel.PoliceName,

		// 设备信息
		RecorderID: mediaReadModel.RecorderID,
		RecorderNo: mediaReadModel.RecorderNo,
	}

	// 设置审计字段
	relationReadModel.ControlBy.CreateBy = payload.CreateBy
	relationReadModel.ControlBy.UpdateBy = payload.UpdateBy
	relationReadModel.ModelTime.CreatedAt = payload.CreatedAt
	relationReadModel.ModelTime.UpdatedAt = payload.UpdatedAt

	return relationReadModel, nil
}

// needsSyncMediaToRelationModel 检查媒体字段变更是否需要同步到关联读模型
func (h *ArchiveMediaRelationEventHandler) needsSyncMediaToRelationModel(changedFields []string) bool {
	stableFields := []string{
		"media_name", "media_cate", "media_suffix", "file_size", "video_duration",
		"shot_time", "shot_time_start", "import_time", "expiry_time",
		"org_id", "org_name", "police_id", "police_name",
		"recorder_id", "recorder_no",
	}

	for _, field := range changedFields {
		for _, stableField := range stableFields {
			if field == stableField {
				return true
			}
		}
	}
	return false
}

// needsSyncArchiveToRelationModel 检查档案字段变更是否需要同步到关联读模型
func (h *ArchiveMediaRelationEventHandler) needsSyncArchiveToRelationModel(changedFields []string) bool {
	stableFields := []string{"archive_code", "archive_title", "archive_type"}

	for _, field := range changedFields {
		for _, stableField := range stableFields {
			if field == stableField {
				return true
			}
		}
	}
	return false
}

// syncMediaFieldsToRelation 同步媒体字段到关联关系
func (h *ArchiveMediaRelationEventHandler) syncMediaFieldsToRelation(relation *models.ArchiveMediaRelationReadModel, media *models.MediaReadModel) {
	relation.MediaName = media.MediaName
	relation.MediaCate = media.MediaCate
	relation.MediaSuffix = media.MediaSuffix
	relation.FileSize = media.FileSize
	relation.VideoDuration = media.VideoDuration
	relation.ShotTime = media.ShotTime
	relation.ShotTimeStart = media.ShotTimeStart
	relation.ImportTime = media.ImportTime
	relation.ExpiryTime = media.ExpiryTime
	relation.OrgID = media.OrgID
	relation.OrgName = media.OrgName
	relation.PoliceID = media.PoliceID
	relation.PoliceName = media.PoliceName
	relation.RecorderID = media.RecorderID
	relation.RecorderNo = media.RecorderNo
}

// syncArchiveFieldsToRelation 同步档案字段到关联关系
func (h *ArchiveMediaRelationEventHandler) syncArchiveFieldsToRelation(relation *models.ArchiveMediaRelationReadModel, archive *models.ArchiveReadModel) {
	relation.ArchiveCode = archive.ArchiveCode
	relation.ArchiveTitle = archive.ArchiveTitle
	relation.ArchiveType = archive.ArchiveType
}
