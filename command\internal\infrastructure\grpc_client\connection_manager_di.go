package grpc_client

import (
	"jxt-evidence-system/evidence-management/shared/common/di"
	grpc_client "jxt-evidence-system/evidence-management/shared/common/grpc/client"

	"github.com/ChenBigdata421/jxt-core/sdk/config"
	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

// 注册ConnectionManager的依赖注入
func init() {
	registrations = append(registrations, registerConnectionManagerDependencies)
}

// registerConnectionManagerDependencies 注册ConnectionManager的依赖注入
func registerConnectionManagerDependencies() {
	// 注册连接管理器 - 单例，所有服务适配器共享
	if err := di.Provide(func() (*grpc_client.ConnectionManager, error) {
		// 使用SDK中的GRPCClientConfig配置
		clientConfig := config.GrpcConfig.Client
		etcdConfig := config.EtcdConfig

		logger.Infof("从配置中获取gRPC客户端配置: ServiceKey=%s, Timeout=%d",
			clientConfig.ServiceKey, clientConfig.Timeout)

		// 创建连接管理器
		connManager, err := grpc_client.NewConnectionManager(clientConfig, *etcdConfig)
		if err != nil {
			logger.Errorf("创建gRPC连接管理器失败: %v", err)
			return nil, err
		}

		logger.Info("gRPC连接管理器创建成功")
		return connManager, nil
	}); err != nil {
		logger.Fatalf("注册gRPC连接管理器失败: %v", err)
	}
}
