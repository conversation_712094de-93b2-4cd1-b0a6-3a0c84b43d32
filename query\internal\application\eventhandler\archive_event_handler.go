package eventhandler

import (
	"context"
	"fmt"
	"jxt-evidence-system/evidence-management/query/internal/models"
	"jxt-evidence-system/evidence-management/query/internal/models/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/global"
	client "jxt-evidence-system/evidence-management/shared/common/grpc/client/port"
	commonModels "jxt-evidence-system/evidence-management/shared/common/models"
	"jxt-evidence-system/evidence-management/shared/domain/event"
	"strings"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"github.com/ThreeDotsLabs/watermill/message"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	registrations = append(registrations, registerArchiveEventHandlerDependencies)
}

func registerArchiveEventHandlerDependencies() {
	err := di.Provide(func(subscriber EventSubscriber,
		repo repository.ArchiveReadModelRepository,
		userInfoClient client.UserInfoServiceClient,
		orgInfoClient client.OrgInfoServiceClient) *ArchiveEventHandler {
		return NewArchiveEventHandler(subscriber, repo, userInfoClient, orgInfoClient)
	})
	if err != nil {
		logger.Error("Failed to provide ArchiveEventHandler", "error", err)
	}
}

type ArchiveEventHandler struct {
	Subscriber     EventSubscriber
	repo           repository.ArchiveReadModelRepository
	userInfoClient client.UserInfoServiceClient
	orgInfoClient  client.OrgInfoServiceClient
}

func NewArchiveEventHandler(subscriber EventSubscriber,
	repo repository.ArchiveReadModelRepository,
	userInfoClient client.UserInfoServiceClient,
	orgInfoClient client.OrgInfoServiceClient,
) *ArchiveEventHandler {
	return &ArchiveEventHandler{
		Subscriber:     subscriber,
		repo:           repo,
		userInfoClient: userInfoClient,
		orgInfoClient:  orgInfoClient,
	}
}

// isConnectionClosingError 检查是否是连接关闭错误
func (h *ArchiveEventHandler) isConnectionClosingError(err error) bool {
	if err == nil {
		return false
	}
	errMsg := err.Error()
	return strings.Contains(errMsg, "connection is closing") ||
		strings.Contains(errMsg, "client connection is closing") ||
		strings.Contains(errMsg, "transport is closing") ||
		strings.Contains(errMsg, "context canceled") ||
		strings.Contains(errMsg, "rpc error: code = Canceled")
}

// retryWithExponentialBackoff 使用指数退避进行重试
func (h *ArchiveEventHandler) retryWithExponentialBackoff(ctx context.Context, operation func() error, maxRetries int) error {
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		err := operation()
		if err == nil {
			return nil
		}

		lastErr = err

		// 如果不是连接关闭错误，直接返回
		if !h.isConnectionClosingError(err) {
			logger.Warn("非连接错误，停止重试", "error", err, "attempt", attempt+1)
			return err
		}

		// 计算退避时间：1s, 2s, 4s, 8s, 16s
		backoffDuration := time.Duration(1<<uint(attempt)) * time.Second
		if backoffDuration > 30*time.Second {
			backoffDuration = 30 * time.Second // 最大30秒
		}

		logger.Warn("检测到连接关闭错误，将重试",
			"error", err,
			"attempt", attempt+1,
			"maxRetries", maxRetries,
			"backoff", backoffDuration)

		// 如果不是最后一次尝试，则等待
		if attempt < maxRetries-1 {
			timer := time.NewTimer(backoffDuration)
			select {
			case <-ctx.Done():
				timer.Stop()
				return ctx.Err()
			case <-timer.C:
				// 继续下一次重试
			}
		}
	}

	logger.Error("达到最大重试次数，仍然失败", "maxRetries", maxRetries, "lastError", lastErr)
	return fmt.Errorf("达到最大重试次数 %d，最后错误: %w", maxRetries, lastErr)
}

func (h *ArchiveEventHandler) ConsumeEvent(topic string) error {
	if err := h.Subscriber.Subscribe(topic, h.handleArchiveEvent, time.Second*30); err != nil {
		logger.Error("Failed to subscribe to topic", "topic", topic, "error", err)
		return err
	}

	return nil
}

func (h *ArchiveEventHandler) handleArchiveEvent(msg *message.Message) error {
	// Step 1: 反序列化为领域事件结构体
	domainEvent := &event.DomainEvent{}

	err := domainEvent.UnmarshalJSON(msg.Payload)
	if err != nil {
		return fmt.Errorf("failed to unmarshal archive event: %w", err)
	}

	// Step 2. 取出领域事件的租户id
	tenantID := domainEvent.GetTenantId()
	if tenantID == "" {
		return fmt.Errorf("租户ID不能为空")
	}

	// Step 3. 把租户id记录到context，传给repo
	ctx := context.WithValue(context.Background(), global.TenantIDKey, tenantID)

	// Step 4: 根据事件类型进行处理
	eventType := domainEvent.GetEventType()
	switch eventType {
	case event.EventTypeArchiveCreated:
		return h.handleArchiveCreatedEvent(ctx, domainEvent)
	case event.EventTypeArchiveUpdated:
		return h.handleArchiveUpdatedEvent(ctx, domainEvent)
	case event.EventTypeArchiveDeleted:
		return h.handleArchiveDeletedEvent(ctx, domainEvent)
	case event.EventTypeArchiveBatchUpdated:
		return h.handleArchiveBatchUpdatedEvent(ctx, domainEvent)
	case event.EventTypeArchiveBatchDeleted:
		return h.handleArchiveBatchDeletedEvent(ctx, domainEvent)
	case event.EventTypeArchiveBatchStatusUpdated:
		return h.handleArchiveBatchStatusUpdatedEvent(ctx, domainEvent)
	case event.EventTypeArchiveBatchExpirationUpdated:
		return h.handleArchiveBatchExpirationUpdatedEvent(ctx, domainEvent)
	case event.EventTypeArchiveBatchStorageDurationUpdated:
		return h.handleArchiveBatchStorageDurationUpdatedEvent(ctx, domainEvent)
	default:
		logger.Errorf("unknown archive event type: %s", eventType)
		return fmt.Errorf("unknown archive event type: %s", eventType)
	}
}

func (h *ArchiveEventHandler) handleArchiveCreatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveCreatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveCreatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveCreatedPayload: %w", err)
	}

	// 构建读模型
	archiveReadModel := &models.ArchiveReadModel{
		ID:              payload.ArchiveID,
		ArchiveCode:     payload.ArchiveCode,
		ArchiveTitle:    payload.ArchiveTitle,
		ArchiveType:     payload.ArchiveType,
		Description:     payload.Description,
		OrgID:           payload.OrgID,
		StorageDuration: payload.StorageDuration,
		ExpirationTime:  payload.ExpirationTime,
		Status:          payload.Status,
		Remarks:         payload.Remarks,
		ControlBy: commonModels.ControlBy{
			CreateBy: payload.CreateBy,
			UpdateBy: payload.UpdateBy,
		},
		ModelTime: commonModels.ModelTime{
			CreatedAt: payload.CreatedAt,
			UpdatedAt: payload.UpdatedAt,
		},
	}

	logger.Info("Received ArchiveCreatedEvent", "payload", payload)

	// 获取租户ID
	tenantID := domainEvent.GetTenantId()

	// ============================================
	// 关键业务流程：查询外部信息并构建完整的读模型
	// 使用应用层重试确保数据一致性
	// ============================================
	err := h.retryWithExponentialBackoff(ctx, func() error {
		// 根据组织ID查询组织信息
		orgInfoRaw, err := h.orgInfoClient.GetOrgById(ctx, tenantID, int32(payload.OrgID))
		if err != nil {
			logger.Error("根据组织ID查询组织信息失败", "orgID", payload.OrgID, "error", err)
			return fmt.Errorf("根据组织ID[%d]查询组织信息失败: %v", payload.OrgID, err)
		}
		if orgInfoRaw == nil {
			logger.Error("组织ID对应的组织不存在", "orgID", payload.OrgID)
			return fmt.Errorf("组织ID[%d]对应的组织不存在", payload.OrgID)
		}

		// 设置组织信息
		archiveReadModel.OrgCode = orgInfoRaw.OrgCode
		archiveReadModel.OrgName = orgInfoRaw.OrgName
		archiveReadModel.OrgJc = orgInfoRaw.OrgNameJc

		// 根据创建用户ID查询用户信息
		if payload.CreateBy > 0 {
			createUserInfo, err := h.userInfoClient.GetUserById(ctx, tenantID, int32(payload.CreateBy))
			if err != nil {
				logger.Error("根据创建用户ID查询用户信息失败", "createUserID", payload.CreateBy, "error", err)
				return fmt.Errorf("根据创建用户ID[%d]查询用户信息失败: %v", payload.CreateBy, err)
			}
			if createUserInfo != nil {
				archiveReadModel.CreateUserName = createUserInfo.UserName
				archiveReadModel.CreateUserNo = createUserInfo.PoliceNo
			}
		}

		// 根据更新用户ID查询用户信息
		if payload.UpdateBy > 0 {
			updateUserInfo, err := h.userInfoClient.GetUserById(ctx, tenantID, int32(payload.UpdateBy))
			if err != nil {
				logger.Error("根据更新用户ID查询用户信息失败", "updateUserID", payload.UpdateBy, "error", err)
				return fmt.Errorf("根据更新用户ID[%d]查询用户信息失败: %v", payload.UpdateBy, err)
			}
			if updateUserInfo != nil {
				archiveReadModel.UpdateUserName = updateUserInfo.UserName
				archiveReadModel.UpdateUserNo = updateUserInfo.PoliceNo
			}
		}

		// 直接使用 repository 创建读模型
		err = h.repo.Create(ctx, archiveReadModel)
		if err != nil {
			logger.Error("创建档案读模型失败", "error", err)
			return err
		}

		return nil
	}, 3) // 关键业务流程重试3次

	if err != nil {
		logger.Error("档案创建事件处理失败", "error", err)
		return err
	}

	logger.Info("档案创建事件处理成功", "archiveID", payload.ArchiveID)
	return nil
}

func (h *ArchiveEventHandler) handleArchiveUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveUpdatedPayload: %w", err)
	}

	logger.Info("Received ArchiveUpdatedEvent", "payload", payload)

	// 直接使用 repository 更新读模型
	err := h.repo.UpdateByID(ctx, payload.ArchiveID, payload.UpdatedFields)
	if err != nil {
		logger.Error("更新档案读模型失败", "error", err)
		return err
	}

	return nil
}

func (h *ArchiveEventHandler) handleArchiveDeletedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveDeletedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveDeletedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveDeletedPayload: %w", err)
	}

	logger.Info("Received ArchiveDeletedEvent", "payload", payload)

	// 直接使用 repository 删除读模型
	err := h.repo.DeleteByID(ctx, payload.GetId().(int64))
	if err != nil {
		logger.Error("删除档案[%d]读模型失败", payload.ArchiveID, "error", err)
		return err
	}

	return nil
}

func (h *ArchiveEventHandler) handleArchiveBatchUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveBatchUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveBatchUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveBatchUpdatedPayload: %w", err)
	}

	logger.Info("Received ArchiveBatchUpdatedEvent", "payload", payload)

	// 直接使用 repository 更新读模型
	_, err := h.repo.UpdateManyByIDs(ctx, payload.ArchiveIDs, payload.UpdatedFields)
	if err != nil {
		logger.Error("批量更新档案读模型失败", "error", err)
		return err
	}

	return nil
}

func (h *ArchiveEventHandler) handleArchiveBatchDeletedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveBatchDeletedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveBatchDeletedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveBatchDeletedPayload: %w", err)
	}

	logger.Info("Received ArchiveBatchDeletedEvent", "payload", payload)

	// 直接使用 repository 删除读模型
	_, err := h.repo.DeleteManyByIDs(ctx, payload.GetIds().([]int64))
	if err != nil {
		logger.Error("批量删除档案读模型失败", "error", err)
		return err
	}

	return nil
}

func (h *ArchiveEventHandler) handleArchiveBatchStatusUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveBatchStatusUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveBatchStatusUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveBatchStatusUpdatedPayload: %w", err)
	}

	logger.Info("Received ArchiveBatchStatusUpdatedEvent", "payload", payload)

	// 定义需要更新的字段
	updates := map[string]interface{}{
		"Status":    payload.Status,
		"UpdateBy":  payload.UpdateBy,
		"UpdatedAt": payload.UpdatedAt,
	}

	// 直接使用 repository 批量更新档案状态
	_, err := h.repo.UpdateManyByIDs(ctx, payload.ArchiveIDs, updates)
	if err != nil {
		logger.Error("批量更新档案状态失败", "error", err)
		return err
	}

	return nil
}

func (h *ArchiveEventHandler) handleArchiveBatchExpirationUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveBatchExpirationUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveBatchExpirationUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveBatchExpirationUpdatedPayload: %w", err)
	}

	logger.Info("Received ArchiveBatchExpirationUpdatedEvent", "payload", payload)

	// 定义需要更新的字段
	updates := map[string]interface{}{
		"ExpirationTime": payload.ExpirationTime,
		"UpdateBy":       payload.UpdateBy,
		"UpdatedAt":      payload.UpdatedAt,
	}

	// 直接使用 repository 批量更新档案过期时间
	_, err := h.repo.UpdateManyByIDs(ctx, payload.ArchiveIDs, updates)
	if err != nil {
		logger.Error("批量更新档案过期时间失败", "error", err)
		return err
	}

	return nil
}

func (h *ArchiveEventHandler) handleArchiveBatchStorageDurationUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.ArchiveBatchStorageDurationUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling ArchiveBatchStorageDurationUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling ArchiveBatchStorageDurationUpdatedPayload: %w", err)
	}

	logger.Info("Received ArchiveBatchStorageDurationUpdatedEvent", "payload", payload)

	// 定义需要更新的字段
	updates := map[string]interface{}{
		"StorageDuration": payload.StorageDuration,
		"UpdateBy":        payload.UpdateBy,
		"UpdatedAt":       payload.UpdatedAt,
	}

	// 直接使用 repository 批量更新档案保存期限
	_, err := h.repo.UpdateManyByIDs(ctx, payload.ArchiveIDs, updates)
	if err != nil {
		logger.Error("批量更新档案保存期限失败", "error", err)
		return err
	}

	return nil
}
