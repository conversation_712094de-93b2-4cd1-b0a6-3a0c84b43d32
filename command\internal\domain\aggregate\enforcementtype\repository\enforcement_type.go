package repository

import (
	"context"
	"jxt-evidence-system/evidence-management/command/internal/application/transaction"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/enforcementtype"
)

// EnforcementTypeRepository 执法类型仓储接口
type EnforcementTypeRepository interface {
	// 基本操作
	Create(ctx context.Context, tx transaction.Transaction, model *enforcementtype.EnforcementType) error
	Update(ctx context.Context, tx transaction.Transaction, model *enforcementtype.EnforcementType) error
	Delete(ctx context.Context, tx transaction.Transaction, id interface{}) error

	// 查询操作
	FindByID(ctx context.Context, id interface{}) (*enforcementtype.EnforcementType, error)
	FindByCode(ctx context.Context, code string) (*enforcementtype.EnforcementType, error)
	FindChildren(ctx context.Context, parentID interface{}) ([]*enforcementtype.EnforcementType, error)

	ListByParentID(ctx context.Context, parentID interface{}) ([]*enforcementtype.EnforcementType, error)
}
