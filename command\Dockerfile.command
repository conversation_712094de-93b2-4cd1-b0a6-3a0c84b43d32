# 从华为云镜像站拉取golang1.21版本，配套的alpine是3.20.0版本
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/golang:1.23-alpine AS builder

ENV GOPROXY=https://goproxy.cn/
# ENV GOPROXY=direct  
ENV GO111MODULE=on

# 安装必要的工具
RUN apk add --no-cache git tzdata

# 设置工作目录
WORKDIR /app

# 复制工作区和模块文件,注意在项目的根目录下执行docker build，下面的
COPY go.work go.work  
COPY go.work.sum go.work.sum

# 复制command模块的内容
COPY ./command ./command
# 复制 shared 目录
COPY ./shared ./shared
# 复制 query 目录,虽然command不依赖query，但执行go work sync时需要query
COPY ./query ./query
# 复制 tests 目录,虽然command不依赖tests，但执行go work sync时需要tests
COPY ./tests ./tests

# 运行 go work sync 同步工作区的模块依赖关系
RUN go work sync

# 移动到 specific module 目录中
WORKDIR /app/command

# 清理和下载所有该模块所需的依赖
RUN go mod tidy
RUN go mod download all

# 打印当前工作目录和列出目录内容
RUN pwd && ls

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-w -s" -a -installsuffix cgo -o evidence-command ./cmd

# 清理不必要的文件
#RUN rm -rf /app/command /app/shared /app/go.work /app/go.work.sum

# 使用轻量级的 alpine 作为基础镜像
FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/alpine:3.20.2

# 设置环境变量
# ENV APP_ENV=production

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/command/evidence-command /app/

# 复制配置文件
COPY --from=builder /app/command/config/settings.yml /app/config/settings.yml

# 设置时区
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 创建应用程序的日志目录
RUN mkdir -p /app/log

# 设置环境变量  
ENV CONFIG_PATH=/app/config/settings.yml

# 设置权限  
RUN chmod +x /app/evidence-command && \
    chown -R nobody:nobody /app /app/log  

USER nobody 

EXPOSE 8081

ENTRYPOINT ["/app/evidence-command"]  
# CMD ["server", "-c", "${CONFIG_PATH}"]  
CMD server -c $CONFIG_PATH 