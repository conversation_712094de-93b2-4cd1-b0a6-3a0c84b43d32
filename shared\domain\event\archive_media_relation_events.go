package event

import (
	"time"
)

// ArchiveMediaRelation事件类型定义
const (
	// 单资源事件
	EventTypeArchiveMediaRelationCreated = "ArchiveMediaRelationCreated"

	EventTypeArchiveMediaRelationDeleted = "ArchiveMediaRelationDeleted"

	// 批量事件
	EventTypeArchiveMediaRelationBatchCreated = "ArchiveMediaRelationBatchCreated"

	EventTypeArchiveMediaRelationBatchDeleted = "ArchiveMediaRelationBatchDeleted"
)

// 具体的领域事件结构体

// ArchiveMediaRelationCreatedPayload 档案媒体关联创建事件载荷
type ArchiveMediaRelationCreatedPayload struct {
	ID        int64 `json:"id"`
	ArchiveId int64 `json:"archiveId"`
	MediaId   int64 `json:"mediaId"`

	// 审计字段
	CreateBy  int       `json:"createBy"`
	UpdateBy  int       `json:"updateBy"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func (s *ArchiveMediaRelationCreatedPayload) GetId() interface{} {
	return s.ID
}

// ArchiveMediaRelationDeletedPayload 档案媒体关联删除事件载荷
type ArchiveMediaRelationDeletedPayload struct {
	ID        int64     `json:"id"`
	UpdateBy  int       `json:"updateBy"`
	DeletedAt time.Time `json:"deletedAt"`
}

func (s *ArchiveMediaRelationDeletedPayload) GetId() interface{} {
	return s.ID
}

// ArchiveMediaRelationBatchCreatedPayload 档案媒体关联批量创建事件载荷
type ArchiveMediaRelationBatchCreatedPayload struct {
	Relations []ArchiveMediaRelationCreatedPayload `json:"relations"`
	CreateBy  int                                  `json:"createBy"`
	CreatedAt time.Time                            `json:"createdAt"`
}

func (s *ArchiveMediaRelationBatchCreatedPayload) GetIds() interface{} {
	var ids []int64
	for _, relation := range s.Relations {
		ids = append(ids, relation.ID)
	}
	return ids
}

// ArchiveMediaRelationBatchDeletedPayload 档案媒体关联批量删除事件载荷
type ArchiveMediaRelationBatchDeletedPayload struct {
	IDs       []int64   `json:"ids"`
	UpdateBy  int       `json:"updateBy"`
	DeletedAt time.Time `json:"deletedAt"`
}

func (s *ArchiveMediaRelationBatchDeletedPayload) GetIds() interface{} {
	return s.IDs
}
