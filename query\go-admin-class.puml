@startuml
'https://plantuml.com/class-diagram

apis.Api <|-- apis.SysDictType
service.Service <|-- service.SysDictType


apis.SysDictType -> service.SysDictType : creates/Update(c *dto.SysDictTypeUpdateReq)


apis.SysDictType -> dto.SysDictTypeUpdateReq : creates

service.SysDictType ..> dto.SysDictTypeUpdateReq : uses

service.SysDictType -> models.SysDictType : creates
service.SysDictType -> dto.SysDictTypeUpdateReq : Generate(&models.SysDictType)
dto.SysDictTypeUpdateReq ..> models.SysDictType : uses

class apis.Api<<controller>> {
	+ Context *gin.Context
	+ Logger  *logger.Helper
	+ Orm     *gorm.DB
	+ Errors  error
	--
	+ MakeContext(c *gin.Context)
    + MakeOrm()
    + MakeService(c *service.Service)
    + Bind(d interface{}, bindings ...binding.Binding)
    + GetLogger()
    + GetOrm()
    + AddError(err error)
    + Error(code int, err error, msg string)
    + OK(data interface{}, msg string)
    + PageOK(result interface{}, count int,..
      ..pageIndex int, pageSize int, msg string)
    + Custom(data gin.H)
}

class apis.SysDictType<<controller>> {
--
   + GetPage(c *gin.Context)
   + Get(c *gin.Context)
   + Insert(c *gin.Context)
   + Update(c *gin.Context)
   + Delete(c *gin.Context)
   + GetAll(c *gin.Context)
}

class service.Service<<service>> {
	+ Orm   *gorm.DB
	+ Msg   string
	+ MsgID string
	+ Log   *logger.Helper
	+ Error error
--
    + AddError(err error)
}

class service.SysDictType<<service>> {
--
    + GetPage(..)
    + Get(..)
    + Insert(c *dto.SysDictTypeInsertReq)
    + Update(c *dto.SysDictTypeUpdateReq)
    + Remove(d *dto.SysDictTypeDeleteReq)
    + GetAll(..)
}

class dto.SysDictTypeUpdateReq {
	+ Id int
	+ DictName string
	+ DictType string
	+ Status int
	+ Remark string
	+ common.ControlBy
--
    + Generate(model *models.SysDictType)
    + GetId()
}

class models.SysDictType<<model>>  {
	+ ID int
	+ DictName string
	+ DictType string
	+ Status int
	+ Remark string
	+ models.ControlBy
	+ models.ModelTime
--
	+ TableName()
	+ Generate()
	+ GetId()
}






@enduml