package grpc_client

import (
	"jxt-evidence-system/evidence-management/shared/common/di"
	grpc_client "jxt-evidence-system/evidence-management/shared/common/grpc/client"
	client "jxt-evidence-system/evidence-management/shared/common/grpc/client/port"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

func init() {
	registrations = append(registrations, registerOrgInfoServiceClientDependencies)
}

func registerOrgInfoServiceClientDependencies() {
	err := di.Provide(func(connManager *grpc_client.ConnectionManager) client.OrgInfoServiceClient {
		return grpc_client.NewOrgInfoServiceClient(connManager)
	})
	if err != nil {
		logger.Fatalf("Failed to provide OrgInfoServiceClient: %v", err)
	}
}
