package router

import (
	"jxt-evidence-system/evidence-management/query/internal/interface/rest/api"
	"jxt-evidence-system/evidence-management/shared/common/di"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
	"github.com/gin-gonic/gin"
)

func init() {
	routerNoCheckRole = append(routerNoCheckRole, registerArchiveMediaRelationRouter)
}

func registerArchiveMediaRelationRouter(v1 *gin.RouterGroup) {
	// 从依赖注入容器获取API控制器
	var archiveMediaRelationHandler *api.ArchiveMediaRelationHandler
	if err := di.Invoke(func(handler *api.ArchiveMediaRelationHandler) {
		archiveMediaRelationHandler = handler
	}); err != nil {
		logger.Fatalf("Failed to get ArchiveMediaRelationHandler: %v", err)
	}

	// 档案媒体关联查询路由组
	relationGroup := v1.Group("/archive-media-relations")
	{
		// 基础查询接口
		relationGroup.GET("/:id", archiveMediaRelationHandler.GetByID)               // 根据ID获取关联详情
		relationGroup.GET("", archiveMediaRelationHandler.GetPage)                   // 分页查询关联列表
		relationGroup.GET("/check", archiveMediaRelationHandler.CheckRelationExists) // 检查关联关系是否存在
		relationGroup.GET("/health", archiveMediaRelationHandler.HealthCheck)        // 健康检查

		// 根据档案ID的查询接口
		relationGroup.GET("/archives/:archiveId/media-relations", archiveMediaRelationHandler.GetRelationsByArchiveID) // 获取档案关联的媒体列表
		relationGroup.GET("/archives/:archiveId/media-summary", archiveMediaRelationHandler.GetArchiveMediaSummary)    // 获取档案媒体汇总信息

		// 根据媒体ID的查询接口
		relationGroup.GET("/media/:mediaId/archive-relations", archiveMediaRelationHandler.GetRelationsByMediaID) // 获取媒体关联的档案列表
		relationGroup.GET("/media/:mediaId/archive-summary", archiveMediaRelationHandler.GetMediaArchiveSummary)  // 获取媒体档案汇总信息
	}
}
