package queryservice

import (
	"context"
	"fmt"
	"jxt-evidence-system/evidence-management/query/internal/models"
	"jxt-evidence-system/evidence-management/query/internal/models/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

func init() {
	registrations = append(registrations, registerArchiveMediaRelationQueryServiceDependencies)
}

func registerArchiveMediaRelationQueryServiceDependencies() {
	err := di.Provide(func(
		relationRepo repository.ArchiveMediaRelationReadRepository,
		mediaRepo repository.MediaReadModelRepository,
		archiveRepo repository.ArchiveReadModelRepository,
	) *ArchiveMediaRelationQueryService {
		return NewArchiveMediaRelationQueryService(relationRepo, mediaRepo, archiveRepo)
	})
	if err != nil {
		logger.Error("Failed to provide ArchiveMediaRelationQueryService", "error", err)
	}
}

type ArchiveMediaRelationQueryService struct {
	relationRepo repository.ArchiveMediaRelationReadRepository
	mediaRepo    repository.MediaReadModelRepository
	archiveRepo  repository.ArchiveReadModelRepository
}

func NewArchiveMediaRelationQueryService(
	relationRepo repository.ArchiveMediaRelationReadRepository,
	mediaRepo repository.MediaReadModelRepository,
	archiveRepo repository.ArchiveReadModelRepository,
) *ArchiveMediaRelationQueryService {
	return &ArchiveMediaRelationQueryService{
		relationRepo: relationRepo,
		mediaRepo:    mediaRepo,
		archiveRepo:  archiveRepo,
	}
}

// GetRelationByID 根据ID获取档案媒体关联详情
func (s *ArchiveMediaRelationQueryService) GetRelationByID(ctx context.Context, id int64) (*models.ArchiveMediaRelationReadModel, error) {
	relation, err := s.relationRepo.FindByID(ctx, id)
	if err != nil {
		logger.Error("Failed to find archive media relation by ID", "error", err, "id", id)
		return nil, fmt.Errorf("查询档案媒体关联失败: %w", err)
	}

	if relation == nil {
		return nil, fmt.Errorf("档案媒体关联不存在")
	}

	return relation, nil
}

// GetRelationsByArchiveID 根据档案ID获取关联的媒体列表
func (s *ArchiveMediaRelationQueryService) GetRelationsByArchiveID(ctx context.Context, archiveId int64, page, pageSize int) (*models.ArchiveMediaRelationReadModelList, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 1000 {
		pageSize = 20
	}

	relations, err := s.relationRepo.FindByArchiveID(ctx, archiveId, page, pageSize)
	if err != nil {
		logger.Error("Failed to find relations by archive ID", "error", err, "archiveId", archiveId)
		return nil, fmt.Errorf("查询档案关联媒体失败: %w", err)
	}

	return relations, nil
}

// GetRelationsByMediaID 根据媒体ID获取关联的档案列表
func (s *ArchiveMediaRelationQueryService) GetRelationsByMediaID(ctx context.Context, mediaId int64, page, pageSize int) (*models.ArchiveMediaRelationReadModelList, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 1000 {
		pageSize = 20
	}

	relations, err := s.relationRepo.FindByMediaID(ctx, mediaId, page, pageSize)
	if err != nil {
		logger.Error("Failed to find relations by media ID", "error", err, "mediaId", mediaId)
		return nil, fmt.Errorf("查询媒体关联档案失败: %w", err)
	}

	return relations, nil
}

// GetRelationWithPagination 分页查询档案媒体关联列表
func (s *ArchiveMediaRelationQueryService) GetRelationWithPagination(ctx context.Context, page, pageSize int, filters map[string]interface{}) (*models.ArchiveMediaRelationReadModelList, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 1000 {
		pageSize = 20
	}

	relations, err := s.relationRepo.FindWithPagination(ctx, page, pageSize, filters)
	if err != nil {
		logger.Error("Failed to find relations with pagination", "error", err, "filters", filters)
		return nil, fmt.Errorf("分页查询档案媒体关联失败: %w", err)
	}

	return relations, nil
}

// CheckRelationExists 检查档案和媒体的关联关系是否存在
func (s *ArchiveMediaRelationQueryService) CheckRelationExists(ctx context.Context, archiveId, mediaId int64) (bool, error) {
	relation, err := s.relationRepo.FindByArchiveAndMedia(ctx, archiveId, mediaId)
	if err != nil {
		logger.Error("Failed to check relation exists", "error", err, "archiveId", archiveId, "mediaId", mediaId)
		return false, fmt.Errorf("检查关联关系失败: %w", err)
	}

	return relation != nil, nil
}

// GetArchiveMediaSummary 获取档案媒体关联汇总信息
func (s *ArchiveMediaRelationQueryService) GetArchiveMediaSummary(ctx context.Context, archiveId int64) (*models.ArchiveMediaRelationSummary, error) {
	summary, err := s.relationRepo.GetArchiveMediaSummary(ctx, archiveId)
	if err != nil {
		logger.Error("Failed to get archive media summary", "error", err, "archiveId", archiveId)
		return nil, fmt.Errorf("获取档案媒体汇总信息失败: %w", err)
	}

	return summary, nil
}

// GetMediaArchiveSummary 获取媒体档案关联汇总信息
func (s *ArchiveMediaRelationQueryService) GetMediaArchiveSummary(ctx context.Context, mediaId int64) (*models.MediaArchiveRelationSummary, error) {
	summary, err := s.relationRepo.GetMediaArchiveSummary(ctx, mediaId)
	if err != nil {
		logger.Error("Failed to get media archive summary", "error", err, "mediaId", mediaId)
		return nil, fmt.Errorf("获取媒体档案汇总信息失败: %w", err)
	}

	return summary, nil
}

// GetMediaTypeStatsByArchive 根据档案ID统计不同媒体类型的数量
func (s *ArchiveMediaRelationQueryService) GetMediaTypeStatsByArchive(ctx context.Context, archiveId int64) (map[int]int, error) {
	stats, err := s.relationRepo.FindMediaTypeStatsByArchive(ctx, archiveId)
	if err != nil {
		logger.Error("Failed to get media type stats by archive", "error", err, "archiveId", archiveId)
		return nil, fmt.Errorf("获取档案媒体类型统计失败: %w", err)
	}

	return stats, nil
}

// GetRecentRelations 获取最近创建的关联关系
func (s *ArchiveMediaRelationQueryService) GetRecentRelations(ctx context.Context, limit int) ([]*models.ArchiveMediaRelationReadModel, error) {
	if limit <= 0 || limit > 100 {
		limit = 10
	}

	relations, err := s.relationRepo.FindRecentRelations(ctx, limit)
	if err != nil {
		logger.Error("Failed to get recent relations", "error", err, "limit", limit)
		return nil, fmt.Errorf("获取最近关联关系失败: %w", err)
	}

	return relations, nil
}

// GetArchiveMediaCount 获取档案关联的媒体数量
func (s *ArchiveMediaRelationQueryService) GetArchiveMediaCount(ctx context.Context, archiveId int64) (int64, error) {
	count, err := s.relationRepo.CountByArchiveID(ctx, archiveId)
	if err != nil {
		logger.Error("Failed to count media by archive ID", "error", err, "archiveId", archiveId)
		return 0, fmt.Errorf("统计档案媒体数量失败: %w", err)
	}

	return count, nil
}

// GetMediaArchiveCount 获取媒体关联的档案数量
func (s *ArchiveMediaRelationQueryService) GetMediaArchiveCount(ctx context.Context, mediaId int64) (int64, error) {
	count, err := s.relationRepo.CountByMediaID(ctx, mediaId)
	if err != nil {
		logger.Error("Failed to count archives by media ID", "error", err, "mediaId", mediaId)
		return 0, fmt.Errorf("统计媒体档案数量失败: %w", err)
	}

	return count, nil
}

// GetRelationWithMediaDetails 获取关联关系及媒体详细信息（混合查询策略）
// 这是一个高级查询方法，结合了关联读模型和媒体读模型的信息
func (s *ArchiveMediaRelationQueryService) GetRelationWithMediaDetails(ctx context.Context, relationId int64) (*ArchiveMediaRelationWithDetails, error) {
	// 1. 先从关联读模型获取基础信息（包含稳定字段）
	relation, err := s.relationRepo.FindByID(ctx, relationId)
	if err != nil {
		logger.Error("Failed to find relation by ID", "error", err, "relationId", relationId)
		return nil, fmt.Errorf("查询关联关系失败: %w", err)
	}

	if relation == nil {
		return nil, fmt.Errorf("关联关系不存在")
	}

	// 2. 如果需要动态字段，再查询媒体读模型获取完整信息
	media, err := s.mediaRepo.FindByID(ctx, relation.MediaId)
	if err != nil {
		logger.Error("Failed to find media by ID", "error", err, "mediaId", relation.MediaId)
		return nil, fmt.Errorf("查询媒体信息失败: %w", err)
	}

	if media == nil {
		return nil, fmt.Errorf("媒体不存在")
	}

	// 3. 组合返回完整信息
	result := &ArchiveMediaRelationWithDetails{
		Relation: relation,
		// 动态字段从媒体读模型获取
		ImportantLevel:        media.ImportantLevel,
		IsLocked:              media.IsLocked,
		IsNonEnforcementMedia: media.IsNonEnforcementMedia,
		EnforceType:           media.EnforceType,
		Comments:              media.Comments,
		// 存储信息
		StoragePath: media.URI,     // 使用URI字段
		Md5:         media.FileMd5, // 使用FileMd5字段
		// 其他详细信息...
	}

	return result, nil
}

// ArchiveMediaRelationWithDetails 档案媒体关联及详细信息
type ArchiveMediaRelationWithDetails struct {
	// 基础关联信息（来自关联读模型，包含稳定字段）
	Relation *models.ArchiveMediaRelationReadModel `json:"relation"`

	// 动态字段（来自媒体读模型）
	ImportantLevel        int    `json:"importantLevel"`        // 重要程度
	IsLocked              int    `json:"isLocked"`              // 是否锁定
	IsNonEnforcementMedia int    `json:"isNonEnforcementMedia"` // 是否非执法媒体
	EnforceType           int    `json:"enforceType"`           // 执法类型
	Comments              string `json:"comments"`              // 备注

	// 存储信息
	StoragePath string `json:"storagePath"` // 存储路径
	Md5         string `json:"md5"`         // 文件MD5
}

// GetArchiveRelationsWithMediaDetails 获取档案的所有关联关系及媒体详细信息
func (s *ArchiveMediaRelationQueryService) GetArchiveRelationsWithMediaDetails(ctx context.Context, archiveId int64, page, pageSize int) (*ArchiveMediaRelationWithDetailsList, error) {
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 1000 {
		pageSize = 20
	}

	// 1. 获取关联关系列表
	relations, err := s.relationRepo.FindByArchiveID(ctx, archiveId, page, pageSize)
	if err != nil {
		logger.Error("Failed to find relations by archive ID", "error", err, "archiveId", archiveId)
		return nil, fmt.Errorf("查询档案关联关系失败: %w", err)
	}

	if len(relations.List) == 0 {
		return &ArchiveMediaRelationWithDetailsList{
			List:  make([]*ArchiveMediaRelationWithDetails, 0),
			Total: 0,
		}, nil
	}

	// 2. 批量获取媒体详细信息
	var detailsList []*ArchiveMediaRelationWithDetails
	for _, relation := range relations.List {
		// 获取媒体详细信息
		media, err := s.mediaRepo.FindByID(ctx, relation.MediaId)
		if err != nil {
			logger.Error("Failed to find media by ID", "error", err, "mediaId", relation.MediaId)
			continue // 跳过失败的，继续处理其他的
		}

		if media == nil {
			logger.Warn("Media not found", "mediaId", relation.MediaId)
			continue
		}

		// 组合详细信息
		details := &ArchiveMediaRelationWithDetails{
			Relation:              &relation,
			ImportantLevel:        media.ImportantLevel,
			IsLocked:              media.IsLocked,
			IsNonEnforcementMedia: media.IsNonEnforcementMedia,
			EnforceType:           media.EnforceType,
			Comments:              media.Comments,
			StoragePath:           media.URI,     // 使用URI字段
			Md5:                   media.FileMd5, // 使用FileMd5字段
		}

		detailsList = append(detailsList, details)
	}

	return &ArchiveMediaRelationWithDetailsList{
		List:  detailsList,
		Total: relations.Total,
	}, nil
}

// ArchiveMediaRelationWithDetailsList 档案媒体关联及详细信息列表
type ArchiveMediaRelationWithDetailsList struct {
	List  []*ArchiveMediaRelationWithDetails `json:"list"`
	Total int64                              `json:"total"`
}
