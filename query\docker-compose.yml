version: '1.0'
services:
  jxt-evidence-query:
    container_name: jxt-evidence-query2
    image: jxt-evidence-query:latest
    privileged: true
    restart: always
    ports:
      - 8002:8002
    volumes:
      - ./config/:/jxt-evidence/config/
      - ./static/:/jxt-evidence/static/
      - ./temp/:/jxt-evidence/temp
    command: ./jxt-evidence-query  server -c ./config/settings.yml
    networks:
      - jxt-security-management_jxt_web

networks:
  jxt-security-management_jxt_web:
    external: true
