package testhelpers

import (
	"fmt"
	commandTesthelpers "jxt-evidence-system/evidence-management/command/testhelpers"
	queryMigrationModels "jxt-evidence-system/evidence-management/query/cmd/migrate/migration/models"
	queryTesthelpers "jxt-evidence-system/evidence-management/query/testhelpers"
	"net/http"
	"strings"
	"time"

	. "github.com/onsi/gomega"
	"gorm.io/gorm"
)

type ArchiveMediaRelationTestHelper struct {
	dbCommand *gorm.DB
	dbQuery   *gorm.DB
	suite     *TestSuite
}

func NewArchiveMediaRelationTestHelper(dbCommand, dbQuery *gorm.DB, suite *TestSuite) *ArchiveMediaRelationTestHelper {
	return &ArchiveMediaRelationTestHelper{
		dbCommand: dbCommand,
		dbQuery:   dbQuery,
		suite:     suite,
	}
}

// CreateTestRelation 创建测试关联并返回ID
func (h *ArchiveMediaRelationTestHelper) CreateTestRelation(archiveID, mediaID int64) (int64, error) {
	relation := commandTesthelpers.NewArchiveMediaRelationBuilder().
		WithArchiveId(archiveID).
		WithMediaId(mediaID).
		Build()

	result := h.dbCommand.Create(&relation)
	if result.Error != nil {
		return 0, result.Error
	}

	// 等待事件处理
	h.WaitForEventProcessing()

	return relation.ID, nil
}

// VerifyCreateRelationResponse 验证创建关联响应
func (h *ArchiveMediaRelationTestHelper) VerifyCreateRelationResponse(resp *http.Response) {
	Expect(resp.StatusCode).To(Equal(http.StatusOK))
	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(200)))
	Expect(body["msg"]).To(Equal("创建档案媒体关联成功"))
}

// VerifyBatchCreateRelationResponse 验证批量创建关联响应
func (h *ArchiveMediaRelationTestHelper) VerifyBatchCreateRelationResponse(resp *http.Response) {
	Expect(resp.StatusCode).To(Equal(http.StatusOK))
	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(200)))
	Expect(body["msg"]).To(Equal("批量创建档案媒体关联成功"))
}

// VerifyDeleteRelationResponse 验证删除关联响应
func (h *ArchiveMediaRelationTestHelper) VerifyDeleteRelationResponse(resp *http.Response) {
	Expect(resp.StatusCode).To(Equal(http.StatusOK))
	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(200)))
	Expect(body["msg"]).To(Equal("删除档案媒体关联成功"))
}

// VerifyBatchDeleteRelationResponse 验证批量删除关联响应
func (h *ArchiveMediaRelationTestHelper) VerifyBatchDeleteRelationResponse(resp *http.Response) {
	Expect(resp.StatusCode).To(Equal(http.StatusOK))
	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(200)))
	Expect(body["msg"]).To(Equal("批量删除档案媒体关联成功"))
}

// VerifyErrorResponse 验证错误响应
func (h *ArchiveMediaRelationTestHelper) VerifyErrorResponse(resp *http.Response, expectedMsgKeyword string) {
	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())

	// 打印调试信息
	fmt.Printf("错误响应调试 - 状态码: %d, body: %+v\n", resp.StatusCode, body)

	// 验证错误响应
	if resp.StatusCode == http.StatusOK {
		// 如果返回200，检查业务状态码
		if code, exists := body["code"]; exists {
			Expect(code).NotTo(Equal(float64(200)), "业务操作应该失败")
		}
	} else {
		// 如果不是200，检查HTTP状态码
		Expect(resp.StatusCode).To(BeNumerically(">=", 400), "应该返回错误状态码")
	}

	// 验证错误消息包含关键词
	if msg, exists := body["msg"]; exists {
		msgStr := msg.(string)
		Expect(strings.Contains(msgStr, expectedMsgKeyword)).To(BeTrue(),
			fmt.Sprintf("错误消息 '%s' 应该包含关键词 '%s'", msgStr, expectedMsgKeyword))
	}
}

// VerifyRelationInCommandDB 验证关联在命令数据库中存在
func (h *ArchiveMediaRelationTestHelper) VerifyRelationInCommandDB(archiveID, mediaID int64) {
	var count int64
	relation := commandTesthelpers.NewArchiveMediaRelationBuilder().Build()
	h.dbCommand.Model(relation).Where("archive_id = ? AND media_id = ?", archiveID, mediaID).Count(&count)
	Expect(count).To(Equal(int64(1)), fmt.Sprintf("档案媒体关联 (ArchiveID=%d, MediaID=%d) 应该在命令数据库中存在", archiveID, mediaID))
}

// VerifyRelationInQueryDB 验证关联在查询数据库中存在
func (h *ArchiveMediaRelationTestHelper) VerifyRelationInQueryDB(archiveID, mediaID int64) {
	// 使用重试机制等待事件处理完成
	Eventually(func() error {
		var relationRead queryMigrationModels.ArchiveMediaRelationReadModel
		return h.dbQuery.Where("archive_id = ? AND media_id = ?", archiveID, mediaID).First(&relationRead).Error
	}, 5*time.Second, 100*time.Millisecond).Should(Succeed(),
		fmt.Sprintf("档案媒体关联 (ArchiveID=%d, MediaID=%d) 应该通过事件同步到查询数据库", archiveID, mediaID))
}

// VerifyBatchRelationsInCommandDB 验证批量关联在命令数据库中存在
func (h *ArchiveMediaRelationTestHelper) VerifyBatchRelationsInCommandDB(archiveID int64, mediaIDs []int64) {
	var count int64
	relation := commandTesthelpers.NewArchiveMediaRelationBuilder().Build()
	h.dbCommand.Model(relation).Where("archive_id = ? AND media_id IN ?", archiveID, mediaIDs).Count(&count)
	Expect(count).To(Equal(int64(len(mediaIDs))),
		fmt.Sprintf("应该在命令数据库中找到 %d 条档案媒体关联记录", len(mediaIDs)))
}

// VerifyBatchRelationsInQueryDB 验证批量关联在查询数据库中存在
func (h *ArchiveMediaRelationTestHelper) VerifyBatchRelationsInQueryDB(archiveID int64, mediaIDs []int64) {
	// 使用重试机制等待事件处理完成
	Eventually(func() int64 {
		var count int64
		relationRead := queryTesthelpers.NewArchiveMediaRelationReadModelBuilder().Build()
		h.dbQuery.Model(relationRead).Where("archive_id = ? AND media_id IN ?", archiveID, mediaIDs).Count(&count)
		return count
	}, 5*time.Second, 100*time.Millisecond).Should(Equal(int64(len(mediaIDs))),
		fmt.Sprintf("应该通过事件同步在查询数据库中找到 %d 条档案媒体关联记录", len(mediaIDs)))
}

// VerifyRelationNotExistsInCommandDB 验证关联在命令数据库中不存在
func (h *ArchiveMediaRelationTestHelper) VerifyRelationNotExistsInCommandDB(relationID int64) {
	var count int64
	relation := commandTesthelpers.NewArchiveMediaRelationBuilder().Build()
	h.dbCommand.Model(relation).Where("id = ?", relationID).Count(&count)
	Expect(count).To(Equal(int64(0)), fmt.Sprintf("关联ID %d 应该在命令数据库中不存在", relationID))
}

// VerifyRelationNotExistsInQueryDB 验证关联在查询数据库中不存在
func (h *ArchiveMediaRelationTestHelper) VerifyRelationNotExistsInQueryDB(relationID int64) {
	var count int64
	relationRead := queryTesthelpers.NewArchiveMediaRelationReadModelBuilder().Build()
	h.dbQuery.Model(relationRead).Where("id = ?", relationID).Count(&count)
	Expect(count).To(Equal(int64(0)), fmt.Sprintf("关联ID %d 应该在查询数据库中不存在", relationID))
}

// WaitForEventProcessing 等待领域事件处理
func (h *ArchiveMediaRelationTestHelper) WaitForEventProcessing() {
	time.Sleep(300 * time.Millisecond)
}

// CleanupTestData 清理测试数据
func (h *ArchiveMediaRelationTestHelper) CleanupTestData() {
	// 清理命令端关联数据
	relation := commandTesthelpers.NewArchiveMediaRelationBuilder().Build()
	h.dbCommand.Unscoped().Where("1=1").Delete(relation)

	// 清理查询端关联数据
	relationRead := queryTesthelpers.NewArchiveMediaRelationReadModelBuilder().Build()
	h.dbQuery.Unscoped().Where("1=1").Delete(relationRead)
}
