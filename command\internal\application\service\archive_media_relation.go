package service

import (
	"context"
	"fmt"
	"jxt-evidence-system/evidence-management/command/internal/application/command"
	"jxt-evidence-system/evidence-management/command/internal/application/service/port"
	"jxt-evidence-system/evidence-management/command/internal/application/transaction"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archive/repository"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archivemediarelation"
	archiveMediaRelationRepo "jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archivemediarelation/repository"
	mediaRepo "jxt-evidence-system/evidence-management/command/internal/domain/aggregate/media/repository"
	"jxt-evidence-system/evidence-management/command/internal/domain/event/publisher"
	domain_service "jxt-evidence-system/evidence-management/command/internal/domain/service"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/eventbus"
	"jxt-evidence-system/evidence-management/shared/common/global"
	"jxt-evidence-system/evidence-management/shared/common/service"
	domain_event "jxt-evidence-system/evidence-management/shared/domain/event"
	event_repository "jxt-evidence-system/evidence-management/shared/domain/event/repository"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

func init() {
	registrations = append(registrations, registerArchiveMediaRelationServiceDependencies)
}

func registerArchiveMediaRelationServiceDependencies() {
	err := di.Provide(func(
		repo archiveMediaRelationRepo.ArchiveMediaRelationRepository,
		archiveRepo repository.ArchiveRepository,
		mediaRepository mediaRepo.MediaRepository,
		eventPublisher publisher.EventPublisher,
		eventRepo event_repository.DomainEventRepository,
		transactionManager transaction.TransactionManager,
		archiveMediaRelationDomainService domain_service.ArchiveMediaRelationDomainService) port.ArchiveMediaRelationService {
		return &archiveMediaRelationService{
			repo:                              repo,
			archiveRepo:                       archiveRepo,
			mediaRepo:                         mediaRepository,
			eventPublisher:                    eventPublisher,
			eventRepo:                         eventRepo,
			transactionManager:                transactionManager,
			archiveMediaRelationDomainService: archiveMediaRelationDomainService,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide ArchiveMediaRelationService: %v", err)
	}
}

type archiveMediaRelationService struct {
	service.Service
	repo                              archiveMediaRelationRepo.ArchiveMediaRelationRepository
	archiveRepo                       repository.ArchiveRepository
	mediaRepo                         mediaRepo.MediaRepository
	eventPublisher                    publisher.EventPublisher
	eventRepo                         event_repository.DomainEventRepository
	transactionManager                transaction.TransactionManager
	archiveMediaRelationDomainService domain_service.ArchiveMediaRelationDomainService
}

// createArchiveMediaRelationFromCommand 从command创建领域对象
func (s *archiveMediaRelationService) createArchiveMediaRelationFromCommand(cmd *command.CreateArchiveMediaRelationCommand) *archivemediarelation.ArchiveMediaRelation {
	relation := &archivemediarelation.ArchiveMediaRelation{
		ArchiveId: cmd.ArchiveId,
		MediaId:   cmd.DocumentId, // DocumentId 对应 MediaId
	}

	if cmd.CreateBy != 0 {
		relation.CreateBy = cmd.CreateBy
	}
	if cmd.UpdateBy != 0 {
		relation.UpdateBy = cmd.UpdateBy
	}

	return relation
}

// CreateArchiveMediaRelation 创建档案媒体关联
func (s *archiveMediaRelationService) CreateArchiveMediaRelation(ctx context.Context, cmd *command.CreateArchiveMediaRelationCommand) error {
	// 输入参数验证
	if cmd.ArchiveId <= 0 {
		s.GetLogger(ctx).Error("档案ID无效", zap.Int64("archiveId", cmd.ArchiveId))
		return fmt.Errorf("档案ID必须大于0")
	}
	if cmd.DocumentId <= 0 {
		s.GetLogger(ctx).Error("文档ID无效", zap.Int64("documentId", cmd.DocumentId))
		return fmt.Errorf("文档ID必须大于0")
	}

	// 使用事务保证数据一致性
	err := transaction.RunInTransaction(ctx, s.transactionManager, func(tx transaction.Transaction) error {
		// 验证档案是否存在
		archive, err := s.archiveRepo.FindByID(ctx, cmd.ArchiveId)
		if err != nil {
			s.GetLogger(ctx).Error("查询档案失败", zap.Error(err), zap.Int64("archiveId", cmd.ArchiveId))
			return fmt.Errorf("查询档案失败: %w", err)
		}
		if archive == nil {
			return fmt.Errorf("档案[%d]不存在", cmd.ArchiveId)
		}

		// 验证媒体是否存在
		media, err := s.mediaRepo.FindByID(ctx, cmd.DocumentId)
		if err != nil {
			s.GetLogger(ctx).Error("查询媒体失败", zap.Error(err), zap.Int64("mediaId", cmd.DocumentId))
			return fmt.Errorf("查询媒体失败: %w", err)
		}
		if media == nil {
			return fmt.Errorf("媒体[%d]不存在", cmd.DocumentId)
		}

		// 检查关联关系是否已存在
		existingRelation, err := s.repo.FindByArchiveAndMedia(ctx, cmd.ArchiveId, cmd.DocumentId)
		if err != nil {
			s.GetLogger(ctx).Error("查询关联关系失败", zap.Error(err))
			return fmt.Errorf("查询关联关系失败: %w", err)
		}
		if existingRelation != nil {
			return fmt.Errorf("档案[%d]与媒体[%d]的关联关系已存在", cmd.ArchiveId, cmd.DocumentId)
		}

		// 创建领域对象
		relation := s.createArchiveMediaRelationFromCommand(cmd)

		// 使用聚合根方法创建关联并生成事件
		err = relation.CreateArchiveMediaRelationAndSave()
		if err != nil {
			s.GetLogger(ctx).Error("创建关联业务规则验证失败", zap.Error(err))
			return fmt.Errorf("创建关联业务验证失败: %w", err)
		}

		// 持久化到数据库
		err = s.repo.Create(ctx, relation)
		if err != nil {
			s.GetLogger(ctx).Error("创建关联数据库操作失败", zap.Error(err))
			return fmt.Errorf("创建关联失败: %w", err)
		}

		// 在数据库保存后创建事件（此时ID已设置）
		relation.CreateCreatedEventAfterSave()

		// 发布事件
		if err := s.publishEvents(ctx, relation); err != nil {
			s.GetLogger(ctx).Error("发布ArchiveMediaRelationCreatedEvent失败", zap.Error(err))
			// 事件发布失败不阻断事务，但需要记录
		}

		return nil
	})

	if err != nil {
		s.GetLogger(ctx).Error("创建档案媒体关联事务失败", zap.Error(err))
		return fmt.Errorf("创建档案媒体关联失败: %w", err)
	}

	s.GetLogger(ctx).Info("档案媒体关联创建成功",
		zap.Int64("archiveId", cmd.ArchiveId),
		zap.Int64("documentId", cmd.DocumentId))
	return nil
}

// DeleteArchiveMediaRelationByID 删除档案媒体关联
func (s *archiveMediaRelationService) DeleteArchiveMediaRelationByID(ctx context.Context, id int64) error {
	// 输入参数验证
	if id <= 0 {
		s.GetLogger(ctx).Error("关联ID无效", zap.Int64("relationId", id))
		return fmt.Errorf("关联ID必须大于0")
	}

	// 使用事务保证数据一致性
	err := transaction.RunInTransaction(ctx, s.transactionManager, func(tx transaction.Transaction) error {
		// 从数据库中获取完整的关联记录
		existingRelation, err := s.repo.FindByID(ctx, id)
		if err != nil {
			s.GetLogger(ctx).Error("查询关联失败", zap.Error(err), zap.Int64("relationId", id))
			return fmt.Errorf("查询关联失败: %w", err)
		}
		if existingRelation == nil {
			return fmt.Errorf("待删除的关联[%d]不存在", id)
		}

		// 使用聚合根方法删除关联并创建事件
		err = existingRelation.DeleteArchiveMediaRelation()
		if err != nil {
			s.GetLogger(ctx).Error("删除关联业务规则验证失败", zap.Error(err))
			return fmt.Errorf("删除关联业务验证失败: %w", err)
		}

		// 删除记录
		err = s.repo.DeleteByID(ctx, id)
		if err != nil {
			s.GetLogger(ctx).Error("删除关联数据库操作失败", zap.Error(err))
			return fmt.Errorf("删除关联失败: %w", err)
		}

		// 发布事件
		if err := s.publishEvents(ctx, existingRelation); err != nil {
			s.GetLogger(ctx).Error("发布ArchiveMediaRelationDeletedEvent失败", zap.Error(err))
			// 事件发布失败不阻断事务，但需要记录
		}

		return nil
	})

	if err != nil {
		s.GetLogger(ctx).Error("删除档案媒体关联事务失败", zap.Error(err))
		return fmt.Errorf("删除档案媒体关联失败: %w", err)
	}

	s.GetLogger(ctx).Info("档案媒体关联删除成功", zap.Int64("relationId", id))
	return nil
}

// BatchCreateArchiveMediaRelation 批量创建档案媒体关联 - 通过领域服务实现
func (s *archiveMediaRelationService) BatchCreateArchiveMediaRelation(ctx context.Context, cmd *command.BatchCreateArchiveMediaRelationCommand) error {
	// 输入参数验证
	if cmd.ArchiveId <= 0 {
		s.GetLogger(ctx).Error("档案ID无效", zap.Int64("archiveId", cmd.ArchiveId))
		return fmt.Errorf("档案ID必须大于0")
	}
	if len(cmd.DocumentIds) == 0 {
		s.GetLogger(ctx).Error("文档ID列表为空")
		return fmt.Errorf("文档ID列表不能为空")
	}

	// 使用领域服务处理批量创建业务逻辑
	newCtx, validDocumentIds, relations, err := s.archiveMediaRelationDomainService.BatchCreateArchiveMediaRelation(
		ctx, cmd.ArchiveId, cmd.DocumentIds, cmd.CreateBy)
	if err != nil {
		s.GetLogger(ctx).Error("调用领域服务批量创建档案媒体关联失败", zap.Error(err))
		return fmt.Errorf("批量创建档案媒体关联失败: %w", err)
	}

	// 使用事务保证数据一致性
	err = transaction.RunInTransaction(newCtx, s.transactionManager, func(tx transaction.Transaction) error {
		// 批量创建
		err = s.repo.BatchCreate(newCtx, relations)
		if err != nil {
			s.GetLogger(ctx).Error("批量创建关联数据库操作失败", zap.Error(err))
			return fmt.Errorf("批量创建关联失败: %w", err)
		}

		// 数据库保存后，relations中的ID已经被设置，现在重新构建批量创建事件
		batchCreatedEvent := s.createArchiveMediaRelationBatchCreatedEventWithIDs(cmd.ArchiveId, relations, cmd.CreateBy)
		if batchCreatedEvent != nil {
			// 从上下文中获取租户ID
			tenantID := newCtx.Value(global.TenantIDKey)
			if tenantID != nil {
				batchCreatedEvent.SetTenantId(tenantID.(string))
			}

			if err := s.eventPublisher.Publish(newCtx, eventbus.ArchiveMediaRelationEventTopic, batchCreatedEvent); err != nil {
				s.GetLogger(ctx).Error("发布ArchiveMediaRelationBatchCreatedEvent失败", zap.Error(err))
				// 事件发布失败不阻断事务，但需要记录
			}
		}

		return nil
	})

	if err != nil {
		s.GetLogger(ctx).Error("批量创建档案媒体关联事务失败", zap.Error(err))
		return fmt.Errorf("批量创建档案媒体关联失败: %w", err)
	}

	s.GetLogger(ctx).Info("批量创建档案媒体关联成功",
		zap.Int64("archiveId", cmd.ArchiveId),
		zap.Int64s("documentIds", validDocumentIds),
		zap.Int("createdCount", len(relations)))

	return nil
}

// BatchDeleteArchiveMediaRelation 批量删除档案媒体关联 - 通过领域服务实现
func (s *archiveMediaRelationService) BatchDeleteArchiveMediaRelation(ctx context.Context, cmd *command.BatchDeleteArchiveMediaRelationCommand) error {
	// 输入参数验证
	if len(cmd.IDs) == 0 {
		s.GetLogger(ctx).Error("关联ID列表为空")
		return fmt.Errorf("关联ID列表不能为空")
	}

	// 使用领域服务处理批量删除业务逻辑
	newCtx, validIds, err := s.archiveMediaRelationDomainService.BatchDeleteArchiveMediaRelation(
		ctx, cmd.IDs, cmd.UpdateBy)
	if err != nil {
		s.GetLogger(ctx).Error("调用领域服务批量删除档案媒体关联失败", zap.Error(err))
		return fmt.Errorf("批量删除档案媒体关联失败: %w", err)
	}

	// 使用事务保证数据一致性
	err = transaction.RunInTransaction(newCtx, s.transactionManager, func(tx transaction.Transaction) error {
		// 批量删除
		rowsAffected, err := s.repo.BatchDeleteByIDs(newCtx, validIds)
		if err != nil {
			s.GetLogger(ctx).Error("批量删除关联数据库操作失败", zap.Error(err))
			return fmt.Errorf("批量删除关联失败: %w", err)
		}

		if rowsAffected == 0 {
			return fmt.Errorf("没有关联被删除")
		}

		// 发布领域服务创建的事件
		if err := s.publishEventsFromContext(newCtx); err != nil {
			s.GetLogger(ctx).Error("发布ArchiveMediaRelationBatchDeletedEvent失败", zap.Error(err))
			// 事件发布失败不阻断事务，但需要记录
		}

		return nil
	})

	if err != nil {
		s.GetLogger(ctx).Error("批量删除档案媒体关联事务失败", zap.Error(err))
		return fmt.Errorf("批量删除档案媒体关联失败: %w", err)
	}

	s.GetLogger(ctx).Info("批量删除档案媒体关联成功",
		zap.Int64s("relationIds", validIds),
		zap.Int64("deletedCount", int64(len(validIds))))

	return nil
}

// publishEvents 发布领域事件
func (s *archiveMediaRelationService) publishEvents(ctx context.Context, aggregate interface{}) error {
	type EventsProvider interface {
		Events() []domain_event.Event
		ClearEvents()
	}

	if provider, ok := aggregate.(EventsProvider); ok {
		events := provider.Events()
		for _, event := range events {
			// 从上下文中获取租户ID
			tenantID := ctx.Value(global.TenantIDKey)
			if tenantID == nil {
				return fmt.Errorf("无法从上下文中获取租户ID")
			}
			event.SetTenantId(tenantID.(string))

			if err := s.eventPublisher.Publish(ctx, eventbus.ArchiveMediaRelationEventTopic, event); err != nil {
				// 发布失败要持久化领域事件
				if domainEvent, ok := event.(*domain_event.DomainEvent); ok {
					err = s.eventRepo.Save(ctx, domainEvent)
					if err != nil {
						return fmt.Errorf("领域事件数据库持久化失败! %s", err)
					}
				} else {
					return fmt.Errorf("event conversion failed! %+v", event)
				}

				return fmt.Errorf("发布事件失败: %v", err)
			}
		}
		provider.ClearEvents()
	}
	return nil
}

// publishEventsFromContext 从上下文中获取并发布领域事件
func (s *archiveMediaRelationService) publishEventsFromContext(ctx context.Context) error {
	// 从上下文中获取领域事件
	events := domain_event.GetEventsFromContext(ctx)
	if len(events) == 0 {
		s.GetLogger(ctx).Warn("上下文中没有领域事件，无需发布")
		return nil
	}

	// 从上下文中获取租户ID
	tenantID := ctx.Value(global.TenantIDKey)
	if tenantID == nil {
		return fmt.Errorf("无法从上下文中获取租户ID")
	}

	// 发布所有事件
	for _, event := range events {
		// 设置租户ID
		event.SetTenantId(tenantID.(string))

		if err := s.eventPublisher.Publish(ctx, eventbus.ArchiveMediaRelationEventTopic, event); err != nil {
			s.GetLogger(ctx).Error("发布领域事件失败，尝试写入数据库",
				zap.Error(err),
				zap.String("EventID", event.GetEventID()),
				zap.String("EventType", event.GetEventType()))

			// 发布失败需要持久化领域事件
			if domainEvent, ok := event.(*domain_event.DomainEvent); ok {
				err = s.eventRepo.Save(ctx, domainEvent)
				if err != nil {
					return fmt.Errorf("领域事件数据库持久化失败! %s", err)
				}
				// 保存成功，清空上下文中的事件
				domain_event.ClearEventsFromContext(ctx)
			} else {
				s.GetLogger(ctx).Error("事件类型转换失败", zap.Any("Event", event))
				return fmt.Errorf("event conversion failed! %+v", event)
			}
			return fmt.Errorf("发布事件失败: %v", err)
		}
	}

	// 发布成功，清空上下文中的事件
	domain_event.ClearEventsFromContext(ctx)

	return nil
}

// createArchiveMediaRelationBatchCreatedEventWithIDs 使用保存后的关联对象创建批量创建事件
func (s *archiveMediaRelationService) createArchiveMediaRelationBatchCreatedEventWithIDs(archiveId int64, relations []*archivemediarelation.ArchiveMediaRelation, createBy int) domain_event.Event {
	if len(relations) == 0 {
		s.GetLogger(context.Background()).Error("关联列表为空，无法创建批量创建事件")
		return nil
	}

	// 构建关联关系列表用于事件载荷
	var relationPayloads []domain_event.ArchiveMediaRelationCreatedPayload
	for _, relation := range relations {
		relationPayloads = append(relationPayloads, domain_event.ArchiveMediaRelationCreatedPayload{
			ID:        relation.ID, // 这里的ID已经从数据库保存后获得
			ArchiveId: relation.ArchiveId,
			MediaId:   relation.MediaId,
			CreateBy:  relation.CreateBy,
			UpdateBy:  relation.UpdateBy,
			CreatedAt: relation.CreatedAt,
			UpdatedAt: relation.UpdatedAt,
		})
	}

	payload := domain_event.ArchiveMediaRelationBatchCreatedPayload{
		Relations: relationPayloads,
		CreateBy:  createBy,
		CreatedAt: time.Now(),
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		s.GetLogger(context.Background()).Error("无法将 ArchiveMediaRelationBatchCreatedPayload 转换为 JSON", zap.Error(err))
		return nil
	}

	// 使用档案ID作为聚合根ID
	return domain_event.NewDomainEvent(domain_event.EventTypeArchiveMediaRelationBatchCreated, archiveId, "ArchiveMediaRelation", payloadJSON)
}
