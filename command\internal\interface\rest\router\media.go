package router

import (
	"jxt-evidence-system/evidence-management/command/internal/interface/rest/api"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/middleware"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	jwt "github.com/ChenBigdata421/jxt-core/sdk/pkg/jwtauth"
	"github.com/gin-gonic/gin"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerMediaRouter)
}

func registerMediaRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	// jiyuanjie 添加通过依赖注入创建的repo，service，api，代替前面的手工创建
	// 解析 MediaApi

	err := di.Invoke(func(handler *api.MediaHandler) {
		if handler != nil {
			r := v1.Group("/media").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
			{
				r.POST("", handler.MediaUpload)
				r.PUT("/:id", handler.MediaUpdateByID)
				r.DELETE("/:id", handler.MediaDeleteByID)

				r.PUT("/batch", handler.MediaBatchUpdate)
				r.DELETE("/batch", handler.MediaBatchDelete)
				r.POST("/batch/mark-no-enforcement-media-status", handler.MarkNonEnforcementMediaStatus) // 新添加的路由
				r.POST("/batch/update-enforce-type", handler.BatchUpdateEnforceType)                     // 批量更新执法类型路由
				r.POST("/batch/update-is-locked", handler.BatchUpdateIsLocked)                           // 批量更新锁定状态路由
			}
		} else {
			logger.Fatal("MediaHandler is nil after resolution")
		}
	})

	if err != nil {
		logger.Fatalf("Failed to resolve MediaHandler: %v", err)
	}
}
