package testhelpers

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
)

// ReadResponseBody 从响应中读取响应体内容，但不关闭响应体
func ReadResponseBody(response *http.Response) ([]byte, error) {
	return io.ReadAll(response.Body)
}

// ParseResponse 解析响应体为指定类型
func ParseResponse[T any](response *http.Response) (T, error) {
	var result T
	
	// 读取响应体
	body, err := ReadResponseBody(response)
	if err != nil {
		return result, err
	}
	
	// 重置响应体，以便后续可以再次读取
	response.Body = io.NopCloser(bytes.NewBuffer(body))
	
	// 解析JSON
	err = json.Unmarshal(body, &result)
	return result, err
}

// GetResponseBody 获取响应体的map形式
func GetResponseBody(response *http.Response) (map[string]interface{}, error) {
	var result map[string]interface{}
	
	// 读取响应体
	body, err := ReadResponseBody(response)
	if err != nil {
		return nil, err
	}
	
	// 重置响应体，以便后续可以再次读取
	response.Body = io.NopCloser(bytes.NewBuffer(body))
	
	// 解析JSON
	err = json.Unmarshal(body, &result)
	return result, err
}

// ExpectSuccess 检查响应是否成功
func ExpectSuccess(response *http.Response) bool {
	return response.StatusCode >= 200 && response.StatusCode < 300
}

// ExpectError 检查响应是否为错误
func ExpectError(response *http.Response) bool {
	return response.StatusCode >= 400
}

// GetErrorMessage 获取错误消息
func GetErrorMessage(response *http.Response) (string, error) {
	body, err := GetResponseBody(response)
	if err != nil {
		return "", err
	}
	if msg, ok := body["msg"].(string); ok {
		return msg, nil
	}
	return "", nil
}
