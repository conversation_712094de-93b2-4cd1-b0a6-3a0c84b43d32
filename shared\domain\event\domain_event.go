package event

import (
	"time"

	jsoniter "github.com/json-iterator/go"

	"github.com/ThreeDotsLabs/watermill"
)

// 实现了Event接口
type DomainEvent struct {
	TenantId      string    `json:"tenantId" gorm:"type:varchar(255);index;column:tenant_id;comment:租户ID"`
	EventID       string    `json:"eventId" gorm:"type:char(36);primary_key;column:event_id;comment:事件ID"`
	EventType     string    `json:"eventType" gorm:"type:varchar(255);index;column:event_type;comment:事件类型"`
	OccurredAt    time.Time `json:"occurredAt" gorm:"type:datetime;index;column:occurred_at;comment:事件发生时间"`
	Version       int       `json:"version" gorm:"type:int;column:event_version;comment:事件版本"`
	AggregateID   int64     `json:"aggregateId" gorm:"type:bigint;index;column:aggregate_id;comment:聚合根ID"`
	AggregateType string    `json:"aggregateType" gorm:"type:varchar(255);index;column:aggregate_type;comment:聚合根类型"`
	Payload       []byte    `json:"payload" gorm:"type:json;column:event_payload;comment:事件载荷"`
}

func NewDomainEvent(eventType string, aggregateID int64, aggregateType string, payload []byte) *DomainEvent {
	return &DomainEvent{
		EventID:       watermill.NewUUID(),
		EventType:     eventType,
		OccurredAt:    time.Now(),
		Version:       1,
		AggregateID:   aggregateID,
		AggregateType: aggregateType,
		Payload:       payload,
	}
}

func (e *DomainEvent) TableName() string {
	return "t_evidence_domain_events"
}

func (e *DomainEvent) GetEventID() string          { return e.EventID }
func (e *DomainEvent) GetEventType() string        { return e.EventType }
func (e *DomainEvent) GetOccurredAt() time.Time    { return e.OccurredAt }
func (e *DomainEvent) GetVersion() int             { return e.Version }
func (e *DomainEvent) GetAggregateID() interface{} { return e.AggregateID }
func (e *DomainEvent) GetAggregateType() string    { return e.AggregateType }
func (e *DomainEvent) GetTenantId() string         { return e.TenantId }
func (e *DomainEvent) SetTenantId(tenantId string) { e.TenantId = tenantId }

// MarshalJSON 自定义序列化方法
func (e *DomainEvent) MarshalJSON() ([]byte, error) {
	type Alias DomainEvent
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	aux := struct {
		*Alias
		OccurredAt string              `json:"occurredAt"`
		Payload    jsoniter.RawMessage `json:"payload,omitempty"`
	}{
		Alias:      (*Alias)(e),
		OccurredAt: e.OccurredAt.Format(time.RFC3339),
	}

	// 对 Payload 进行 nil 检查
	if e.Payload != nil {
		aux.Payload = jsoniter.RawMessage(e.Payload)
	}

	return json.Marshal(aux)
}

// UnmarshalJSON 自定义反序列化方法
func (e *DomainEvent) UnmarshalJSON(data []byte) error {
	type Alias DomainEvent
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	aux := &struct {
		*Alias
		OccurredAt string              `json:"occurredAt"`
		Payload    jsoniter.RawMessage `json:"payload"`
	}{
		Alias: (*Alias)(e),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// 解析 OccurredAt
	if aux.OccurredAt != "" {
		t, err := time.Parse(time.RFC3339, aux.OccurredAt)
		if err != nil {
			return err
		}
		e.OccurredAt = t
	}

	// 处理 Payload
	if len(aux.Payload) > 0 {
		e.Payload = []byte(aux.Payload)
	} else {
		e.Payload = nil
	}

	return nil
}
