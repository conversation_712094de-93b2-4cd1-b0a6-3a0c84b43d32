package grpc_client

import (
	"jxt-evidence-system/evidence-management/shared/common/di"
	grpc_client "jxt-evidence-system/evidence-management/shared/common/grpc/client"
	client_port "jxt-evidence-system/evidence-management/shared/common/grpc/client/port"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

// 注册用户服务适配器的依赖注入
func init() {
	registrations = append(registrations, registerUserInfoServiceClientDependencies)
}

// registerUserInfoServiceClientDependencies 注册UserInfoServiceClient的依赖注入
func registerUserInfoServiceClientDependencies() {
	// 注册用户服务适配器 - 依赖已经注册的ConnectionManager
	if err := di.Provide(func(connManager *grpc_client.ConnectionManager) client_port.UserInfoServiceClient {
		logger.Info("创建用户信息服务客户端")
		return grpc_client.NewUserInfoServiceClient(connManager)
	}); err != nil {
		logger.Fatalf("注册用户信息服务客户端失败: %v", err)
	}
}
