version: '1.0'  
services:  
  kafka:  
    user: root  
    image: confluentinc/cp-kafka:latest  
    environment:  
      KAFKA_KRAFT_MODE: "true"  # 启用Kraft模式  
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: "PLAINTEXT:PLAINTEXT,CONTROLLER:PLAINTEXT"  #定义监听器的安全协议。这里指定了 PLAINTEXT 协议用于普通通信和控制器通信。
      KAFKA_LISTENERS: "PLAINTEXT://0.0.0.0:9092,CONTROLLER://0.0.0.0:9093"  #指定 Kafka 监听所有接口的地址。普通 broker 通信使用 9092 端口，控制器通信使用 9093 端口。
      KAFKA_ADVERTISED_LISTENERS: "PLAINTEXT://kafka:9092"   # Kafka 对外公布的监听地址。其他客户端使用该地址进行连接。
      KAFKA_LOG_DIRS: "/var/lib/kafka/data"  # Kafka日志存储目录
      KAFKA_BROKER_ID: "1"  #设置 broker 的 ID 为 1，用于标识在 Kafka 集群中的身份。
      KAFKA_CONFLUENT_SUPPORT_METRICS_ENABLED: "false"  # 禁用 Confluent 支持的度量功能，以简化测试或非生产环境。
      KAFKA_PROCESS_ROLES: "broker,controller"  #设置 Kafka 进程角色，执行 broker 和 controller 的职责。
      KAFKA_CONTROLLER_QUORUM_VOTERS: "1@localhost:9093"  #指定节点作为 Quorum Voters 授权 controller 操作，在 Kraft 模式下使用。
      KAFKA_CONTROLLER_LISTENER_NAMES: "CONTROLLER"  #定义 Kafka 使用哪个 listener 名称来连接其他 controllers。
      CLUSTER_ID: "91270774-4616-477e-a011-1e790c803fab"  #明确设定 Kafka 集群的 ID。
    volumes:  
      - kafka-data:/var/lib/kafka/data  # 持久化Kafka数据
      - kafka-metadata:/var/lib/kafka/metadata  
    ports:  
      - "9092:9092"  
      - "9093:9093"  

    command: >  
      bash -c '  
        mkdir -p /var/lib/kafka/data /var/lib/kafka/metadata &&  
        chown -R appuser:appuser /var/lib/kafka &&  
        echo "Initializing Kafka storage..." &&  
        if [ ! -f /var/lib/kafka/metadata/meta.properties ]; then  
          kafka-storage format --config /etc/kafka/kraft/server.properties --cluster-id "$${CLUSTER_ID}" --ignore-formatted  
        else  
          echo "meta.properties found, skipping format."  
        fi &&  
        echo "Checking data directory contents:" &&  
        ls -la /var/lib/kafka/data &&  
        echo "Checking metadata directory contents:" &&  
        ls -la /var/lib/kafka/metadata &&  
        if [ -f /var/lib/kafka/metadata/meta.properties ]; then  
          echo "meta.properties content:" &&  
          cat /var/lib/kafka/metadata/meta.properties;  
        else  
          echo "meta.properties not found in metadata directory";  
        fi &&  
        echo "Starting Kafka..." &&  
        su appuser -c "/etc/confluent/docker/run"  
      '
    networks:
      - jxt-security-management_jxt_web

volumes:  
  kafka-data:  
  kafka-metadata:  

networks:  
  jxt-security-management_jxt_web:  
    external: true  