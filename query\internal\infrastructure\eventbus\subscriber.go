package eventbus

import (
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/eventbus"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"jxt-evidence-system/evidence-management/query/internal/application/eventhandler"

	"github.com/ThreeDotsLabs/watermill/message"
)

// jiyuanjie 添加通过依赖注入创建service
func init() {
	registrations = append(registrations, registerKafkaEventSubscriberDependencies)
}

// jiyuanjie GormMediaReadModelRepository的依赖注入
func registerKafkaEventSubscriberDependencies() {
	if err := di.Provide(func() eventhandler.EventSubscriber {
		return &KafkaEventSubscriber{
			kafkaManager: eventbus.DefaultKafkaSubscriberManager,
		}
	}); err != nil {
		logger.Fatalf("failed to provide KafkaEventPublisher: %v", err)
	}
}

// 实现subscriber.EventSubscriber接口
type KafkaEventSubscriber struct {
	kafkaManager *eventbus.KafkaSubscriberManager //由于 kafkaSubscriberManager 在创建后不会改变，所以多个 goroutine 同时读取它是安全的。
}

// 订阅事件
func (k *KafkaEventSubscriber) Subscribe(topic string, handler func(msg *message.Message) error, timeout time.Duration) error {

	if err := k.kafkaManager.SubscribeToTopic(topic, handler, timeout); err != nil {
		logger.Infof("Failed to subscribe to topic %s: %v", topic, err)
		return err
	}

	return nil
}

// jiyuanjie 在这里也可以实现基于其它组件例如NATS jetstream的eventSubscriber
