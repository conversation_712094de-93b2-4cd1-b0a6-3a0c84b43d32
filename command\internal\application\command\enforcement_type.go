package command

import (
	common "jxt-evidence-system/evidence-management/shared/common/models"
)

// CreateEnforcementTypeCommand 创建执法类型命令
type CreateEnforcementTypeCommand struct {
	EnforcementTypeCode string `json:"enforceTypeCode" binding:"required"` //执行类型编码
	EnforcementTypeName string `json:"enforceTypeName" binding:"required"` //执行类型名称
	EnforcementTypeDesc string `json:"enforceTypeDesc"`                    //执行类型描述
	EnforcementTypePath string `json:"enforceTypePath"`                    //执法类型路径
	ParentId            int64  `json:"parentId"`                           //上级部门
	Source              string `json:"source"`                             //执法类型来源
	Sort                int    `json:"sort"`                               //排序
	common.ControlBy           //由服务端根据登录用户设置
}

// UpdateEnforcementTypeCommand 更新执法类型命令
type UpdateEnforcementTypeCommand struct {
	ID                  int64   `json:"id" binding:"required"`              //主键ID
	EnforcementTypeCode string  `json:"enforceTypeCode" binding:"required"` //执行类型编码
	EnforcementTypeName *string `json:"enforceTypeName"`                    //执行类型名称
	EnforcementTypeDesc *string `json:"enforceTypeDesc"`                    //执行类型描述
	EnforcementTypePath *string `json:"enforceTypePath"`                    //执法类型路径
	ParentId            *int64  `json:"parentId"`                           //上级部门
	Source              *string `json:"source"`                             //执法类型来源
	Sort                *int    `json:"sort"`                               //排序
	common.ControlBy            //由服务端根据登录用户设置
}
