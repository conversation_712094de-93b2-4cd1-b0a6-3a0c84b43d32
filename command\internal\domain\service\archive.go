package domain_service

import (
	"context"
	"errors"
	"fmt"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archive/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"
	domain_event "jxt-evidence-system/evidence-management/shared/domain/event"
	"strings"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	jsoniter "github.com/json-iterator/go"

	"go.uber.org/zap"
)

func init() {
	registrations = append(registrations, registerArchiveDomainServiceDependencies)
}

func registerArchiveDomainServiceDependencies() {
	err := di.Provide(func(repo repository.ArchiveRepository) ArchiveDomainService {
		return &archiveDomainService{
			repo:   repo,
			events: make([]domain_event.Event, 0),
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide ArchiveDomainService: %v", err)
	}
}

// ArchiveDomainService 档案领域服务接口
type ArchiveDomainService interface {
	// BatchUpdateArchive 批量更新档案
	BatchUpdateArchive(ctx context.Context, ids []int64, updates map[string]interface{}) (context.Context, []int64, error)

	// BatchDeleteArchive 批量删除档案
	BatchDeleteArchive(ctx context.Context, ids []int64, updateBy int) (context.Context, []int64, error)

	// BatchUpdateArchiveStatus 批量更新档案状态
	BatchUpdateArchiveStatus(ctx context.Context, ids []int64, status int, updateBy int, updatedAt time.Time) (context.Context, []int64, error)

	// BatchUpdateArchiveExpiration 批量更新档案过期时间
	BatchUpdateArchiveExpiration(ctx context.Context, ids []int64, expirationTime time.Time, updateBy int, updatedAt time.Time) (context.Context, []int64, error)

	// BatchUpdateArchiveStorageDuration 批量更新档案保存期限
	BatchUpdateArchiveStorageDuration(ctx context.Context, ids []int64, storageDuration int, updateBy int, updatedAt time.Time) (context.Context, []int64, error)
}

// archiveDomainService 档案领域服务实现，不负责持久化操作，领域服务必须无状态
type archiveDomainService struct {
	repo   repository.ArchiveRepository
	events []domain_event.Event //ai认为不合理，领域服务声明为无状态，但却包含了 events 字段，这违反了DDD原则，可能导致并发问题。
}

func NewArchiveDomainService(repo repository.ArchiveRepository) ArchiveDomainService {
	return &archiveDomainService{
		repo:   repo,
		events: make([]domain_event.Event, 0),
	}
}

// BatchUpdateArchive 批量更新档案
func (s *archiveDomainService) BatchUpdateArchive(ctx context.Context, ids []int64, updates map[string]interface{}) (context.Context, []int64, error) {
	if len(ids) == 0 {
		return ctx, nil, errors.New("档案ID列表不能为空")
	}

	if len(updates) == 0 {
		return ctx, nil, errors.New("更新字段不能为空")
	}

	// 验证档案是否可以更新，过滤掉不能更新的档案
	var validIds []int64
	for _, id := range ids {
		archive, err := s.repo.FindByID(ctx, id)
		if err != nil {
			logger.Error("查询档案失败", zap.Error(err), zap.Int64("archiveId", id))
			continue
		}

		if archive == nil {
			logger.Warn("档案不存在，无法更新", zap.Int64("archiveId", id))
			continue
		}

		// 检查档案状态：已删除的档案不能更新
		if archive.Status == 1 {
			logger.Warn("档案已删除，跳过更新", zap.Int64("archiveId", id))
			continue
		}

		validIds = append(validIds, id)
	}

	// 更新ids为有效的档案ID列表
	ids = validIds

	// 如果过滤后没有可更新的档案，则返回
	if len(ids) == 0 {
		logger.Error("没有可更新的档案")
		return ctx, nil, errors.New("没有可更新的档案")
	}

	// 添加审计字段
	if _, ok := updates["UpdateBy"]; !ok {
		updates["UpdateBy"] = 0 // 默认值，实际应用中应从上下文获取，当前代码中不会出现使用默认值
	}

	if _, ok := updates["UpdatedAt"]; !ok {
		updates["UpdatedAt"] = time.Now()
	}

	// 创建批量更新事件
	updateEvent := s.createArchiveBatchUpdatedEvent(ids, updates)
	if updateEvent == nil {
		logger.Error("创建批量更新事件失败")
		return ctx, nil, errors.New("创建批量更新事件失败")
	}

	// 将事件添加到上下文中
	newCtx := domain_event.AddEventToContext(ctx, updateEvent)

	return newCtx, ids, nil
}

// BatchDeleteArchive 批量删除档案
func (s *archiveDomainService) BatchDeleteArchive(ctx context.Context, ids []int64, updateBy int) (context.Context, []int64, error) {
	if len(ids) == 0 {
		logger.Error("档案ID列表不能为空")
		return ctx, nil, errors.New("档案ID列表不能为空")
	}

	var deleteIds []int64

	// 批量删除的领域逻辑
	for _, id := range ids {
		// 查询档案是否存在
		archive, err := s.repo.FindByID(ctx, id)
		if err != nil {
			logger.Error("查询档案失败", zap.Error(err), zap.Int64("archiveId", id))
			continue
		}

		if archive == nil {
			logger.Warn("档案不存在，无法删除", zap.Int64("archiveId", id))
			continue
		}

		// 已删除的档案不能再次删除
		if archive.Status == 1 {
			logger.Warn("档案已删除，不能再次删除", zap.Int64("archiveId", id))
			continue
		}

		// 检查是否有关联的媒体文件（业务规则）
		// 这里可以添加更多业务规则检查

		deleteIds = append(deleteIds, id)
	}

	// 如果没有可删除的档案，返回错误
	if len(deleteIds) == 0 {
		logger.Error("没有可删除的档案")
		return ctx, nil, errors.New("没有可删除的档案")
	}

	// 创建批量删除事件
	deleteEvent := s.createArchiveBatchDeletedEvent(deleteIds, updateBy)
	if deleteEvent == nil {
		logger.Error("创建批量删除事件失败")
		return ctx, nil, errors.New("创建批量删除事件失败")
	}

	// 将事件添加到上下文中
	newCtx := domain_event.AddEventToContext(ctx, deleteEvent)

	return newCtx, deleteIds, nil
}

// BatchUpdateArchiveStatus 批量更新档案状态
func (s *archiveDomainService) BatchUpdateArchiveStatus(ctx context.Context, ids []int64, status int, updateBy int, updatedAt time.Time) (context.Context, []int64, error) {
	if len(ids) == 0 {
		return ctx, nil, errors.New("档案ID列表不能为空")
	}

	// 验证status参数
	if status < 0 || status > 2 {
		return ctx, nil, errors.New("档案状态参数无效，必须为0(正常)、1(删除)或2(其它)")
	}

	var validIds []int64
	var errorMessages []string

	// 在领域服务中协调批量操作的业务逻辑
	for _, id := range ids {
		archive, err := s.repo.FindByID(ctx, id)
		if err != nil {
			errorMessages = append(errorMessages, fmt.Sprintf("档案ID:%d查询失败:%s", id, err.Error()))
			logger.Error("查询档案失败", zap.Error(err), zap.Int64("archiveId", id))
			continue
		}

		if archive == nil {
			errorMessages = append(errorMessages, fmt.Sprintf("档案ID:%d不存在", id))
			logger.Warn("档案不存在，无法更新", zap.Int64("archiveId", id))
			continue
		}

		// 业务规则检查
		if archive.Status == status {
			logger.Warn("档案状态无变化，跳过更新", zap.Int64("archiveId", id), zap.Int("currentStatus", archive.Status), zap.Int("newStatus", status))
			continue
		}

		validIds = append(validIds, id)
	}

	if len(validIds) == 0 {
		errorMessage := fmt.Sprintf("没有可更新状态的档案: %s", strings.Join(errorMessages, "; "))
		logger.Error("批量更新档案状态失败", zap.String("errors", errorMessage))
		return ctx, nil, errors.New(errorMessage)
	}

	// 创建批量更新状态事件
	statusEvent := s.createArchiveBatchStatusUpdatedEvent(validIds, status, updateBy, updatedAt)
	if statusEvent == nil {
		logger.Error("创建批量更新状态事件失败")
		return ctx, nil, errors.New("创建批量更新状态事件失败")
	}

	// 将事件添加到上下文中
	newCtx := domain_event.AddEventToContext(ctx, statusEvent)

	// 如果有部分失败，记录到上下文中供应用服务处理
	if len(errorMessages) > 0 {
		newCtx = context.WithValue(newCtx, "partialErrors", errorMessages)
	}

	return newCtx, validIds, nil
}

// BatchUpdateArchiveExpiration 批量更新档案过期时间
func (s *archiveDomainService) BatchUpdateArchiveExpiration(ctx context.Context, ids []int64, expirationTime time.Time, updateBy int, updatedAt time.Time) (context.Context, []int64, error) {
	if len(ids) == 0 {
		return ctx, nil, errors.New("档案ID列表不能为空")
	}

	var validIds []int64

	// 过滤掉不能更新的档案
	for _, id := range ids {
		archive, err := s.repo.FindByID(ctx, id)
		if err != nil {
			logger.Error("查询档案失败", zap.Error(err), zap.Int64("archiveId", id))
			continue
		}

		if archive == nil {
			logger.Warn("档案不存在，无法更新", zap.Int64("archiveId", id))
			continue
		}

		// 已删除的档案不能更新过期时间
		if archive.Status == 1 {
			logger.Warn("档案已删除，跳过更新过期时间", zap.Int64("archiveId", id))
			continue
		}

		validIds = append(validIds, id)
	}

	if len(validIds) == 0 {
		logger.Error("没有可更新过期时间的档案")
		return ctx, nil, errors.New("没有可更新过期时间的档案")
	}

	// 创建批量更新过期时间事件
	expirationEvent := s.createArchiveBatchExpirationUpdatedEvent(validIds, expirationTime, updateBy, updatedAt)
	if expirationEvent == nil {
		logger.Error("创建批量更新过期时间事件失败")
		return ctx, nil, errors.New("创建批量更新过期时间事件失败")
	}

	// 将事件添加到上下文中
	newCtx := domain_event.AddEventToContext(ctx, expirationEvent)

	return newCtx, validIds, nil
}

// BatchUpdateArchiveStorageDuration 批量更新档案保存期限
func (s *archiveDomainService) BatchUpdateArchiveStorageDuration(ctx context.Context, ids []int64, storageDuration int, updateBy int, updatedAt time.Time) (context.Context, []int64, error) {
	if len(ids) == 0 {
		return ctx, nil, errors.New("档案ID列表不能为空")
	}

	if storageDuration <= 0 {
		return ctx, nil, errors.New("保存期限必须大于0")
	}

	var validIds []int64

	// 过滤掉不能更新的档案
	for _, id := range ids {
		archive, err := s.repo.FindByID(ctx, id)
		if err != nil {
			logger.Error("查询档案失败", zap.Error(err), zap.Int64("archiveId", id))
			continue
		}

		if archive == nil {
			logger.Warn("档案不存在，无法更新", zap.Int64("archiveId", id))
			continue
		}

		// 已删除的档案不能更新保存期限
		if archive.Status == 1 {
			logger.Warn("档案已删除，跳过更新保存期限", zap.Int64("archiveId", id))
			continue
		}

		validIds = append(validIds, id)
	}

	if len(validIds) == 0 {
		logger.Error("没有可更新保存期限的档案")
		return ctx, nil, errors.New("没有可更新保存期限的档案")
	}

	// 创建批量更新保存期限事件
	storageDurationEvent := s.createArchiveBatchStorageDurationUpdatedEvent(validIds, storageDuration, updateBy, updatedAt)
	if storageDurationEvent == nil {
		logger.Error("创建批量更新保存期限事件失败")
		return ctx, nil, errors.New("创建批量更新保存期限事件失败")
	}

	// 将事件添加到上下文中
	newCtx := domain_event.AddEventToContext(ctx, storageDurationEvent)

	return newCtx, validIds, nil
}

// 事件创建方法

func (s *archiveDomainService) createArchiveBatchUpdatedEvent(ids []int64, updates map[string]interface{}) domain_event.Event {
	if len(ids) == 0 {
		logger.Error("档案ID列表为空，无法创建批量更新事件")
		return nil
	}

	payload := domain_event.ArchiveBatchUpdatedPayload{
		ArchiveIDs:    ids,
		UpdatedFields: updates,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 ArchiveBatchUpdatedPayload 转换为 JSON", "error", err)
		return nil
	}

	// 使用第一个ID作为聚合根ID
	aggregateID := ids[0]
	return domain_event.NewDomainEvent(domain_event.EventTypeArchiveBatchUpdated, aggregateID, "Archive", payloadJSON)
}

func (s *archiveDomainService) createArchiveBatchDeletedEvent(ids []int64, updateBy int) domain_event.Event {
	if len(ids) == 0 {
		logger.Error("档案ID列表为空，无法创建批量删除事件")
		return nil
	}

	payload := domain_event.ArchiveBatchDeletedPayload{
		ArchiveIDs: ids,
		UpdateBy:   updateBy,
		DeletedAt:  time.Now(),
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 ArchiveBatchDeletedPayload 转换为 JSON", zap.Error(err))
		return nil
	}

	// 使用第一个ID作为聚合根ID，备注：ai认为有问题: 批量操作时使用第一个ID作为聚合根ID是不合理的，应该使用特殊的批量操作标识符。
	aggregateID := ids[0]
	return domain_event.NewDomainEvent(domain_event.EventTypeArchiveBatchDeleted, aggregateID, "Archive", payloadJSON)
}

func (s *archiveDomainService) createArchiveBatchStatusUpdatedEvent(ids []int64, status int, updateBy int, updatedAt time.Time) domain_event.Event {
	if len(ids) == 0 {
		logger.Error("档案ID列表为空，无法创建批量更新状态事件")
		return nil
	}

	payload := domain_event.ArchiveBatchStatusUpdatedPayload{
		ArchiveIDs: ids,
		Status:     status,
		UpdateBy:   updateBy,
		UpdatedAt:  updatedAt,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 ArchiveBatchStatusUpdatedPayload 转换为 JSON", "error", err)
		return nil
	}

	// 使用第一个ID作为聚合根ID
	aggregateID := ids[0]
	return domain_event.NewDomainEvent(domain_event.EventTypeArchiveBatchStatusUpdated, aggregateID, "Archive", payloadJSON)
}

func (s *archiveDomainService) createArchiveBatchExpirationUpdatedEvent(ids []int64, expirationTime time.Time, updateBy int, updatedAt time.Time) domain_event.Event {
	if len(ids) == 0 {
		logger.Error("档案ID列表为空，无法创建批量更新过期时间事件")
		return nil
	}

	payload := domain_event.ArchiveBatchExpirationUpdatedPayload{
		ArchiveIDs:     ids,
		ExpirationTime: expirationTime,
		UpdateBy:       updateBy,
		UpdatedAt:      updatedAt,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 ArchiveBatchExpirationUpdatedPayload 转换为 JSON", "error", err)
		return nil
	}

	// 使用第一个ID作为聚合根ID
	aggregateID := ids[0]
	return domain_event.NewDomainEvent(domain_event.EventTypeArchiveBatchExpirationUpdated, aggregateID, "Archive", payloadJSON)
}

func (s *archiveDomainService) createArchiveBatchStorageDurationUpdatedEvent(ids []int64, storageDuration int, updateBy int, updatedAt time.Time) domain_event.Event {
	if len(ids) == 0 {
		logger.Error("档案ID列表为空，无法创建批量更新保存期限事件")
		return nil
	}

	payload := domain_event.ArchiveBatchStorageDurationUpdatedPayload{
		ArchiveIDs:      ids,
		StorageDuration: storageDuration,
		UpdateBy:        updateBy,
		UpdatedAt:       updatedAt,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 ArchiveBatchStorageDurationUpdatedPayload 转换为 JSON", "error", err)
		return nil
	}

	// 使用第一个ID作为聚合根ID
	aggregateID := ids[0]
	return domain_event.NewDomainEvent(domain_event.EventTypeArchiveBatchStorageDurationUpdated, aggregateID, "Archive", payloadJSON)
}
