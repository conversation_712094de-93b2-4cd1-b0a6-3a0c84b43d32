package api

import (
	"jxt-evidence-system/evidence-management/query/internal/application/eventhandler"
	"jxt-evidence-system/evidence-management/query/internal/application/queryservice"
	"jxt-evidence-system/evidence-management/query/internal/infrastructure/eventbus"
	grpcclient "jxt-evidence-system/evidence-management/query/internal/infrastructure/grpc_client"
	persistence "jxt-evidence-system/evidence-management/query/internal/infrastructure/persistence/gorm"
	"jxt-evidence-system/evidence-management/query/internal/interface/rest/api"
	"jxt-evidence-system/evidence-management/query/internal/interface/rest/router"
)

// jiyuanjie 注意import目录为正确的六边形架构的目录

func init() {
	// jiyuanjie 这里注册 采用六边形架构开发API的路由，在本目录新建文件放在init方法
	AppRouters = append(AppRouters, router.InitRouter)

	// jiyuanjie 添加依赖注入 api、application service、persistence
	Registrations = append(Registrations, persistence.RegisterDependencies)
	Registrations = append(Registrations, grpcclient.RegisterDependencies)
	Registrations = append(Registrations, queryservice.RegisterDependencies)
	Registrations = append(Registrations, api.RegisterDependencies)

	// jiyuanjie 添加依赖注入 subscriber、eventHandler
	Registrations = append(Registrations, eventhandler.RegisterDependencies)
	Registrations = append(Registrations, eventbus.RegisterDependencies)
}
