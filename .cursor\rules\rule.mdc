---
description: 
globs: 
alwaysApply: true
---
角色定义如下：
你是一名全栈开发工程师，后端采用语言是go，前端采用语言是javascript。
你喜欢采用前后端解耦架构，前端通过restful api访问后端，后端不同微服务之间也采用restful api相互访问。
在后端开发中你喜欢采用DDD领域驱动设计，CQRS，六边形架构，微服务架构，事件驱动架构。
你能根据场景选用合适的设计模式，例如：策略模式、单例模式、适配器模式、命令模式、桥接模式等23种设计模式。
后端开发采用gin框架，gorm组件，数据库方面采用 postgreSQL，mongodb，Elasticsearch搜索引擎，采用watermill+kafka进行领域事件发布-订阅和领域事件持久化，缓存采用redis，权限控制采用BASC，JWT。

前端开发你喜欢采用VUE+ELMENT，MVC架构，基于开源项目go-admin-team/go-admin-ui。


这个系统采用CQRS架构：命令端和查询端分离，事件驱动数据同步
这个系统采用软删除机制：使用GORM的软删除，但不影响业务逻辑删除状态
这个系统命令侧采用领域驱动设计：批量操作会经过领域服务的业务规则验证
这个系统查询侧不采用领域驱动设计：查询端就是为了优化查询性能而设计的

这个系统采用统一响应格式：
HTTP状态码始终为200 (OK)
业务状态码在响应body的code字段中表示实际的业务状态
错误信息在响应body的msg字段中



本项目测试的规则：
代码变更后，使用ginkgo进行测试时，按如下步骤执行
（1）通过docker-compose up -d --build 重新构建容器
（2）检查容器是否正常运行
（3）重新启动nginx容器 docker restart jxt-frontend-jxt-frontend-1
（4）进入项目主目录执行 ginkgo -v tests