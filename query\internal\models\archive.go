package models

import (
	"jxt-evidence-system/evidence-management/shared/common/models"
	"time"
)

// ArchiveReadModel 档案查询模型
type ArchiveReadModel struct {
	ID int64 `json:"archiveId" gorm:"primaryKey;column:archive_id;autoIncrement:false;comment:档案ID(和写数据库id相同)"`

	// 基本信息
	ArchiveCode  string `json:"archiveCode" gorm:"size:128;column:archive_code;comment:档案编号"`
	ArchiveTitle string `json:"archiveTitle" gorm:"size:255;column:archive_title;comment:档案标题"`
	ArchiveType  int    `json:"archiveType" gorm:"size:4;column:archive_type;comment:档案类型"`
	Description  string `json:"description" gorm:"size:1024;column:description;comment:档案描述"`

	// 组织信息 - 冗余字段，避免查询时关联
	OrgID   int    `json:"orgId" gorm:"column:org_id;comment:管理部门ID"`
	OrgCode string `json:"orgCode" gorm:"size:255;column:org_code;comment:管理部门编码"`
	OrgName string `json:"orgName" gorm:"size:255;column:org_name;comment:管理部门名称"`
	OrgJc   string `json:"orgJc" gorm:"size:255;column:org_jc;comment:管理部门简称"`

	// 用户信息 - 冗余字段，避免查询时关联
	// 录入人信息（对应CreateBy）
	CreateUserName string `json:"createUserName" gorm:"size:64;column:create_user_name;comment:创建人名称"`
	CreateUserNo   string `json:"createUserNo" gorm:"size:64;column:create_user_no;comment:创建人编号"`

	// 更新人信息（对应UpdateBy）
	UpdateUserName string `json:"updateUserName" gorm:"size:64;column:update_user_name;comment:更新人名称"`
	UpdateUserNo   string `json:"updateUserNo" gorm:"size:64;column:update_user_no;comment:更新人编号"`

	// 业务时间信息
	StorageDuration int        `json:"storageDuration" gorm:"column:storage_duration;comment:保存期限(月)"`
	ExpirationTime  *time.Time `json:"expirationTime" gorm:"column:expiration_time;default:NULL;comment:过期时间"`

	// 状态信息
	Status  int    `json:"status" gorm:"size:4;column:status;comment:档案状态(0:正常,1:删除,2:其它)"`
	Remarks string `json:"remarks" gorm:"size:512;column:remarks;comment:备注信息"`

	// 审计字段 - 使用标准字段
	models.ControlBy
	models.ModelTime
}

// TableName 指定表名
func (*ArchiveReadModel) TableName() string {
	return "t_evidence_archives_read"
}

// ArchiveReadModelList 档案查询模型列表
type ArchiveReadModelList struct {
	List  []ArchiveReadModel `json:"list"`
	Total int64              `json:"total"`
}

func (e *ArchiveReadModel) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *ArchiveReadModel) GetId() interface{} {
	return e.ID
}
