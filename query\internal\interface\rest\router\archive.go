package router

import (
	"jxt-evidence-system/evidence-management/query/internal/interface/rest/api"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/middleware"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	jwt "github.com/ChenBigdata421/jxt-core/sdk/pkg/jwtauth"
	"github.com/gin-gonic/gin"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerArchiveQueryRouter)
}

func registerArchiveQueryRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {

	// 通过依赖注入创建的api处理器，代替手工创建
	// 解析 ArchiveHandler
	err := di.Invoke(func(archiveHandler *api.ArchiveHandler) {
		if archiveHandler != nil {
			r := v1.Group("/archives").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
			{
				r.GET("", archiveHandler.GetPage)              // 分页查询档案
				r.GET("/:id", archiveHandler.GetByID)          // 根据ID查询档案
				r.GET("/code/:code", archiveHandler.GetByCode) // 根据编码查询档案
			}
		} else {
			logger.Fatal("ArchiveHandler is nil after resolution")
		}
	})

	if err != nil {
		logger.Fatalf("Failed to resolve ArchiveHandler: %v", err)
	}

}
