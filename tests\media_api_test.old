package tests

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"testing"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	commandTesthelpers "jxt-evidence-system/evidence-management/command/testhelpers"
	queryTesthelpers "jxt-evidence-system/evidence-management/query/testhelpers"
	"jxt-evidence-system/evidence-management/tests/testhelpers"
)

func TestMediaApi(t *testing.T) {
	RegisterFailHandler(Fail)
	//RunSpecs(t, "MediaApi Suite")
}

var _ = Describe("MediaApi", func() {
	var suite *testhelpers.TestSuite

	BeforeEach(func() {
		suite = testhelpers.SetupSuite(baseURL)
		suite.SetToken(token)
	})

	Describe("Insert", func() {
		It("成功创建一个新的媒体", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// mediaName赋值，mediaName值唯一，用例检查中要用到它
			mediaName := fmt.Sprintf("新增媒体名称%06d", randNum)
			importTime := time.Now()

			// 创建一个媒体记录
			uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
				WithMediaName(mediaName).
				WithMediaCate(0).     // 设为0，表示新增媒体，但后端服务报错
				WithPoliceNo("0001"). //设为admin的警号
				WithRecorderNo("REC001").
				WithAuthKey("md5hashhere").
				WithRequestIdentity("REQ001").
				WithImportantLevel(1).
				WithMediaSuffix("jpg").
				WithPoliceName("张三").
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithVideoClarity(1).
				WithVideoDuration(3600000).
				WithFileSize(1024).
				WithOrgID(1).
				WithOrgName("公安局").
				WithStorageType(0).
				WithSiteID(1).
				WithStorageID(1).
				WithSiteClientID(1).
				WithTrialID(1).
				WithImportTime(&importTime).
				WithComments("测试媒体").
				Build()

			// 发送前打印请求体
			jsonBody, _ := json.Marshal(uploadMediaCommand)
			fmt.Printf("Sending JSON: %s\n", string(jsonBody))

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("创建媒体成功"))

			// 验证命令数据库中是否创建了新记录
			var count int64
			media := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.Model(media).Where("media_name = ?", uploadMediaCommand.MediaName).Count(&count)
			Expect(count).To(Equal(int64(1)))

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 获取创建的媒体ID
			originalMedia := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)

			// 验证查询数据库中也有对应的媒体记录
			originalQueryMedia := queryTesthelpers.NewMediaReadModelBuilder().Build()
			err = dbQuery.Where("media_id = ?", originalMedia.ID).First(&originalQueryMedia).Error
			if err != nil {
				// 如果查询数据库中没有记录，说明媒体创建事件没有正确处理
				Fail(fmt.Sprintf("查询数据库中没有找到媒体ID为%d的记录，说明媒体创建事件没有正确处理: %v", originalMedia.ID, err))
			}

			// 通过API查询验证
			query := map[string]string{
				"mediaName": uploadMediaCommand.MediaName,
				"mediaCate": fmt.Sprintf("%d", uploadMediaCommand.MediaCate),
			}
			resp, err = testhelpers.SendRequestWithAuthAndQuery(suite.BaseURL, "GET", "/api/v1/media", nil, query, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证查询结果
			body, err = testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("查询成功"))

			mediaData := body["data"].(map[string]interface{})
			list := mediaData["list"].([]interface{})
			Expect(len(list)).To(Equal(1))
			mediaItem := list[0].(map[string]interface{})
			Expect(mediaItem["mediaName"]).To(Equal(uploadMediaCommand.MediaName))
			Expect(mediaItem["mediaCate"]).To(Equal(float64(*uploadMediaCommand.MediaCate)))
			Expect(mediaItem["importantLevel"]).To(Equal(float64(uploadMediaCommand.ImportantLevel)))
			Expect(mediaItem["mediaSuffix"]).To(Equal(uploadMediaCommand.MediaSuffix))
			Expect(mediaItem["comments"]).To(Equal(uploadMediaCommand.Comments))

			Expect(mediaItem["policeNo"]).To(Equal("0001"))     // 验证警号是否为0001
			Expect(mediaItem["policeName"]).To(Equal("admin"))  // 验证警员姓名是否为admin
			Expect(mediaItem["recorderNo"]).To(Equal("REC001")) // 验证执法记录仪号是否为REC001
		})

		It("当尝试插入一个已存在的媒体时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)
			importTime := time.Now()

			// 首先直接在数据库中创建一个媒体记录
			existingMedia := commandTesthelpers.NewMediaBuilder().
				WithMediaName(fmt.Sprintf("已存在的媒体%06d", randNum)).
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithImportTime(&importTime).
				Build()
			dbCommand.Create(&existingMedia)

			// 尝试创建一个与已存在媒体名称相同的新媒体
			uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
				WithMediaName(fmt.Sprintf("已存在的媒体%06d", randNum)).
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithImportTime(&importTime).
				Build()

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证响应
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusInternalServerError)))
			Expect(body["msg"]).To(And(
				ContainSubstring("创建媒体失败"),
				ContainSubstring(fmt.Sprintf("媒体[%s]已经存在", existingMedia.MediaName)),
			))
		})
	})

	Describe("Update", func() {
		It("成功更新一个已存在的媒体", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			mediaName := fmt.Sprintf("已存在媒体名称%06d", randNum)
			importTime := time.Now()
			// 创建一个新的媒体记录
			uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
				WithMediaName(mediaName).
				WithMediaCate(1).
				WithPoliceNo("0001").
				WithRecorderNo("REC001").
				WithAuthKey("md5hashhere").
				WithRequestIdentity("REQ001").
				WithImportantLevel(1).
				WithMediaSuffix("WAV").
				WithPoliceName("张三").
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithVideoClarity(1).
				WithVideoDuration(3600000).
				WithFileSize(1024).
				WithOrgID(1).
				WithOrgName("公安局").
				WithStorageType(0).
				WithSiteID(1).
				WithStorageID(1).
				WithSiteClientID(1).
				WithTrialID(1).
				WithImportTime(&importTime).
				WithComments("测试媒体").
				Build()

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 获取创建的媒体ID
			originalMedia := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)

			// 创建更新命令
			updateMediaCommand := commandTesthelpers.NewUpdateMediaCommandBuilder().
				WithImportantLevel(1).
				WithComments("更新后的测试媒体").
				Build()

				// 发送前打印请求体
			jsonBody, _ := json.Marshal(updateMediaCommand)
			fmt.Printf("Sending JSON: %s\n", string(jsonBody))

			// 发送更新请求
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "PUT", fmt.Sprintf("/api/v1/media/%d", originalMedia.ID), updateMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("Media updated successfully"))

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证数据库更新
			updatedMedia := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.First(updatedMedia, originalMedia.ID)
			Expect(updatedMedia.MediaName).To(Equal(mediaName))
			Expect(updatedMedia.Comments).To(Equal(*updateMediaCommand.Comments))

			// 验证查询数据库更新
			updatedMediaRead := queryTesthelpers.NewMediaReadModelBuilder().Build()
			dbQuery.Where("media_name = ?", mediaName).First(&updatedMediaRead)
			Expect(updatedMediaRead.MediaName).To(Equal(mediaName))
			Expect(updatedMediaRead.Comments).To(Equal(*updateMediaCommand.Comments))

			// 通过API验证更新
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", originalMedia.ID), nil, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证查询结果
			body, err = testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			data := body["data"].(map[string]interface{})
			Expect(data["mediaName"]).To(Equal(mediaName))
			Expect(data["comments"]).To(Equal(*updateMediaCommand.Comments))
		})

		It("当尝试更新不存在的媒体时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			nonExistentID := int64(99999 + randNum)
			updateMediaCommand := commandTesthelpers.NewUpdateMediaCommandBuilder().
				WithID(nonExistentID).
				WithComments("更新不存在的Media").
				Build()

			// 发送更新请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "PUT", fmt.Sprintf("/api/v1/media/%d", nonExistentID), updateMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证响应
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusInternalServerError)))
			Expect(body["msg"]).To(ContainSubstring("更新媒体失败"))
		})
	})

	Describe("BatchUpdateMedia", func() {
		It("成功批量更新媒体信息", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建3个待更新的媒体记录
			ids := make([]int64, 3)

			for i := 0; i < 3; i++ {
				mediaName := fmt.Sprintf("批量更新测试媒体_%06d", randNum+i)
				importTime := time.Now()
				// 创建一个新的媒体记录
				uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
					WithMediaName(mediaName).
					WithMediaCate(2).
					WithPoliceNo("0001").
					WithRecorderNo("REC001").
					WithAuthKey("md5hashhere").
					WithRequestIdentity("REQ001").
					WithImportantLevel(1).
					WithMediaSuffix("mp4").
					WithPoliceName("张三").
					WithShotTimeStart(time.Now()).
					WithShotTime(time.Now().Add(time.Hour)).
					WithVideoClarity(1).
					WithVideoDuration(3600000).
					WithFileSize(1024).
					WithOrgID(1).
					WithOrgName("公安局").
					WithStorageType(0).
					WithSiteID(1).
					WithStorageID(1).
					WithSiteClientID(1).
					WithTrialID(1).
					WithImportTime(&importTime).
					WithComments(fmt.Sprintf("原始测试媒体_%d", i+1)).
					Build()

				// 发送创建请求
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 断言响应成功
				testhelpers.AssertResponseSuccess(resp)

				// 等待领域事件处理
				time.Sleep(200 * time.Millisecond)

				// 获取创建的媒体ID
				originalMedia := commandTesthelpers.NewMediaBuilder().Build()
				dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)
				ids[i] = originalMedia.ID
			}

			// 验证查询数据库中是否创建了3条新媒体
			var count int64
			mediaRead := queryTesthelpers.NewMediaReadModelBuilder().Build()
			dbQuery.Model(mediaRead).Where("media_id IN ?", ids).Count(&count)
			Expect(count).To(Equal(int64(3)))

			// 创建批量更新媒体命令
			newImportantLevel := 3
			newComments := "批量更新后的标注内容"
			newExpiryTime := time.Now().AddDate(1, 0, 0) // 一年后过期

			batchUpdateCommand := commandTesthelpers.NewBatchUpdateMediaCommandBuilder().
				WithIDs(ids).
				WithImportantLevel(newImportantLevel).
				WithComments(newComments).
				WithExpiryTime(newExpiryTime).
				Build()

			// 发送前打印请求体
			jsonBody, _ := json.Marshal(batchUpdateCommand)
			fmt.Printf("Sending BatchUpdateMedia JSON: %s\n", string(jsonBody))

			// 发送批量更新媒体请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "PUT", "/api/v1/media/batch", batchUpdateCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("批量更新媒体成功"))

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证命令数据库中的媒体信息已更新
			for _, id := range ids {
				updatedMediaFromCommandDB := commandTesthelpers.NewMediaBuilder().Build()
				dbCommand.First(updatedMediaFromCommandDB, id)
				Expect(updatedMediaFromCommandDB.ImportantLevel).To(Equal(newImportantLevel))
				Expect(updatedMediaFromCommandDB.Comments).To(Equal(newComments))
				// 验证过期时间（允许1秒误差）
				Expect(updatedMediaFromCommandDB.ExpiryTime.Unix()).To(BeNumerically("~", newExpiryTime.Unix(), 1))
			}

			// 验证查询数据库中的媒体信息已更新
			for _, id := range ids {
				updatedMediaFromQueryDB := queryTesthelpers.NewMediaReadModelBuilder().Build()
				dbQuery.First(updatedMediaFromQueryDB, id)
				Expect(updatedMediaFromQueryDB.ImportantLevel).To(Equal(newImportantLevel))
				Expect(updatedMediaFromQueryDB.Comments).To(Equal(newComments))
				// 验证过期时间（允许1秒误差）
				Expect(updatedMediaFromQueryDB.ExpiryTime.Unix()).To(BeNumerically("~", newExpiryTime.Unix(), 1))
			}

			// 通过API验证第一个媒体的更新
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", ids[0]), nil, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证查询结果
			body, err = testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			data := body["data"].(map[string]interface{})
			Expect(data["importantLevel"]).To(Equal(float64(newImportantLevel)))
			Expect(data["comments"]).To(Equal(newComments))
		})

		It("当尝试批量更新不存在媒体时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建批量更新媒体命令，使用不存在的ID
			newImportantLevel := 2
			newComments := "更新不存在的媒体"

			batchUpdateCommand := commandTesthelpers.NewBatchUpdateMediaCommandBuilder().
				WithIDs([]int64{int64(1000000 + randNum), int64(2000000 + randNum), int64(3000000 + randNum)}).
				WithImportantLevel(newImportantLevel).
				WithComments(newComments).
				Build()

			// 发送批量更新媒体请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "PUT", "/api/v1/media/batch", batchUpdateCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证响应
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusInternalServerError)))
			Expect(body["msg"]).To(And(
				ContainSubstring("批量更新媒体失败"),
				ContainSubstring("没有可更新的媒体"),
			))
		})

		It("成功为单个媒体更新信息", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			mediaName := fmt.Sprintf("单个媒体更新测试_%06d", randNum)
			importTime := time.Now()
			// 创建一个新的媒体记录
			uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
				WithMediaName(mediaName).
				WithMediaCate(1). // 音频类型
				WithPoliceNo("0001").
				WithRecorderNo("REC001").
				WithAuthKey("md5hashhere").
				WithRequestIdentity("REQ001").
				WithImportantLevel(1).
				WithMediaSuffix("wav").
				WithPoliceName("李四").
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithVideoClarity(0).
				WithVideoDuration(1800000). // 30分钟
				WithFileSize(2048).
				WithOrgID(2).
				WithOrgName("交警队").
				WithStorageType(1).
				WithSiteID(2).
				WithStorageID(2).
				WithSiteClientID(2).
				WithTrialID(2).
				WithImportTime(&importTime).
				WithComments("单个媒体更新测试").
				Build()

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 获取创建的媒体ID
			originalMedia := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)

			// 创建批量更新媒体命令（单个媒体）
			newImportantLevel := 4
			newComments := "单个媒体更新后的标注"
			newExpiryTime := time.Now().AddDate(0, 6, 0) // 6个月后过期

			batchUpdateCommand := commandTesthelpers.NewBatchUpdateMediaCommandBuilder().
				WithIDs([]int64{originalMedia.ID}).
				WithImportantLevel(newImportantLevel).
				WithComments(newComments).
				WithExpiryTime(newExpiryTime).
				Build()

			// 发送批量更新媒体请求
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "PUT", "/api/v1/media/batch", batchUpdateCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("批量更新媒体成功"))

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证命令数据库中的媒体信息已更新
			updatedMediaFromCommandDB := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.First(updatedMediaFromCommandDB, originalMedia.ID)
			Expect(updatedMediaFromCommandDB.ImportantLevel).To(Equal(newImportantLevel))
			Expect(updatedMediaFromCommandDB.Comments).To(Equal(newComments))
			Expect(updatedMediaFromCommandDB.ExpiryTime.Unix()).To(BeNumerically("~", newExpiryTime.Unix(), 1))

			// 验证查询数据库中的媒体信息已更新
			updatedMediaFromQueryDB := queryTesthelpers.NewMediaReadModelBuilder().Build()
			dbQuery.First(updatedMediaFromQueryDB, originalMedia.ID)
			Expect(updatedMediaFromQueryDB.ImportantLevel).To(Equal(newImportantLevel))
			Expect(updatedMediaFromQueryDB.Comments).To(Equal(newComments))
			Expect(updatedMediaFromQueryDB.ExpiryTime.Unix()).To(BeNumerically("~", newExpiryTime.Unix(), 1))

			// 通过API验证更新
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", originalMedia.ID), nil, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证查询结果
			body, err = testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			data := body["data"].(map[string]interface{})
			Expect(data["importantLevel"]).To(Equal(float64(newImportantLevel)))
			Expect(data["comments"]).To(Equal(newComments))
			Expect(data["mediaName"]).To(Equal(mediaName))
		})

		It("成功部分更新媒体信息（仅更新重要级别）", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			mediaName := fmt.Sprintf("部分更新测试媒体_%06d", randNum)
			importTime := time.Now()
			originalComments := "原始标注内容"

			// 创建一个新的媒体记录
			uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
				WithMediaName(mediaName).
				WithMediaCate(0). // 图片类型
				WithPoliceNo("0001").
				WithRecorderNo("REC001").
				WithAuthKey("md5hashhere").
				WithRequestIdentity("REQ001").
				WithImportantLevel(1).
				WithMediaSuffix("jpg").
				WithPoliceName("王五").
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithVideoClarity(2).
				WithVideoDuration(0).
				WithFileSize(512).
				WithOrgID(1).
				WithOrgName("派出所").
				WithStorageType(0).
				WithSiteID(1).
				WithStorageID(1).
				WithSiteClientID(1).
				WithTrialID(1).
				WithImportTime(&importTime).
				WithComments(originalComments).
				Build()

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 获取创建的媒体ID
			originalMedia := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)

			// 创建批量更新媒体命令（仅更新重要级别）
			newImportantLevel := 5

			batchUpdateCommand := commandTesthelpers.NewBatchUpdateMediaCommandBuilder().
				WithIDs([]int64{originalMedia.ID}).
				WithImportantLevel(newImportantLevel).
				// 不设置 Comments 和 ExpiryTime，它们应该保持原值
				Build()

			// 发送批量更新媒体请求
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "PUT", "/api/v1/media/batch", batchUpdateCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("批量更新媒体成功"))

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证命令数据库中的媒体信息
			updatedMediaFromCommandDB := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.First(updatedMediaFromCommandDB, originalMedia.ID)
			Expect(updatedMediaFromCommandDB.ImportantLevel).To(Equal(newImportantLevel))
			Expect(updatedMediaFromCommandDB.Comments).To(Equal(originalComments))           // 应该保持原值
			Expect(updatedMediaFromCommandDB.ExpiryTime).To(Equal(originalMedia.ExpiryTime)) // 应该保持原值

			// 验证查询数据库中的媒体信息
			updatedMediaFromQueryDB := queryTesthelpers.NewMediaReadModelBuilder().Build()
			dbQuery.First(updatedMediaFromQueryDB, originalMedia.ID)
			Expect(updatedMediaFromQueryDB.ImportantLevel).To(Equal(newImportantLevel))
			Expect(updatedMediaFromQueryDB.Comments).To(Equal(originalComments)) // 应该保持原值

			// 通过API验证更新
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", originalMedia.ID), nil, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证查询结果
			body, err = testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			data := body["data"].(map[string]interface{})
			Expect(data["importantLevel"]).To(Equal(float64(newImportantLevel)))
			Expect(data["comments"]).To(Equal(originalComments)) // 应该保持原值
			Expect(data["mediaName"]).To(Equal(mediaName))
		})

		It("当传入空的ID列表时应该返回错误", func() {
			// 创建批量更新媒体命令，使用空的ID列表
			newImportantLevel := 2
			newComments := "空ID列表测试"

			batchUpdateCommand := commandTesthelpers.NewBatchUpdateMediaCommandBuilder().
				WithIDs([]int64{}). // 空的ID列表
				WithImportantLevel(newImportantLevel).
				WithComments(newComments).
				Build()

			// 发送批量更新媒体请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "PUT", "/api/v1/media/batch", batchUpdateCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证响应
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusBadRequest)))
			Expect(body["msg"]).To(And(
				ContainSubstring("批量更新媒体失败"),
				ContainSubstring("参数验证失败"),
			))
		})
	})

	Describe("MarkNonEnforcementMediaStatus", func() {
		It("成功标注非执法媒体", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			mediaName := fmt.Sprintf("被标注媒体名称%06d", randNum)
			importTime := time.Now()
			// 创建一个新的媒体记录
			uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
				WithMediaName(mediaName).
				WithMediaCate(2).
				WithPoliceNo("0001").
				WithRecorderNo("REC001").
				WithAuthKey("md5hashhere").
				WithRequestIdentity("REQ001").
				WithImportantLevel(1).
				WithMediaSuffix("mp4").
				WithPoliceName("张三").
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithVideoClarity(1).
				WithVideoDuration(3600000).
				WithFileSize(1024).
				WithOrgID(1).
				WithOrgName("公安局").
				WithStorageType(0).
				WithSiteID(1).
				WithStorageID(1).
				WithSiteClientID(1).
				WithTrialID(1).
				WithImportTime(&importTime).
				WithComments("测试媒体").
				Build()

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 获取创建的媒体ID
			originalMedia := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)

			// 验证查询数据库中也有对应的媒体记录
			originalQueryMedia := queryTesthelpers.NewMediaReadModelBuilder().Build()
			err = dbQuery.Where("media_id = ?", originalMedia.ID).First(&originalQueryMedia).Error
			if err != nil {
				// 如果查询数据库中没有记录，说明媒体创建事件没有正确处理
				Fail(fmt.Sprintf("查询数据库中没有找到媒体ID为%d的记录，说明媒体创建事件没有正确处理: %v", originalMedia.ID, err))
			}

			// 创建标记命令
			markCommand := commandTesthelpers.NewBatchUpdateNonEnforcementStatusCommandBuilder().
				WithIDs([]int64{originalMedia.ID}).
				WithIsNonEnforcementMedia(1).
				Build()

			// 发送标记请求
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/mark-no-enforcement-media-status", markCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("标注非执法视频成功"))

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证数据库更新
			updatedMediaFromCommandDB := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.First(updatedMediaFromCommandDB, originalMedia.ID)
			Expect(updatedMediaFromCommandDB.IsNonEnforcementMedia).To(Equal(markCommand.IsNonEnforcementMedia))

			// 验证查询数据库更新
			updatedMediaFromQueryDB := queryTesthelpers.NewMediaReadModelBuilder().Build()
			dbQuery.First(updatedMediaFromQueryDB, originalMedia.ID)
			Expect(updatedMediaFromQueryDB.IsNonEnforcementMedia).To(Equal(markCommand.IsNonEnforcementMedia))

			// 通过API验证更新
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", originalMedia.ID), nil, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证查询结果
			body, err = testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			data := body["data"].(map[string]interface{})
			Expect(data["isNonEnforcementMedia"]).To(Equal(float64(markCommand.IsNonEnforcementMedia)))
		})

		It("当尝试标记不存在的媒体为非执法媒体时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			nonExistentID := int64(99999 + randNum)
			markCommand := commandTesthelpers.NewBatchUpdateNonEnforcementStatusCommandBuilder().
				WithIDs([]int64{nonExistentID}).
				WithIsNonEnforcementMedia(0).
				Build()

			// 发送标记请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/mark-no-enforcement-media-status", markCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证响应
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusInternalServerError)))
			Expect(body["msg"]).To(And(
				ContainSubstring("标注非执法视频失败"),
				ContainSubstring("没有可设置非执法媒体状态的媒体"),
			))
		})
	})

	Describe("BatchUpdateEnforceType", func() {
		It("成功批量更新执法类型", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建3个待更新执法类型的媒体记录
			ids := make([]int64, 3)

			for i := 0; i < 3; i++ {
				mediaName := fmt.Sprintf("执法类型测试媒体_%06d", randNum+i)
				importTime := time.Now()
				// 创建一个新的媒体记录
				uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
					WithMediaName(mediaName).
					WithMediaCate(2).
					WithPoliceNo("0001").
					WithRecorderNo("REC001").
					WithAuthKey("md5hashhere").
					WithRequestIdentity("REQ001").
					WithImportantLevel(1).
					WithMediaSuffix("mp4").
					WithPoliceName("张三").
					WithShotTimeStart(time.Now()).
					WithShotTime(time.Now().Add(time.Hour)).
					WithVideoClarity(1).
					WithVideoDuration(3600000).
					WithFileSize(1024).
					WithOrgID(1).
					WithOrgName("公安局").
					WithStorageType(0).
					WithSiteID(1).
					WithStorageID(1).
					WithSiteClientID(1).
					WithTrialID(1).
					WithImportTime(&importTime).
					WithComments(fmt.Sprintf("执法类型测试媒体_%d", i+1)).
					Build()

				// 发送创建请求
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 断言响应成功
				testhelpers.AssertResponseSuccess(resp)

				// 等待领域事件处理
				time.Sleep(200 * time.Millisecond)

				// 获取创建的媒体ID
				originalMedia := commandTesthelpers.NewMediaBuilder().Build()
				dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)
				ids[i] = originalMedia.ID
			}

			// 验证查询数据库中是否创建了3条新媒体
			var count int64
			mediaRead := queryTesthelpers.NewMediaReadModelBuilder().Build()
			dbQuery.Model(mediaRead).Where("media_id IN ?", ids).Count(&count)
			Expect(count).To(Equal(int64(3)))

			// 创建批量更新执法类型命令
			updateEnforceTypeCommand := commandTesthelpers.NewBatchUpdateEnforceTypeCommandBuilder().
				WithIDs(ids).
				WithEnforceType(2). // 设置执法类型为2
				Build()

			// 发送前打印请求体
			jsonBody, _ := json.Marshal(updateEnforceTypeCommand)
			fmt.Printf("Sending BatchUpdateEnforceType JSON: %s\n", string(jsonBody))

			// 发送批量更新执法类型请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-enforce-type", updateEnforceTypeCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("批量更新执法类型成功"))

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证命令数据库中的执法类型已更新
			for _, id := range ids {
				updatedMediaFromCommandDB := commandTesthelpers.NewMediaBuilder().Build()
				dbCommand.First(updatedMediaFromCommandDB, id)
				Expect(updatedMediaFromCommandDB.EnforceType).To(Equal(updateEnforceTypeCommand.EnforceType))
			}

			// 验证查询数据库中的执法类型已更新
			for _, id := range ids {
				updatedMediaFromQueryDB := queryTesthelpers.NewMediaReadModelBuilder().Build()
				dbQuery.First(updatedMediaFromQueryDB, id)
				Expect(updatedMediaFromQueryDB.EnforceType).To(Equal(updateEnforceTypeCommand.EnforceType))
			}

			// 通过API验证第一个媒体的更新
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", ids[0]), nil, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证查询结果
			body, err = testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			data := body["data"].(map[string]interface{})
			Expect(data["enforceType"]).To(Equal(float64(updateEnforceTypeCommand.EnforceType)))
		})

		It("当尝试批量更新不存在媒体的执法类型时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建批量更新执法类型命令，使用不存在的ID
			updateEnforceTypeCommand := commandTesthelpers.NewBatchUpdateEnforceTypeCommandBuilder().
				WithIDs([]int64{int64(1000000 + randNum), int64(2000000 + randNum), int64(3000000 + randNum)}).
				WithEnforceType(3). // 设置执法类型为3
				Build()

			// 发送批量更新执法类型请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-enforce-type", updateEnforceTypeCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证响应
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusInternalServerError)))
			Expect(body["msg"]).To(And(
				ContainSubstring("批量更新执法类型失败"),
				ContainSubstring("没有可更新执法类型的媒体"),
			))
		})

		It("成功为单个媒体更新执法类型", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			mediaName := fmt.Sprintf("单个执法类型更新媒体_%06d", randNum)
			importTime := time.Now()
			// 创建一个新的媒体记录
			uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
				WithMediaName(mediaName).
				WithMediaCate(1). // 音频类型
				WithPoliceNo("0001").
				WithRecorderNo("REC001").
				WithAuthKey("md5hashhere").
				WithRequestIdentity("REQ001").
				WithImportantLevel(2).
				WithMediaSuffix("wav").
				WithPoliceName("李四").
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithVideoClarity(0).
				WithVideoDuration(1800000). // 30分钟
				WithFileSize(2048).
				WithOrgID(2).
				WithOrgName("交警队").
				WithStorageType(1).
				WithSiteID(2).
				WithStorageID(2).
				WithSiteClientID(2).
				WithTrialID(2).
				WithImportTime(&importTime).
				WithComments("单个执法类型更新测试").
				Build()

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 获取创建的媒体ID
			originalMedia := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)

			// 创建批量更新执法类型命令（单个媒体）
			updateEnforceTypeCommand := commandTesthelpers.NewBatchUpdateEnforceTypeCommandBuilder().
				WithIDs([]int64{originalMedia.ID}).
				WithEnforceType(5). // 设置执法类型为5
				Build()

			// 发送批量更新执法类型请求
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-enforce-type", updateEnforceTypeCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("批量更新执法类型成功"))

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证命令数据库中的执法类型已更新
			updatedMediaFromCommandDB := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.First(updatedMediaFromCommandDB, originalMedia.ID)
			Expect(updatedMediaFromCommandDB.EnforceType).To(Equal(updateEnforceTypeCommand.EnforceType))

			// 验证查询数据库中的执法类型已更新
			updatedMediaFromQueryDB := queryTesthelpers.NewMediaReadModelBuilder().Build()
			dbQuery.First(updatedMediaFromQueryDB, originalMedia.ID)
			Expect(updatedMediaFromQueryDB.EnforceType).To(Equal(updateEnforceTypeCommand.EnforceType))

			// 通过API验证更新
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", originalMedia.ID), nil, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证查询结果
			body, err = testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			data := body["data"].(map[string]interface{})
			Expect(data["enforceType"]).To(Equal(float64(updateEnforceTypeCommand.EnforceType)))
			Expect(data["mediaName"]).To(Equal(mediaName))
		})

		It("当传入无效的执法类型值时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			mediaName := fmt.Sprintf("无效执法类型测试媒体_%06d", randNum)
			importTime := time.Now()
			// 创建一个新的媒体记录
			uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
				WithMediaName(mediaName).
				WithMediaCate(0).
				WithPoliceNo("0001").
				WithRecorderNo("REC001").
				WithAuthKey("md5hashhere").
				WithRequestIdentity("REQ001").
				WithImportantLevel(1).
				WithMediaSuffix("jpg").
				WithPoliceName("王五").
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithVideoClarity(2).
				WithVideoDuration(0).
				WithFileSize(512).
				WithOrgID(1).
				WithOrgName("派出所").
				WithStorageType(0).
				WithSiteID(1).
				WithStorageID(1).
				WithSiteClientID(1).
				WithTrialID(1).
				WithImportTime(&importTime).
				WithComments("无效执法类型测试").
				Build()

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 获取创建的媒体ID
			originalMedia := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)

			// 创建包含无效执法类型的命令（负数）
			invalidCommand := map[string]interface{}{
				"ids":         []int64{originalMedia.ID},
				"enforceType": -1, // 无效的执法类型值（负数）
			}

			// 发送批量更新执法类型请求
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-enforce-type", invalidCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证响应
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusBadRequest)))
			Expect(body["msg"]).To(And(
				ContainSubstring("批量更新执法类型失败"),
				ContainSubstring("参数验证失败"),
			))
		})
	})

	Describe("BatchUpdateIsLocked", func() {
		It("成功批量更新锁定状态", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建3个待更新锁定状态的媒体记录
			ids := make([]int64, 3)

			for i := 0; i < 3; i++ {
				mediaName := fmt.Sprintf("锁定状态测试媒体_%06d", randNum+i)
				importTime := time.Now()
				// 创建一个新的媒体记录
				uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
					WithMediaName(mediaName).
					WithMediaCate(2).
					WithPoliceNo("0001").
					WithRecorderNo("REC001").
					WithAuthKey("md5hashhere").
					WithRequestIdentity("REQ001").
					WithImportantLevel(1).
					WithMediaSuffix("mp4").
					WithPoliceName("张三").
					WithShotTimeStart(time.Now()).
					WithShotTime(time.Now().Add(time.Hour)).
					WithVideoClarity(1).
					WithVideoDuration(3600000).
					WithFileSize(1024).
					WithOrgID(1).
					WithOrgName("公安局").
					WithStorageType(0).
					WithSiteID(1).
					WithStorageID(1).
					WithSiteClientID(1).
					WithTrialID(1).
					WithImportTime(&importTime).
					WithComments(fmt.Sprintf("锁定状态测试媒体_%d", i+1)).
					Build()

				// 发送创建请求
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 断言响应成功
				testhelpers.AssertResponseSuccess(resp)

				// 等待领域事件处理
				time.Sleep(200 * time.Millisecond)

				// 获取创建的媒体ID
				originalMedia := commandTesthelpers.NewMediaBuilder().Build()
				dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)
				ids[i] = originalMedia.ID
			}

			// 验证查询数据库中是否创建了3条新媒体
			var count int64
			mediaRead := queryTesthelpers.NewMediaReadModelBuilder().Build()
			dbQuery.Model(mediaRead).Where("media_id IN ?", ids).Count(&count)
			Expect(count).To(Equal(int64(3)))

			// 创建批量更新锁定状态命令
			updateIsLockedCommand := commandTesthelpers.NewBatchUpdateIsLockedCommandBuilder().
				WithIDs(ids).
				WithIsLocked(1). // 设置为锁定状态
				Build()

			// 发送前打印请求体
			jsonBody, _ := json.Marshal(updateIsLockedCommand)
			fmt.Printf("Sending BatchUpdateIsLocked JSON: %s\n", string(jsonBody))

			// 发送批量更新锁定状态请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-is-locked", updateIsLockedCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("批量更新锁定状态成功"))

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证命令数据库中的锁定状态已更新
			for _, id := range ids {
				updatedMediaFromCommandDB := commandTesthelpers.NewMediaBuilder().Build()
				dbCommand.First(updatedMediaFromCommandDB, id)
				Expect(updatedMediaFromCommandDB.IsLocked).To(Equal(updateIsLockedCommand.IsLocked))
			}

			// 验证查询数据库中的锁定状态已更新
			for _, id := range ids {
				updatedMediaFromQueryDB := queryTesthelpers.NewMediaReadModelBuilder().Build()
				dbQuery.First(updatedMediaFromQueryDB, id)
				Expect(updatedMediaFromQueryDB.IsLocked).To(Equal(updateIsLockedCommand.IsLocked))
			}

			// 通过API验证第一个媒体的更新
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", ids[0]), nil, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证查询结果
			body, err = testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			data := body["data"].(map[string]interface{})
			Expect(data["isLocked"]).To(Equal(float64(updateIsLockedCommand.IsLocked)))
		})

		It("当尝试批量更新不存在媒体的锁定状态时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建批量更新锁定状态命令，使用不存在的ID
			updateIsLockedCommand := commandTesthelpers.NewBatchUpdateIsLockedCommandBuilder().
				WithIDs([]int64{int64(1000000 + randNum), int64(2000000 + randNum), int64(3000000 + randNum)}).
				WithIsLocked(1). // 设置为锁定状态
				Build()

			// 发送批量更新锁定状态请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-is-locked", updateIsLockedCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证响应
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusInternalServerError)))
			Expect(body["msg"]).To(And(
				ContainSubstring("批量更新锁定状态失败"),
				ContainSubstring("没有可更新锁定状态的媒体"),
			))
		})

		It("成功为单个媒体更新锁定状态", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			mediaName := fmt.Sprintf("单个锁定状态更新媒体_%06d", randNum)
			importTime := time.Now()
			// 创建一个新的媒体记录
			uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
				WithMediaName(mediaName).
				WithMediaCate(1). // 音频类型
				WithPoliceNo("0001").
				WithRecorderNo("REC001").
				WithAuthKey("md5hashhere").
				WithRequestIdentity("REQ001").
				WithImportantLevel(2).
				WithMediaSuffix("wav").
				WithPoliceName("李四").
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithVideoClarity(0).
				WithVideoDuration(1800000). // 30分钟
				WithFileSize(2048).
				WithOrgID(2).
				WithOrgName("交警队").
				WithStorageType(1).
				WithSiteID(2).
				WithStorageID(2).
				WithSiteClientID(2).
				WithTrialID(2).
				WithImportTime(&importTime).
				WithComments("单个锁定状态更新测试").
				Build()

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 获取创建的媒体ID
			originalMedia := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)

			// 创建批量更新锁定状态命令（单个媒体）
			updateIsLockedCommand := commandTesthelpers.NewBatchUpdateIsLockedCommandBuilder().
				WithIDs([]int64{originalMedia.ID}).
				WithIsLocked(1). // 设置为锁定状态
				Build()

			// 发送批量更新锁定状态请求
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-is-locked", updateIsLockedCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("批量更新锁定状态成功"))

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证命令数据库中的锁定状态已更新
			updatedMediaFromCommandDB := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.First(updatedMediaFromCommandDB, originalMedia.ID)
			Expect(updatedMediaFromCommandDB.IsLocked).To(Equal(updateIsLockedCommand.IsLocked))

			// 验证查询数据库中的锁定状态已更新
			updatedMediaFromQueryDB := queryTesthelpers.NewMediaReadModelBuilder().Build()
			dbQuery.First(updatedMediaFromQueryDB, originalMedia.ID)
			Expect(updatedMediaFromQueryDB.IsLocked).To(Equal(updateIsLockedCommand.IsLocked))

			// 通过API验证更新
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", originalMedia.ID), nil, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证查询结果
			body, err = testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			data := body["data"].(map[string]interface{})
			Expect(data["isLocked"]).To(Equal(float64(updateIsLockedCommand.IsLocked)))
			Expect(data["mediaName"]).To(Equal(mediaName))
		})

		It("成功将媒体从锁定状态解锁", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			mediaName := fmt.Sprintf("解锁状态测试媒体_%06d", randNum)
			importTime := time.Now()
			// 创建一个新的媒体记录
			uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
				WithMediaName(mediaName).
				WithMediaCate(0). // 图片类型
				WithPoliceNo("0001").
				WithRecorderNo("REC001").
				WithAuthKey("md5hashhere").
				WithRequestIdentity("REQ001").
				WithImportantLevel(3).
				WithMediaSuffix("jpg").
				WithPoliceName("王五").
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithVideoClarity(2).
				WithVideoDuration(0).
				WithFileSize(512).
				WithOrgID(1).
				WithOrgName("派出所").
				WithStorageType(0).
				WithSiteID(1).
				WithStorageID(1).
				WithSiteClientID(1).
				WithTrialID(1).
				WithImportTime(&importTime).
				WithComments("解锁状态测试").
				Build()

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 获取创建的媒体ID
			originalMedia := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)

			// 首先将媒体设为锁定状态
			lockCommand := commandTesthelpers.NewBatchUpdateIsLockedCommandBuilder().
				WithIDs([]int64{originalMedia.ID}).
				WithIsLocked(1). // 设置为锁定状态
				Build()

			// 发送锁定请求
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-is-locked", lockCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证媒体已锁定
			lockedMedia := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.First(lockedMedia, originalMedia.ID)
			Expect(lockedMedia.IsLocked).To(Equal(1))

			// 创建解锁命令
			unlockCommand := commandTesthelpers.NewBatchUpdateIsLockedCommandBuilder().
				WithIDs([]int64{originalMedia.ID}).
				WithIsLocked(0). // 设置为解锁状态
				Build()

			// 发送解锁请求
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-is-locked", unlockCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("批量更新锁定状态成功"))

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证命令数据库中的媒体已解锁
			unlockedMediaFromCommandDB := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.First(unlockedMediaFromCommandDB, originalMedia.ID)
			Expect(unlockedMediaFromCommandDB.IsLocked).To(Equal(0))

			// 验证查询数据库中的媒体已解锁
			unlockedMediaFromQueryDB := queryTesthelpers.NewMediaReadModelBuilder().Build()
			dbQuery.First(unlockedMediaFromQueryDB, originalMedia.ID)
			Expect(unlockedMediaFromQueryDB.IsLocked).To(Equal(0))

			// 通过API验证解锁
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "GET", fmt.Sprintf("/api/v1/media/%d", originalMedia.ID), nil, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证查询结果
			body, err = testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			data := body["data"].(map[string]interface{})
			Expect(data["isLocked"]).To(Equal(float64(0)))
			Expect(data["mediaName"]).To(Equal(mediaName))
		})

		It("当传入无效的锁定状态值时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			mediaName := fmt.Sprintf("无效锁定状态测试媒体_%06d", randNum)
			importTime := time.Now()
			// 创建一个新的媒体记录
			uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
				WithMediaName(mediaName).
				WithMediaCate(0).
				WithPoliceNo("0001").
				WithRecorderNo("REC001").
				WithAuthKey("md5hashhere").
				WithRequestIdentity("REQ001").
				WithImportantLevel(1).
				WithMediaSuffix("jpg").
				WithPoliceName("赵六").
				WithShotTimeStart(time.Now()).
				WithShotTime(time.Now().Add(time.Hour)).
				WithVideoClarity(2).
				WithVideoDuration(0).
				WithFileSize(512).
				WithOrgID(1).
				WithOrgName("派出所").
				WithStorageType(0).
				WithSiteID(1).
				WithStorageID(1).
				WithSiteClientID(1).
				WithTrialID(1).
				WithImportTime(&importTime).
				WithComments("无效锁定状态测试").
				Build()

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 获取创建的媒体ID
			originalMedia := commandTesthelpers.NewMediaBuilder().Build()
			dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)

			// 创建包含无效锁定状态的命令（非0非1的值）
			invalidCommand := map[string]interface{}{
				"ids":      []int64{originalMedia.ID},
				"isLocked": 2, // 无效的锁定状态值（只能是0或1）
			}

			// 发送批量更新锁定状态请求
			resp, err = testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media/batch/update-is-locked", invalidCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证响应
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusBadRequest)))
			Expect(body["msg"]).To(And(
				ContainSubstring("批量更新锁定状态失败"),
				ContainSubstring("参数验证失败"),
			))
		})
	})

	Describe("Delete", func() {
		It("成功批量删除已存在媒体", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建3个待删除媒体记录
			ids := make([]int64, 3)

			for i := 0; i < 3; i++ {
				mediaName := fmt.Sprintf("待删除媒体_%06d", randNum+i)
				importTime := time.Now()
				// 创建一个新的媒体记录
				uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
					WithMediaName(mediaName).
					WithMediaCate(2).
					WithPoliceNo("0001").
					WithRecorderNo("REC001").
					WithAuthKey("md5hashhere").
					WithRequestIdentity("REQ001").
					WithImportantLevel(1).
					WithMediaSuffix("mp4").
					WithPoliceName("张三").
					WithShotTimeStart(time.Now()).
					WithShotTime(time.Now().Add(time.Hour)).
					WithVideoClarity(1).
					WithVideoDuration(3600000).
					WithFileSize(1024).
					WithOrgID(1).
					WithOrgName("公安局").
					WithStorageType(0).
					WithSiteID(1).
					WithStorageID(1).
					WithSiteClientID(1).
					WithTrialID(1).
					WithImportTime(&importTime).
					WithComments(fmt.Sprintf("测试媒体_%d", i+1)).
					Build()

				// 发送创建请求
				resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, suite.Token)
				Expect(err).NotTo(HaveOccurred())
				defer resp.Body.Close()

				// 断言响应成功
				testhelpers.AssertResponseSuccess(resp)

				// 等待领域事件处理
				time.Sleep(200 * time.Millisecond)

				// 获取创建的媒体ID
				originalMedia := commandTesthelpers.NewMediaBuilder().Build()
				dbCommand.Where("media_name = ?", mediaName).First(&originalMedia)
				ids[i] = originalMedia.ID
			}

			// 验证查询数据库中是否创建了3条新媒体
			var count int64
			mediaRead := queryTesthelpers.NewMediaReadModelBuilder().Build()
			dbQuery.Model(mediaRead).Where("media_id IN ?", ids).Count(&count)
			Expect(count).To(Equal(int64(3)))

			// 创建删除命令
			deleteCommand := commandTesthelpers.NewBatchDeleteMediaCommandBuilder().WithIDs(ids).Build()

			// 发送删除请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "DELETE", "/api/v1/media/batch", deleteCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 断言响应成功
			testhelpers.AssertResponseSuccess(resp)

			// 验证响应内容
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusOK)))
			Expect(body["msg"]).To(Equal("批量删除媒体成功"))

			// 等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证命令数据库中记录已删除
			dbCommand.Model(commandTesthelpers.NewMediaBuilder().Build()).Where("media_id IN ?", ids).Count(&count)
			Expect(count).To(Equal(int64(0)))

			// 验证查询数据库中记录已删除
			mediaRead = queryTesthelpers.NewMediaReadModelBuilder().Build()
			dbQuery.Model(mediaRead).Where("media_id IN ?", deleteCommand.IDs).Count(&count)
			Expect(count).To(Equal(int64(0)))
		})

		It("当尝试批量删除不存在的媒体时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建删除命令，使用不存在的ID
			deleteCommand := commandTesthelpers.NewBatchDeleteMediaCommandBuilder().
				WithIDs([]int64{int64(1000000 + randNum), int64(2000000 + randNum), int64(3000000 + randNum)}).
				Build()

			// 发送删除请求
			resp, err := testhelpers.SendRequestWithAuth(suite.BaseURL, "DELETE", "/api/v1/media/batch", deleteCommand, suite.Token)
			Expect(err).NotTo(HaveOccurred())
			defer resp.Body.Close()

			// 验证响应
			body, err := testhelpers.GetResponseBody(resp)
			Expect(err).NotTo(HaveOccurred())
			Expect(body["code"]).To(Equal(float64(http.StatusInternalServerError)))
			Expect(body["msg"]).To(And(
				ContainSubstring("批量删除媒体失败"),
				ContainSubstring("没有可删除的媒体"),
			))
		})
	})
})
