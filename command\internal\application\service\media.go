package service

import (
	"context"
	"errors"
	"fmt"
	"jxt-evidence-system/evidence-management/command/internal/application/command"
	"jxt-evidence-system/evidence-management/command/internal/application/service/port"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/media"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/media/repository"
	"jxt-evidence-system/evidence-management/command/internal/domain/event/publisher"
	domain_service "jxt-evidence-system/evidence-management/command/internal/domain/service"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/eventbus"
	"jxt-evidence-system/evidence-management/shared/common/global"
	client "jxt-evidence-system/evidence-management/shared/common/grpc/client/port"
	"jxt-evidence-system/evidence-management/shared/common/service"
	domain_event "jxt-evidence-system/evidence-management/shared/domain/event"
	event_repository "jxt-evidence-system/evidence-management/shared/domain/event/repository"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"go.uber.org/zap"
)

func init() {
	registrations = append(registrations, registerMediaServiceDependencies)
}

func registerMediaServiceDependencies() {
	err := di.Provide(func(repo repository.MediaRepository,
		eventPublisher publisher.EventPublisher,
		eventRepo event_repository.DomainEventRepository,
		mediaDomainService domain_service.MediaDomainService,
		userInfoClient client.UserInfoServiceClient,
		lawcameraInfoClient client.LawcameraInfoServiceClient) port.MediaService {
		return &mediaService{
			repo:                repo,
			eventPublisher:      eventPublisher,
			eventRepo:           eventRepo,
			mediaDomainService:  mediaDomainService,
			userInfoClient:      userInfoClient,
			lawcameraInfoClient: lawcameraInfoClient,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide MediaService: %v", err)
	}
}

type mediaService struct {
	service.Service
	repo                repository.MediaRepository
	eventPublisher      publisher.EventPublisher
	eventRepo           event_repository.DomainEventRepository
	mediaDomainService  domain_service.MediaDomainService
	userInfoClient      client.UserInfoServiceClient
	lawcameraInfoClient client.LawcameraInfoServiceClient
}

// createMediaFromCommand 从command创建领域对象
func (e *mediaService) createMediaFromCommand(command *command.UploadMediaCommand) *media.Media {
	media := &media.Media{
		MediaName:             command.MediaName,
		MediaCate:             *command.MediaCate,
		MediaSuffix:           command.MediaSuffix,
		ImportantLevel:        command.ImportantLevel,
		ShotTimeStart:         command.ShotTimeStart,
		ShotTime:              command.ShotTime,
		VideoClarity:          command.VideoClarity,
		VideoDuration:         command.VideoDuration,
		FileSize:              command.FileSize,
		OrgID:                 command.OrgID,
		StorageType:           command.StorageType,
		SiteID:                command.SiteID,
		StorageID:             command.StorageID,
		SiteClientID:          command.SiteClientID,
		TrialID:               command.TrialID,
		Comments:              command.Comments,
		IsNonEnforcementMedia: 0, // 总是初始化为0
	}

	if command.ImportTime != nil {
		media.ImportTime = command.ImportTime
	} else {
		now := time.Now()
		media.ImportTime = &now
	}

	if command.AcquisitionTime != nil {
		media.AcquisitionTime = command.AcquisitionTime
	} else {
		now := time.Now()
		media.AcquisitionTime = &now
	}

	if command.CreateBy != 0 {
		media.CreateBy = command.CreateBy
	}
	if command.UpdateBy != 0 {
		media.UpdateBy = command.UpdateBy
	}

	return media
}

func (e *mediaService) UploadMedia(ctx context.Context, command *command.UploadMediaCommand) error {
	// 检查媒体是否已经存在
	existingMedia, err := e.repo.FindByName(ctx, command.MediaName)
	if err != nil {
		return err
	}
	if existingMedia != nil {
		return fmt.Errorf("上传的媒体[%s]已经存在！", command.MediaName)
	}

	// 创建领域对象
	media := e.createMediaFromCommand(command)

	// 从上下文中获取租户ID
	tenantID := ctx.Value(global.TenantIDKey)
	if tenantID == nil {
		return fmt.Errorf("无法从上下文中获取租户ID")
	}

	// 根据policeNo查询policeID的逻辑
	if command.PoliceNo != "" {

		userInfo, err := e.userInfoClient.GetUserByPoliceNo(ctx, tenantID.(string), command.PoliceNo)
		if err != nil {
			return fmt.Errorf("根据警号[%s]查询用户信息失败: %v", command.PoliceNo, err)
		}
		if userInfo == nil {
			return fmt.Errorf("警号[%s]对应的用户不存在", command.PoliceNo)
		}
		media.PoliceID = int(userInfo.UserId)
	}

	// 根据RecorderNo查询recorderID的逻辑
	if command.RecorderNo != "" {
		recorderID, err := e.lawcameraInfoClient.GetLawcameraByNo(ctx, tenantID.(string), command.RecorderNo)
		if err != nil {
			return err
		}
		media.RecorderID = int(recorderID.Id)
	}

	err = e.repo.Create(ctx, media)
	if err != nil {
		return fmt.Errorf("数据库持久化失败! %s", err)
	}

	media.CreateMediaAndSave() // 使用聚合根方法创建事件，放在数据库写入之后，是因为写入数据库之后id才会生成，不然id为0

	if err := e.publishEvents(ctx, media); err != nil {
		// 打印异常日志，但不返回错误
		e.GetLogger(ctx).Error("发布MediaCreatedEvent失败: %s", zap.Error(err), zap.Int64("MediaId", media.ID))
	}

	return nil
}

func (e *mediaService) UpdateMediaByID(ctx context.Context, command *command.UpdateMediaCommand) error {
	// 检查媒体是否存在
	existingMedia, err := e.repo.FindByID(ctx, command.ID)
	if err != nil {
		return err
	}
	if existingMedia == nil {
		return fmt.Errorf("待更新的媒体[%d]不存在", command.GetId())
	}

	// 这里实现，只更新需要更新的字段，不更新无需更新的字段
	updates := make(map[string]interface{})

	if command.ImportantLevel != nil {
		updates["ImportantLevel"] = *command.ImportantLevel
		existingMedia.ImportantLevel = *command.ImportantLevel
	}
	if command.MediaCate != nil {
		updates["MediaCate"] = *command.MediaCate
		existingMedia.MediaCate = *command.MediaCate
	}
	if command.MediaSuffix != nil {
		updates["MediaSuffix"] = *command.MediaSuffix
		existingMedia.MediaSuffix = *command.MediaSuffix
	}
	if command.ShotTimeStart != nil {
		updates["ShotTimeStart"] = *command.ShotTimeStart
		existingMedia.ShotTimeStart = *command.ShotTimeStart
	}
	if command.ShotTime != nil {
		updates["ShotTime"] = *command.ShotTime
		existingMedia.ShotTime = *command.ShotTime
	}
	if command.VideoClarity != nil {
		updates["VideoClarity"] = *command.VideoClarity
		existingMedia.VideoClarity = *command.VideoClarity
	}
	if command.VideoDuration != nil {
		updates["VideoDuration"] = *command.VideoDuration
		existingMedia.VideoDuration = *command.VideoDuration
	}
	if command.FileSize != nil {
		updates["FileSize"] = *command.FileSize
		existingMedia.FileSize = *command.FileSize
	}
	if command.OrgID != nil {
		updates["OrgID"] = *command.OrgID
		existingMedia.OrgID = *command.OrgID
	}
	if command.StorageType != nil {
		updates["StorageType"] = *command.StorageType
		existingMedia.StorageType = *command.StorageType
	}
	if command.SiteID != nil {
		updates["SiteID"] = *command.SiteID
		existingMedia.SiteID = *command.SiteID
	}
	if command.StorageID != nil {
		updates["StorageID"] = *command.StorageID
		existingMedia.StorageID = *command.StorageID
	}
	if command.SiteClientID != nil {
		updates["SiteClientID"] = *command.SiteClientID
		existingMedia.SiteClientID = *command.SiteClientID
	}
	if command.TrialID != nil {
		updates["TrialID"] = *command.TrialID
		existingMedia.TrialID = *command.TrialID
	}

	if command.Comments != nil {
		updates["Comments"] = *command.Comments
		existingMedia.Comments = *command.Comments
	}
	if command.UpdateBy != 0 {
		updates["UpdateBy"] = command.UpdateBy
		existingMedia.UpdateBy = command.UpdateBy
	}

	err = existingMedia.UpdateMedia(updates) // 使用聚合根方法判断是否可更新，并创建事件
	if err != nil {
		return err
	}

	err = e.repo.UpdateByID(ctx, existingMedia.ID, updates)
	if err != nil {
		return err
	}

	if err := e.publishEvents(ctx, existingMedia); err != nil {
		// 打印异常日志，但不返回错误
		e.GetLogger(ctx).Error("发布MediaUpdatedEvent失败!", zap.Error(err), zap.Int64("MediaId", existingMedia.ID))
	}

	return nil
}

func (e *mediaService) DeleteMediaByID(ctx context.Context, id int64) error {

	// 从数据库中获取完整的Media记录
	existingMedia, err := e.repo.FindByID(ctx, id)
	if err != nil {
		return err
	}

	if existingMedia == nil {
		return fmt.Errorf("待删除的媒体[%d]不存在", id)
	}

	err = existingMedia.DeleteMedia() // 使用聚合根方法判断是否可删除，并创建事件
	if err != nil {
		return err
	}

	// 删除单条记录
	err = e.repo.DeleteByID(ctx, id)
	if err != nil {
		return err
	}

	if err := e.publishEvents(ctx, existingMedia); err != nil {
		// 打印异常日志，但不返回错误
		e.GetLogger(ctx).Error("发布MediaDeletedEvent失败!", zap.Error(err), zap.Int64("MediaId", id))
	}

	return nil
}

// 批量更新媒体通过媒体领域服务创建事件，并执行批量更新操作
// 前端可以同时更新重要级别，标注内容（Comments）、存储期限
func (e *mediaService) BatchUpdateMedia(ctx context.Context, command *command.BatchUpdateMediaCommand) error {

	// 这里实现，只更新需要更新的字段，不更新无需更新的字段
	updates := make(map[string]interface{})

	if command.ImportantLevel != nil {
		updates["ImportantLevel"] = *command.ImportantLevel
	}

	if command.Comments != nil {
		updates["Comments"] = *command.Comments
	}

	if command.ExpiryTime != nil {
		updates["ExpiryTime"] = *command.ExpiryTime
	}
	/*
		if command.MediaCate != nil {
			updates["MediaCate"] = *command.MediaCate
		}
		if command.MediaSuffix != nil {
			updates["MediaSuffix"] = *command.MediaSuffix
		}
		if command.ShotTimeStart != nil {
			updates["ShotTimeStart"] = *command.ShotTimeStart
		}
		if command.ShotTime != nil {
			updates["ShotTime"] = *command.ShotTime
		}
		if command.VideoClarity != nil {
			updates["VideoClarity"] = *command.VideoClarity
		}
		if command.VideoDuration != nil {
			updates["VideoDuration"] = *command.VideoDuration
		}
		if command.FileSize != nil {
			updates["FileSize"] = *command.FileSize
		}
		if command.OrgID != nil {
			updates["OrgID"] = *command.OrgID
		}
		if command.StorageType != nil {
			updates["StorageType"] = *command.StorageType
		}
		if command.SiteID != nil {
			updates["SiteID"] = *command.SiteID
		}
		if command.StorageID != nil {
			updates["StorageID"] = *command.StorageID
		}
		if command.SiteClientID != nil {
			updates["SiteClientID"] = *command.SiteClientID
		}
		if command.TrialID != nil {
			updates["TrialID"] = command.TrialID
		}
	*/

	if command.UpdateBy != 0 {
		updates["UpdateBy"] = command.UpdateBy
	}
	updates["UpdatedAt"] = time.Now()

	// 领域服务负责批量更新业务逻辑，比如判断媒体是否被锁定，是否归档，是否关联等
	// 使用领域服务创建事件，并接收返回的新上下文
	newCtx, updateIds, err := e.mediaDomainService.BatchUpdateMedia(ctx, command.GetIds(), updates)
	if err != nil {
		e.GetLogger(ctx).Error("调用领域服务批量更新媒体失败", zap.Error(err), zap.Int64s("MediaIds", command.IDs))
		return err
	}

	// 应用服务负责执行批量更新持久化操作
	rowsAffected, err := e.repo.BatchUpdateByIDs(newCtx, updateIds, updates)
	if err != nil {
		e.GetLogger(ctx).Error("数据库批量更新媒体失败", zap.Error(err), zap.Int64s("UpdateIds", updateIds))
		return err
	}

	if rowsAffected == 0 {
		e.GetLogger(ctx).Warn("没有媒体被更新", zap.Int64s("UpdateIds", updateIds))
		return errors.New("要更新的媒体不存在")
	}

	if err := e.publishEventsFromContext(newCtx); err != nil {
		e.GetLogger(ctx).Error("发布MediaBatchUpdatedEvent失败!", zap.Error(err), zap.Int64s("MediaIds", command.IDs))
		return fmt.Errorf("发布批量更新事件失败: %v", err)
	}

	return nil
}

// 批量删除媒体通过媒体领域服务创建事件，并执行批量删除操作
func (e *mediaService) BatchDeleteMedia(ctx context.Context, command *command.BatchDeleteMediaCommand) error {
	// 领域服务负责批量删除业务逻辑，比如判断媒体是否被锁定，是否归档，是否关联等
	var deleteIds []int64
	var err error
	// 使用领域服务创建事件，并接收返回的新上下文
	ctx, deleteIds, err = e.mediaDomainService.BatchDeleteMedia(ctx, command.IDs, int(command.UpdateBy))
	if err != nil {
		e.GetLogger(ctx).Error("调用领域服务批量删除媒体失败", zap.Error(err), zap.Int64s("MediaIds", command.IDs))
		return err
	}

	// gorm支持一次删除多条记录， 即使ID 都不存在时，也不会返回错误
	rowsAffected, err := e.repo.BatchDeleteByIDs(ctx, deleteIds)
	if err != nil {
		e.GetLogger(ctx).Error("数据库批量删除媒体失败", zap.Error(err), zap.Int64s("DeleteIds", deleteIds))
		return err
	}

	if rowsAffected == 0 {
		e.GetLogger(ctx).Warn("没有媒体被删除", zap.Int64s("DeleteIds", deleteIds))
		return errors.New("要删除的媒体不存在")
	}

	// 发布事件
	if err := e.publishEventsFromContext(ctx); err != nil {
		e.GetLogger(ctx).Error("发布MediaBatchDeletedEvent失败!",
			zap.Error(err),
			zap.Int64s("MediaIds", command.IDs))
		return fmt.Errorf("发布批量删除事件失败: %v", err)
	}

	return nil
}

// 批量更新媒体为非执法媒体通过媒体领域服务创建事件，并执行批量更新操作
func (e *mediaService) BatchUpdateMediaNonEnforcementStatus(ctx context.Context, command *command.BatchUpdateNonEnforcementStatusCommand) error {

	// 创建更新字段映射,使用模型中的字段名
	updates := map[string]interface{}{
		"IsNonEnforcementMedia": command.IsNonEnforcementMedia, // 设置是否为执法媒体的状态
		"UpdateBy":              command.UpdateBy,              // 更新操作人
		"UpdatedAt":             time.Now(),                    // 更新时间
	}

	// 领域服务负责批量更新执法媒体状态业务逻辑，比如判断媒体是否被锁定，是否归档，是否关联等
	var err error
	newCtx, ids, err := e.mediaDomainService.BatchUpdateNonEnforcementStatus(ctx, command.IDs, command.IsNonEnforcementMedia, command.UpdateBy, time.Now()) // 使用领域服务创建事件
	if err != nil {
		return err
	}
	// 必须把ctx返回来，因为发送领域事件时，需要使用返回ctx，只有返回的ctx才会携带领域服务创建的领域事件

	// 批量更新所有ID
	rowsAffected, err := e.repo.BatchUpdateByIDs(newCtx, ids, updates)
	if err != nil {
		return fmt.Errorf("批量更新Media状态失败: %s", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("没有找到需要更新的媒体记录")
	}

	if err := e.publishEventsFromContext(newCtx); err != nil {
		e.GetLogger(ctx).Error("发布MediaBatchLawEnforcementSetEvent失败!", zap.Error(err), zap.Int64s("MediaIds", command.IDs))
		return fmt.Errorf("发布批量更新非执法媒体状态事件失败: %v", err)
	}

	return nil
}

// 批量更新媒体执法类型通过媒体领域服务创建事件，并执行批量更新操作
func (e *mediaService) BatchUpdateEnforceType(ctx context.Context, command *command.BatchUpdateEnforceTypeCommand) error {

	// 创建更新字段映射,使用模型中的字段名
	updates := map[string]interface{}{
		"EnforceType": command.EnforceType, // 设置执法类型
		"UpdateBy":    command.UpdateBy,    // 更新操作人
		"UpdatedAt":   time.Now(),          // 更新时间
	}

	// 领域服务负责批量更新执法类型业务逻辑，比如判断媒体是否被锁定，是否归档，是否关联等
	var err error
	newCtx, ids, err := e.mediaDomainService.BatchUpdateEnforceType(ctx, command.IDs, command.EnforceType, command.UpdateBy, time.Now()) // 使用领域服务创建事件
	if err != nil {
		e.GetLogger(ctx).Error("调用领域服务批量更新执法类型失败", zap.Error(err), zap.Int64s("MediaIds", command.IDs))
		return err
	}

	// 批量更新所有ID
	rowsAffected, err := e.repo.BatchUpdateByIDs(newCtx, ids, updates)
	if err != nil {
		e.GetLogger(ctx).Error("数据库批量更新执法类型失败", zap.Error(err), zap.Int64s("UpdateIds", ids))
		return fmt.Errorf("批量更新执法类型失败: %s", err)
	}

	if rowsAffected == 0 {
		e.GetLogger(ctx).Warn("没有媒体被更新", zap.Int64s("UpdateIds", ids))
		return fmt.Errorf("没有找到需要更新的媒体记录")
	}

	if err := e.publishEventsFromContext(newCtx); err != nil {
		e.GetLogger(ctx).Error("发布MediaBatchEnforceTypeUpdatedEvent失败!", zap.Error(err), zap.Int64s("MediaIds", command.IDs))
		return fmt.Errorf("发布批量更新执法类型事件失败: %v", err)
	}

	return nil
}

// 批量更新媒体锁定状态通过媒体领域服务创建事件，并执行批量更新操作
func (e *mediaService) BatchUpdateIsLocked(ctx context.Context, command *command.BatchUpdateIsLockedCommand) error {

	// 创建更新字段映射,使用模型中的字段名
	updates := map[string]interface{}{
		"IsLocked":  command.IsLocked, // 设置锁定状态
		"UpdateBy":  command.UpdateBy, // 更新操作人
		"UpdatedAt": time.Now(),       // 更新时间
	}

	// 领域服务负责批量更新锁定状态业务逻辑，包括权限验证、状态检查等
	var err error
	newCtx, ids, err := e.mediaDomainService.BatchUpdateIsLocked(ctx, command.IDs, command.IsLocked, command.UpdateBy, time.Now()) // 使用领域服务处理业务逻辑
	if err != nil {
		e.GetLogger(ctx).Error("调用领域服务批量更新锁定状态失败", zap.Error(err), zap.Int64s("MediaIds", command.IDs))
		return err
	}

	// 批量更新所有通过验证的ID
	rowsAffected, err := e.repo.BatchUpdateByIDs(newCtx, ids, updates)
	if err != nil {
		e.GetLogger(ctx).Error("数据库批量更新锁定状态失败", zap.Error(err), zap.Int64s("UpdateIds", ids))
		return fmt.Errorf("批量更新锁定状态失败: %s", err)
	}

	if rowsAffected == 0 {
		e.GetLogger(ctx).Warn("没有媒体被更新", zap.Int64s("UpdateIds", ids))
		return fmt.Errorf("没有找到需要更新的媒体记录")
	}

	// 发布领域服务创建的事件
	if err := e.publishEventsFromContext(newCtx); err != nil {
		e.GetLogger(ctx).Error("发布MediaBatchIsLockedUpdatedEvent失败!", zap.Error(err), zap.Int64s("MediaIds", command.IDs))
		return fmt.Errorf("发布批量更新锁定状态事件失败: %v", err)
	}

	// 检查是否有部分失败的情况，记录警告日志
	if partialErrors, ok := newCtx.Value("partialErrors").([]string); ok && len(partialErrors) > 0 {
		e.GetLogger(ctx).Warn("部分媒体更新失败",
			zap.Strings("errors", partialErrors),
			zap.Int64s("successIds", ids),
			zap.Int64s("requestIds", command.IDs))
	}

	return nil
}

func (e *mediaService) publishEvents(ctx context.Context, media *media.Media) error {
	for _, event := range media.Events() {
		// 从上下文中获取租户ID
		tenantID := ctx.Value(global.TenantIDKey)
		if tenantID == nil {
			return fmt.Errorf("无法从上下文中获取租户ID")
		}
		event.SetTenantId(tenantID.(string))

		if err := e.eventPublisher.Publish(ctx, eventbus.MediaEventTopic, event); err != nil {
			// to bo done，发布失败要持久化领域事件，一般来说多节点部署的kafka不应该同时故障
			// 转换为结构体指针
			if domainEvent, ok := event.(*domain_event.DomainEvent); ok {
				fmt.Printf("Pointer conversion successful: %v\n", domainEvent.EventID)
				err = e.eventRepo.Save(ctx, domainEvent)
				if err != nil {
					return fmt.Errorf("领域事件数据库持久化失败! %s", err)
				}
			} else {
				return fmt.Errorf("event conversion failed! %+v", event)
			}

			return fmt.Errorf("发布事件失败: %v", err)
		}
	}
	media.ClearEvents() // 发布成功，清空事件
	return nil
}

// publishEventsFromContext 从上下文中获取并发布领域事件
func (e *mediaService) publishEventsFromContext(ctx context.Context) error {
	// 从上下文中获取领域事件
	events := domain_event.GetEventsFromContext(ctx)
	if len(events) == 0 {
		e.GetLogger(ctx).Warn("上下文中没有领域事件，无需发布")
		return nil
	}

	// 从上下文中获取租户ID
	tenantID := ctx.Value(global.TenantIDKey)
	if tenantID == nil {
		return fmt.Errorf("无法从上下文中获取租户ID")
	}

	// 发布所有事件
	for _, event := range events {
		// 设置租户ID
		event.SetTenantId(tenantID.(string))

		if err := e.eventPublisher.Publish(ctx, eventbus.MediaEventTopic, event); err != nil {
			e.GetLogger(ctx).Error("发布领域事件失败，尝试写入数据库",
				zap.Error(err),
				zap.String("EventID", event.GetEventID()),
				zap.String("EventType", event.GetEventType()))

			// 发布失败需要持久化领域事件
			if domainEvent, ok := event.(*domain_event.DomainEvent); ok {
				err = e.eventRepo.Save(ctx, domainEvent)
				if err != nil {
					return fmt.Errorf("领域事件数据库持久化失败! %s", err)
				}
				// 保存成功，清空上下文中的事件
				domain_event.ClearEventsFromContext(ctx)
			} else {
				e.GetLogger(ctx).Error("事件类型转换失败", zap.Any("Event", event))
				return fmt.Errorf("event conversion failed! %+v", event)
			}
			return fmt.Errorf("发布事件失败: %v", err)
		}
	}

	// 发布成功，清空上下文中的事件
	domain_event.ClearEventsFromContext(ctx)

	return nil
}
