package port

import (
	"context"
	query "jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/models"
)

// MediaQuery 媒体查询接口
type MediaQuery interface {
	// GetPage 获取列表
	GetPage(ctx context.Context, r *query.MediaPagedQuery) (list *[]models.MediaReadModel, count int64, err error)
	// Get 获取对象
	GetByID(ctx context.Context, id int64) (*models.MediaReadModel, error)
}
