package testhelpers

import (
	"jxt-evidence-system/evidence-management/command/internal/application/command"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archivemediarelation"
	commonModels "jxt-evidence-system/evidence-management/shared/common/models"
	"time"
)

// CreateArchiveMediaRelationCommandBuilder 创建档案媒体关联构建器
type CreateArchiveMediaRelationCommandBuilder struct {
	command *command.CreateArchiveMediaRelationCommand
}

func NewCreateArchiveMediaRelationCommandBuilder() *CreateArchiveMediaRelationCommandBuilder {
	return &CreateArchiveMediaRelationCommandBuilder{
		command: &command.CreateArchiveMediaRelationCommand{
			ArchiveId:    1,
			DocumentId:   1,
			RelationType: "primary",
		},
	}
}

func (b *CreateArchiveMediaRelationCommandBuilder) WithArchiveId(archiveId int64) *CreateArchiveMediaRelationCommandBuilder {
	b.command.ArchiveId = archiveId
	return b
}

func (b *CreateArchiveMediaRelationCommandBuilder) WithDocumentId(documentId int64) *CreateArchiveMediaRelationCommandBuilder {
	b.command.DocumentId = documentId
	return b
}

func (b *CreateArchiveMediaRelationCommandBuilder) WithRelationType(relationType string) *CreateArchiveMediaRelationCommandBuilder {
	b.command.RelationType = relationType
	return b
}

func (b *CreateArchiveMediaRelationCommandBuilder) Build() *command.CreateArchiveMediaRelationCommand {
	return b.command
}

// BatchCreateArchiveMediaRelationCommandBuilder 批量创建档案媒体关联构建器
type BatchCreateArchiveMediaRelationCommandBuilder struct {
	command *command.BatchCreateArchiveMediaRelationCommand
}

func NewBatchCreateArchiveMediaRelationCommandBuilder() *BatchCreateArchiveMediaRelationCommandBuilder {
	return &BatchCreateArchiveMediaRelationCommandBuilder{
		command: &command.BatchCreateArchiveMediaRelationCommand{
			ArchiveId:    1,
			DocumentIds:  []int64{1, 2, 3},
			RelationType: "primary",
		},
	}
}

func (b *BatchCreateArchiveMediaRelationCommandBuilder) WithArchiveId(archiveId int64) *BatchCreateArchiveMediaRelationCommandBuilder {
	b.command.ArchiveId = archiveId
	return b
}

func (b *BatchCreateArchiveMediaRelationCommandBuilder) WithDocumentIds(documentIds []int64) *BatchCreateArchiveMediaRelationCommandBuilder {
	b.command.DocumentIds = documentIds
	return b
}

func (b *BatchCreateArchiveMediaRelationCommandBuilder) WithRelationType(relationType string) *BatchCreateArchiveMediaRelationCommandBuilder {
	b.command.RelationType = relationType
	return b
}

func (b *BatchCreateArchiveMediaRelationCommandBuilder) Build() *command.BatchCreateArchiveMediaRelationCommand {
	return b.command
}

// DeleteArchiveMediaRelationCommandBuilder 删除档案媒体关联构建器
type DeleteArchiveMediaRelationCommandBuilder struct {
	command *command.DeleteArchiveMediaRelationCommand
}

func NewDeleteArchiveMediaRelationCommandBuilder() *DeleteArchiveMediaRelationCommandBuilder {
	return &DeleteArchiveMediaRelationCommandBuilder{
		command: &command.DeleteArchiveMediaRelationCommand{
			ID: 1,
		},
	}
}

func (b *DeleteArchiveMediaRelationCommandBuilder) WithID(id int64) *DeleteArchiveMediaRelationCommandBuilder {
	b.command.ID = id
	return b
}

func (b *DeleteArchiveMediaRelationCommandBuilder) Build() *command.DeleteArchiveMediaRelationCommand {
	return b.command
}

// BatchDeleteArchiveMediaRelationCommandBuilder 批量删除档案媒体关联构建器
type BatchDeleteArchiveMediaRelationCommandBuilder struct {
	command *command.BatchDeleteArchiveMediaRelationCommand
}

func NewBatchDeleteArchiveMediaRelationCommandBuilder() *BatchDeleteArchiveMediaRelationCommandBuilder {
	return &BatchDeleteArchiveMediaRelationCommandBuilder{
		command: &command.BatchDeleteArchiveMediaRelationCommand{
			IDs: []int64{1, 2, 3},
		},
	}
}

func (b *BatchDeleteArchiveMediaRelationCommandBuilder) WithIDs(ids []int64) *BatchDeleteArchiveMediaRelationCommandBuilder {
	b.command.IDs = ids
	return b
}

func (b *BatchDeleteArchiveMediaRelationCommandBuilder) Build() *command.BatchDeleteArchiveMediaRelationCommand {
	return b.command
}

// ArchiveMediaRelationBuilder 档案媒体关联聚合根构建器
type ArchiveMediaRelationBuilder struct {
	relation *archivemediarelation.ArchiveMediaRelation
}

func NewArchiveMediaRelationBuilder() *ArchiveMediaRelationBuilder {
	now := time.Now()
	return &ArchiveMediaRelationBuilder{
		relation: &archivemediarelation.ArchiveMediaRelation{
			ID:        0, // 让GORM自动生成ID
			ArchiveId: 1,
			MediaId:   1,
			ControlBy: commonModels.ControlBy{
				CreateBy: 1,
				UpdateBy: 1,
			},
			ModelTime: commonModels.ModelTime{
				CreatedAt: now,
				UpdatedAt: now,
			},
		},
	}
}

func (b *ArchiveMediaRelationBuilder) WithID(id int64) *ArchiveMediaRelationBuilder {
	b.relation.ID = id
	return b
}

func (b *ArchiveMediaRelationBuilder) WithArchiveId(archiveId int64) *ArchiveMediaRelationBuilder {
	b.relation.ArchiveId = archiveId
	return b
}

func (b *ArchiveMediaRelationBuilder) WithMediaId(mediaId int64) *ArchiveMediaRelationBuilder {
	b.relation.MediaId = mediaId
	return b
}

func (b *ArchiveMediaRelationBuilder) WithCreateBy(createBy int) *ArchiveMediaRelationBuilder {
	b.relation.CreateBy = createBy
	return b
}

func (b *ArchiveMediaRelationBuilder) WithUpdateBy(updateBy int) *ArchiveMediaRelationBuilder {
	b.relation.UpdateBy = updateBy
	return b
}

func (b *ArchiveMediaRelationBuilder) WithCreatedAt(createdAt time.Time) *ArchiveMediaRelationBuilder {
	b.relation.CreatedAt = createdAt
	return b
}

func (b *ArchiveMediaRelationBuilder) WithUpdatedAt(updatedAt time.Time) *ArchiveMediaRelationBuilder {
	b.relation.UpdatedAt = updatedAt
	return b
}

func (b *ArchiveMediaRelationBuilder) Build() *archivemediarelation.ArchiveMediaRelation {
	return b.relation
}
