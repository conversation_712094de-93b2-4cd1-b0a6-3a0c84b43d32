package models

import (
	"jxt-evidence-system/evidence-management/shared/common/models"
)

// ArchiveMediaRelation 档案媒体关联迁移模型
type ArchiveMediaRelation struct {
	ID        int64 `json:"id" gorm:"primaryKey;column:id;autoIncrement;comment:主键"`
	ArchiveId int64 `json:"archiveId" gorm:"column:archive_id;comment:档案ID，外键关联t_evidence_archives.archive_id"`
	MediaId   int64 `json:"mediaId" gorm:"column:media_id;comment:媒体ID，外键关联t_evidence_media.media_id"`

	// 审计字段
	models.ControlBy
	models.ModelTime
}

func (*ArchiveMediaRelation) TableName() string {
	return "t_archive_media_relations"
}
