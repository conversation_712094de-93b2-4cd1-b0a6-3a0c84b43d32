package event

import (
	"time"
)

// archive事件类型定义
const (
	// 单资源事件
	EventTypeArchiveCreated = "ArchiveCreated"
	EventTypeArchiveUpdated = "ArchiveUpdated"
	EventTypeArchiveDeleted = "ArchiveDeleted"

	// 批量事件
	EventTypeArchiveBatchUpdated                = "ArchiveBatchUpdated"
	EventTypeArchiveBatchDeleted                = "ArchiveBatchDeleted"
	EventTypeArchiveBatchStatusUpdated          = "ArchiveBatchStatusUpdated"
	EventTypeArchiveBatchExpirationUpdated      = "ArchiveBatchExpirationUpdated"
	EventTypeArchiveBatchStorageDurationUpdated = "ArchiveBatchStorageDurationUpdated"
)

// 具体的领域事件结构体

// ArchiveCreatedPayload 档案创建事件载荷
type ArchiveCreatedPayload struct {
	// 基本信息
	ArchiveID    int64  `json:"archiveId"`
	ArchiveCode  string `json:"archiveCode"`
	ArchiveTitle string `json:"archiveTitle"`
	ArchiveType  int    `json:"archiveType"`
	Description  string `json:"description"`

	// 组织信息
	OrgID int `json:"orgId"`

	// 业务时间信息
	StorageDuration int        `json:"storageDuration"`
	ExpirationTime  *time.Time `json:"expirationTime"`

	// 状态信息
	Status  int    `json:"status"`
	Remarks string `json:"remarks"`

	// 审计字段
	CreateBy  int       `json:"createBy"`
	UpdateBy  int       `json:"updateBy"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func (s *ArchiveCreatedPayload) GetId() interface{} {
	return s.ArchiveID
}

// ArchiveUpdatedPayload 档案更新事件载荷
type ArchiveUpdatedPayload struct {
	ArchiveID     int64                  `json:"archiveId"`
	UpdatedFields map[string]interface{} `json:"updatedFields"`
	UpdateBy      int                    `json:"updateBy"`
	UpdatedAt     time.Time              `json:"updatedAt"`
}

func (s *ArchiveUpdatedPayload) GetId() interface{} {
	return s.ArchiveID
}

// ArchiveDeletedPayload 档案删除事件载荷
type ArchiveDeletedPayload struct {
	ArchiveID int64     `json:"archiveId"`
	UpdateBy  int       `json:"updateBy"`
	DeletedAt time.Time `json:"deletedAt"`
}

func (s *ArchiveDeletedPayload) GetId() interface{} {
	return s.ArchiveID
}

// ArchiveBatchUpdatedPayload 档案批量更新事件载荷
type ArchiveBatchUpdatedPayload struct {
	ArchiveIDs    []int64                `json:"archiveIds"`
	UpdatedFields map[string]interface{} `json:"updatedFields"`
	UpdateBy      int                    `json:"updateBy"`
	UpdatedAt     time.Time              `json:"updatedAt"`
}

func (s *ArchiveBatchUpdatedPayload) GetIds() interface{} {
	return s.ArchiveIDs
}

// ArchiveBatchDeletedPayload 档案批量删除事件载荷
type ArchiveBatchDeletedPayload struct {
	ArchiveIDs []int64   `json:"archiveIds"`
	UpdateBy   int       `json:"updateBy"`
	DeletedAt  time.Time `json:"deletedAt"`
}

func (s *ArchiveBatchDeletedPayload) GetIds() interface{} {
	return s.ArchiveIDs
}

// ArchiveBatchStatusUpdatedPayload 档案批量状态更新事件载荷
type ArchiveBatchStatusUpdatedPayload struct {
	ArchiveIDs []int64   `json:"archiveIds"`
	Status     int       `json:"status"`
	UpdateBy   int       `json:"updateBy"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

func (s *ArchiveBatchStatusUpdatedPayload) GetIds() interface{} {
	return s.ArchiveIDs
}

// ArchiveBatchExpirationUpdatedPayload 档案批量过期时间更新事件载荷
type ArchiveBatchExpirationUpdatedPayload struct {
	ArchiveIDs     []int64   `json:"archiveIds"`
	ExpirationTime time.Time `json:"expirationTime"`
	UpdateBy       int       `json:"updateBy"`
	UpdatedAt      time.Time `json:"updatedAt"`
}

func (s *ArchiveBatchExpirationUpdatedPayload) GetIds() interface{} {
	return s.ArchiveIDs
}

// ArchiveBatchStorageDurationUpdatedPayload 档案批量保存期限更新事件载荷
type ArchiveBatchStorageDurationUpdatedPayload struct {
	ArchiveIDs      []int64   `json:"archiveIds"`
	StorageDuration int       `json:"storageDuration"`
	UpdateBy        int       `json:"updateBy"`
	UpdatedAt       time.Time `json:"updatedAt"`
}

func (s *ArchiveBatchStorageDurationUpdatedPayload) GetIds() interface{} {
	return s.ArchiveIDs
}
