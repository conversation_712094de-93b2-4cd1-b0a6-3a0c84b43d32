package eventbus

import (
	"log"
	"strings"
	"time"

	toolsConfig "github.com/ChenBigdata421/jxt-core/sdk/config"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg"
	"github.com/Shopify/sarama"
	"github.com/ThreeDotsLabs/watermill-kafka/v2/pkg/kafka"
)

func SetupPublisher() {

	// 本文件用golang自带log不用zaplogger，目的是为了能带颜色打印
	log.Printf("Kafka Brokers => %s \n", pkg.Green(strings.Join(toolsConfig.EventBusConfig.Kafka.Brokers, ", ")))

	// 使用 watermill 封装后的 kafka，watermill 针对 kafka 有优化
	kafkaConfig := kafka.PublisherConfig{
		Brokers:               toolsConfig.EventBusConfig.Kafka.Brokers,
		Marshaler:             kafka.DefaultMarshaler{},
		OverwriteSaramaConfig: kafka.DefaultSaramaSyncPublisherConfig(),
	}

	kafkaConfig.OverwriteSaramaConfig.Producer.RequiredAcks = sarama.WaitForLocal
	kafkaConfig.OverwriteSaramaConfig.Producer.Compression = sarama.CompressionSnappy
	kafkaConfig.OverwriteSaramaConfig.Producer.Flush.Frequency = 500 * time.Millisecond
	kafkaConfig.OverwriteSaramaConfig.Producer.Flush.Messages = 100
	kafkaConfig.OverwriteSaramaConfig.Producer.Retry.Max = 3
	kafkaConfig.OverwriteSaramaConfig.Producer.Timeout = 10 * time.Second

	healthCheckInterval := toolsConfig.EventBusConfig.Kafka.HealthCheckInterval
	if healthCheckInterval < 1 {
		healthCheckInterval = 1
	} else if healthCheckInterval > 15 {
		healthCheckInterval = 15
	}

	log.Printf(pkg.Green("Kafka healthCheckInterval: %d minutes"), healthCheckInterval)

	config := DefaultKafkaPublisherManagerConfig()
	config.KafkaPublisherConfig = kafkaConfig
	config.HealthCheckInterval = time.Duration(healthCheckInterval) * time.Minute

	manager, err := NewKafkaPublisherManager(config)
	if err != nil {
		log.Fatal(pkg.Red("new kafka manager error: %v\n"), err)
	}

	if err := manager.Start(); err != nil {
		log.Fatal(pkg.Red("Kafka publisher setup error: %v\n"), err)
	} else {
		log.Println(pkg.Green("Kafka publisher setup success!"))
	}

	DefaultKafkaPublisherManager = manager //如果能保存到Runtime中就更好了，使用时从Runtime获取

	// jiyuanjie 这里可以根据需要，创建NATS jetstream等其它类型的eventbus

}

func SetupSubscriber() {
	log.Printf("Kafka Subscriber Brokers => %s\n", pkg.Green(strings.Join(toolsConfig.EventBusConfig.Kafka.Brokers, ", ")))
	// 使用 watermill 封装后的 kafka，watermill 针对 kafka 有优化
	kafkaConfig := kafka.SubscriberConfig{
		Brokers:               toolsConfig.EventBusConfig.Kafka.Brokers,
		Unmarshaler:           kafka.DefaultMarshaler{},
		OverwriteSaramaConfig: kafka.DefaultSaramaSyncPublisherConfig(),
		ConsumerGroup:         "evidence-query-group",
	}

	// 设置超时和重试
	kafkaConfig.OverwriteSaramaConfig.Net.DialTimeout = 30 * time.Second
	kafkaConfig.OverwriteSaramaConfig.Net.ReadTimeout = 30 * time.Second
	kafkaConfig.OverwriteSaramaConfig.Net.WriteTimeout = 30 * time.Second
	kafkaConfig.OverwriteSaramaConfig.Metadata.Retry.Max = 3
	kafkaConfig.OverwriteSaramaConfig.Metadata.Retry.Backoff = 250 * time.Millisecond
	kafkaConfig.OverwriteSaramaConfig.Producer.Retry.Max = 3
	kafkaConfig.OverwriteSaramaConfig.Producer.Retry.Backoff = 100 * time.Millisecond
	kafkaConfig.OverwriteSaramaConfig.Consumer.Group.Session.Timeout = 20 * time.Second
	kafkaConfig.OverwriteSaramaConfig.Consumer.Group.Heartbeat.Interval = 6 * time.Second

	// 设置 auto.offset.reset 为 earliest
	kafkaConfig.OverwriteSaramaConfig.Consumer.Offsets.Initial = sarama.OffsetOldest

	healthCheckInterval := toolsConfig.EventBusConfig.Kafka.HealthCheckInterval
	if healthCheckInterval < 1 {
		healthCheckInterval = 1
	} else if healthCheckInterval > 15 {
		healthCheckInterval = 15
	}
	log.Printf(pkg.Green("Kafka healthCheckInterval: %d minutes"), healthCheckInterval)

	config := DefaultKafkaSubscriberManagerConfig()
	config.KafkaConfig = kafkaConfig
	config.HealthCheckConfig.Interval = time.Duration(healthCheckInterval) * time.Minute
	config.HealthCheckConfig.MaxMessageAge = time.Duration(healthCheckInterval) * 3 * time.Minute

	manager, err := NewKafkaSubscriberManager(config)
	if err != nil {
		log.Fatal(pkg.Red("new kafka manager error: %v\n"), err)
	}
	if err := manager.Start(); err != nil {
		log.Fatal(pkg.Red("Kafka subscriber setup error: %v\n"), err)
	} else {
		log.Printf(pkg.Green("Kafka subscriber setup success! topic is %s\n"), manager.Config.HealthCheckConfig.Topic)
	}

	DefaultKafkaSubscriberManager = manager //如果能保存到Runtime中就更好了，使用时从Runtime获取

	// jiyuanjie 这里可以根据需要，创建NATS jetstream等其它类型的eventbus

}
