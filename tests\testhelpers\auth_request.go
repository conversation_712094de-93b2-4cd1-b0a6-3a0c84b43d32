package testhelpers

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
)

// SendRequestWithAuth 发送带认证的HTTP请求
func SendRequestWithAuth(baseURL, method, path string, body interface{}, token string) (*http.Response, error) {
	// 将请求体序列化为JSON
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, baseURL+path, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// 创建HTTP客户端
	client := &http.Client{}

	// 发送请求
	return client.Do(req)
}

// SendRequestWithQuery 发送带查询参数的HTTP请求
func SendRequestWithQuery(baseURL, method, path string, body interface{}, query map[string]string) (*http.Response, error) {
	// 将请求体序列化为JSON
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, baseURL+path, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 添加查询参数
	q := req.URL.Query()
	for k, v := range query {
		q.Add(k, v)
	}
	req.URL.RawQuery = q.Encode()

	// 创建HTTP客户端
	client := &http.Client{}

	// 发送请求
	return client.Do(req)
}

// SendRequestWithAuthAndQuery 发送带认证和查询参数的HTTP请求
func SendRequestWithAuthAndQuery(baseURL, method, path string, body interface{}, query map[string]string, token string) (*http.Response, error) {
	// 将请求体序列化为JSON
	var bodyReader io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		bodyReader = bytes.NewBuffer(jsonBody)
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, baseURL+path, bodyReader)
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// 添加查询参数
	q := req.URL.Query()
	for k, v := range query {
		q.Add(k, v)
	}
	req.URL.RawQuery = q.Encode()

	// 创建HTTP客户端
	client := &http.Client{}

	// 发送请求
	return client.Do(req)
}

// ParseResponseToMap 读取并解析HTTP响应体为map
func ParseResponseToMap(resp *http.Response) map[string]interface{} {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil
	}
	defer resp.Body.Close()

	var response map[string]interface{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		return nil
	}

	return response
}
