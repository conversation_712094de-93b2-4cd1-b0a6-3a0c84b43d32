package config

// Application 应用程序配置
type Application struct {
	Mode          string `mapstructure:"mode"`
	Host          string `mapstructure:"host"`
	Name          string `mapstructure:"name"`
	Port          int    `mapstructure:"port"`
	ReadTimeout   int    `mapstructure:"readtimeout"`
	WriterTimeout int    `mapstructure:"writertimeout"`
	EnableDP      bool   `mapstructure:"enabledp"`
	DemoMsg       string `mapstructure:"demomsg"`
}
