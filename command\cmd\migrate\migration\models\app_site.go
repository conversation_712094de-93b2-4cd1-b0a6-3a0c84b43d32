package models

import "time"

type AppSite struct {
	ID               int64     `json:"id" gorm:"primaryKey;column:id;autoIncrement;comment:主键ID"`
	Name             string    `json:"name" gorm:"size:255;column:name;comment:名称"`
	IP               string    `json:"ip" gorm:"size:255;column:ip;comment:IP"`
	Address          string    `json:"address" gorm:"size:255;column:address;comment:物理地址"`
	HTTP             string    `json:"http" gorm:"size:255;column:http;comment:播放地址"`
	NoticeURL        string    `json:"noticeUrl" gorm:"size:255;column:notice_url;comment:通知"`
	SecretKey        string    `json:"secretKey" gorm:"size:255;column:secret_key;comment:密钥"`
	IsOpen           int       `json:"isOpen" gorm:"size:4;column:is_open;comment:0:禁用；1：启用"`
	AdminPoliceID    int64     `json:"adminPoliceId" gorm:"column:admin_police_id;comment:管理员（警员id）"`
	OrgID            int64     `json:"orgId" gorm:"column:org_id;comment:单位id"`
	OnlineTimeTotal  int64     `json:"onlineTimeTotal" gorm:"column:online_time_total;comment:在线总时长（秒）"`
	No               string    `json:"no" gorm:"size:255;column:no;comment:采集站编号"`
	BrandID          int64     `json:"brandId" gorm:"column:brand_id;comment:品牌ID"`
	CPU              string    `json:"cpu" gorm:"size:255;column:cpu;comment:CPU"`
	Memory           string    `json:"memory" gorm:"size:255;column:memory;comment:内存，单位：G"`
	Disk             string    `json:"disk" gorm:"size:255;column:disk;comment:磁盘，单位：G"`
	BuyTime          time.Time `json:"buyTime" gorm:"column:buy_time;comment:购买时间"`
	USBNum           int       `json:"usbNum" gorm:"size:4;column:usb_num;comment:UDB数量"`
	System           string    `json:"system" gorm:"size:255;column:system;comment:操作系统"`
	Version          string    `json:"version" gorm:"size:255;column:version;comment:版本号"`
	State            int       `json:"state" gorm:"size:4;column:state;comment:0:正常（默认）;1:报废;2:报修;3:库存;4:遗失"`
	DiskUseThreshold int       `json:"diskUseThreshold" gorm:"size:4;column:disk_use_threshold;comment:磁盘使用阈值 默认90"`
	Remark           string    `json:"remark" gorm:"size:255;column:remark;comment:备注"`
	ControlBy
	ModelTime
}
