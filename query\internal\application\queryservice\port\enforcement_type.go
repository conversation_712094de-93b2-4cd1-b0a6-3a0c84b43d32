package port

import (
	"context"
	query "jxt-evidence-system/evidence-management/query/internal/application/query"
)

type EnforcementTypeService interface {
	// GetByID 根据ID获取执法类型
	EnforcementTypeGetByID(ctx context.Context, id int64) (*query.EnforcementTypeDTO, error)
	// List 获取执法类型列表
	EnforcementTypeGetPage(ctx context.Context, r *query.EnforcementTypePagedQuery) ([]*query.EnforcementTypeDTO, int64, error)
	// ListTree 获取执法类型树形结构
	EnforcementTypeListTree(ctx context.Context) ([]*query.EnforcementTypeDTO, error)
}
