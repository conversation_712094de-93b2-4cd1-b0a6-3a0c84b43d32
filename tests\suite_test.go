package tests

import (
	"net/http"
	"testing"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	commandTesthelpers "jxt-evidence-system/evidence-management/command/testhelpers"
	queryTesthelpers "jxt-evidence-system/evidence-management/query/testhelpers"
)

var dbCommand *gorm.DB
var dbQuery *gorm.DB

var client *http.Client
var baseURL string

var token string

func TestApi(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "API Suite") // 使用一个通用的套件名称
}

var _ = BeforeSuite(func() {
	// 使用 dockertest 启动一个 PostgreSQL 容器
	// pool, err := dockertest.NewPool("")
	// Expect(err).NotTo(HaveOccurred())

	// resource, err := pool.RunWithOptions(&dockertest.RunOptions{
	// 	Repository: "postgres",
	// 	Tag:        "13",
	// 	Env: []string{
	// 		"POSTGRES_PASSWORD=secret",
	// 		"POSTGRES_DB=testdb",
	// 	},
	// }, func(config *docker.HostConfig) {
	// 	config.AutoRemove = true
	// 	config.RestartPolicy = docker.RestartPolicy{
	// 		Name: "no",
	// 	}
	// })
	// Expect(err).NotTo(HaveOccurred())

	// // 等待 PostgreSQL 容器准备就绪
	// if err := pool.Retry(func() error {
	// 	var err error
	// 	db, err = gorm.Open(postgres.Open(fmt.Sprintf("host=localhost port=%s user=postgres dbname=testdb password=secret sslmode=disable", resource.GetPort("5432/tcp"))), &gorm.Config{})
	// 	if err != nil {
	// 		return err
	// 	}
	// 	return db.Raw("SELECT 1").Error
	// }); err != nil {
	// 	Expect(err).NotTo(HaveOccurred())
	// }

	// // 设置环境变量
	// os.Setenv("DB_HOST", "localhost")
	// os.Setenv("DB_PORT", resource.GetPort("5432/tcp"))
	// os.Setenv("DB_USER", "postgres")
	// os.Setenv("DB_PASSWORD", "secret")
	// os.Setenv("DB_NAME", "testdb")

	// 初始化应用程序
	//config.Setup("../config/settings.yml")
	//router = api.InitRouter()  // 直接测试 gin 路由器，适合单元测试和集成测试，不涉及网络操作

	// 初始化 命令微服务 GORM
	var err error
	dsnCommand := "root:123456@tcp(localhost:3307)/evidence_command_db?charset=utf8&parseTime=True&loc=Local&timeout=1000ms"
	dbCommand, err = gorm.Open(mysql.Open(dsnCommand), &gorm.Config{})
	if err != nil {
		Fail("无法连接到数据库: " + err.Error())
	}

	// 设置连接池
	sqlDBCommand, err := dbCommand.DB()
	if err != nil {
		Fail("无法获取数据库连接: " + err.Error())
	}
	sqlDBCommand.SetMaxIdleConns(10)
	sqlDBCommand.SetMaxOpenConns(100)
	sqlDBCommand.SetConnMaxLifetime(time.Hour)

	// 创建命令数据库表
	dbCommand.AutoMigrate(commandTesthelpers.NewMediaBuilder().Build())
	dbCommand.AutoMigrate(commandTesthelpers.NewEnforcementTypeBuilder().Build())
	dbCommand.AutoMigrate(commandTesthelpers.NewArchiveBuilder().Build())
	dbCommand.AutoMigrate(commandTesthelpers.NewArchiveMediaRelationBuilder().Build())

	// 初始化 查询微服务 GORM
	dsnQuery := "postgres://root:123456@localhost:5433/evidencedb_query_db?sslmode=disable&connect_timeout=1&TimeZone=Asia/Shanghai"
	dbQuery, err = gorm.Open(postgres.Open(dsnQuery), &gorm.Config{})
	if err != nil {
		Fail("无法连接到数据库: " + err.Error())
	}

	// 设置连接池
	sqlDBQuery, err := dbQuery.DB()
	if err != nil {
		Fail("无法获取数据库连接: " + err.Error())
	}
	sqlDBQuery.SetMaxIdleConns(10)
	sqlDBQuery.SetMaxOpenConns(100)
	sqlDBQuery.SetConnMaxLifetime(time.Hour)

	// 创建数据库表
	dbQuery.AutoMigrate(queryTesthelpers.NewMediaReadModelBuilder().Build())
	dbQuery.AutoMigrate(queryTesthelpers.NewEnforcementTypeReadModelBuilder().Build())
	dbQuery.AutoMigrate(queryTesthelpers.NewArchiveReadModelBuilder().Build())
	dbQuery.AutoMigrate(queryTesthelpers.NewArchiveMediaRelationReadModelBuilder().Build())

	// 假设你的 API 服务器在测试之前已经启动
	// 你可能需要在这里启动你的服务器，或者使用一个已经运行的服务器
	baseURL = "http://localhost:8080" // 替换为你的 API 服务器地址
	client = &http.Client{}

	// 获取 token
	//token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhc2NvcGUiOiIiLCJleHAiOjQ4ODI1ODc0NjIsImlkZW50aXR5IjoxLCJuaWNlIjoiYWRtaW4iLCJvcmlnX2lhdCI6MTcyODk1MTQ2Miwicm9sZWlkIjoxLCJyb2xla2V5IjoiYWRtaW4iLCJyb2xlbmFtZSI6Iuezu-e7n-euoeeQhuWRmCJ9._4JBdcvpCJv35bHiPIPwlOGFM6vjA2pz1Xnd6fEbNqM"
	//token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhc2NvcGUiOiIiLCJleHAiOjQ4ODI1ODc0NjIsImlkZW50aXR5IjoxLCJuaWNlIjoiYWRtaW4iLCJvcmlnX2lhdCI6MTcyODk1MTQ2Miwicm9sZWlkIjoxLCJyb2xla2V5IjoiYWRtaW4iLCJyb2xlbmFtZSI6Iuezu-e7n-euoeeQhuWRmCJ9._4JBdcvpCJv35bHiPIPwlOGFM6vjA2pz1Xnd6fEbNqM"
	//token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhc2NvcGUiOiIiLCJleHAiOjE3NDMyNDU4OTksImlkZW50aXR5IjoxLCJuaWNlIjoiIiwib3JpZ19pYXQiOjE3NDMyNDIyOTksInJvbGVpZCI6MSwicm9sZWtleSI6ImFkbWluIiwicm9sZW5hbWUiOiLns7vnu5_nrqHnkIblkZgifQ.6CuuU57K37JKyIONsZFaFCflOuEaSdvRGSbnzwr2Gmc"
	//token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhc2NvcGUiOiIiLCJleHAiOjE3NDMyNTk4OTEsImlkZW50aXR5IjoxLCJuaWNlIjoiIiwib3JpZ19pYXQiOjE3NDMyNTYyOTEsInJvbGVpZCI6MSwicm9sZWtleSI6ImFkbWluIiwicm9sZW5hbWUiOiLns7vnu5_nrqHnkIblkZgifQ.9szUnmkIEqf1v9TUm9-7ljXntD6J3vd2wqBLHVUBj3g"
	token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhc2NvcGUiOiIiLCJleHAiOjQ4OTY5MDA3NDUsImlkZW50aXR5IjoxLCJuaWNlIjoiIiwib3JpZ19pYXQiOjE3NDMyNjQ3NDUsInJvbGVpZCI6MSwicm9sZWtleSI6ImFkbWluIiwicm9sZW5hbWUiOiLns7vnu5_nrqHnkIblkZgifQ.rfGjvm0PfkRMMeMSesx78dozDs7r3AxBOhR0dxM9f78"
})

var _ = AfterSuite(func() {
	// 清空命令数据库表
	if dbCommand != nil {
		dbCommand.Exec("DELETE FROM t_evidence_media;")
		dbCommand.Exec("DELETE FROM t_evidence_enforcement_types;")
		dbCommand.Exec("DELETE FROM t_evidence_archives;")
		dbCommand.Exec("DELETE FROM t_evidence_archive_media_relations;")
		// 清理命令数据库资源
		if sqlDBCommand, err := dbCommand.DB(); err == nil && sqlDBCommand != nil {
			sqlDBCommand.Close()
		}
	}

	// 清理查询数据库资源
	if dbQuery != nil {
		// 清空查询数据库表
		dbQuery.Exec("DELETE FROM t_evidence_media_read;")
		dbQuery.Exec("DELETE FROM t_evidence_enforcement_types_read;")
		dbQuery.Exec("DELETE FROM t_evidence_archives_read;")
		dbQuery.Exec("DELETE FROM t_evidence_archive_media_relations_read;")
		// 清理查询数据库资源
		if sqlDBQuery, err := dbQuery.DB(); err == nil && sqlDBQuery != nil {
			sqlDBQuery.Close()
		}
	}
})
