# 权限检查机制分析文档

## 概述

本文档详细分析了证据管理系统中Gin中间件的权限检查机制。该系统采用了**5层递进式权限控制架构**，实现了从租户隔离到数据权限的全方位安全控制。

## 权限检查架构图

```mermaid
graph TD
    A["HTTP请求"] --> B["1️⃣ 租户识别<br/>TenantResolver"]
    B --> C["2️⃣ JWT认证<br/>authMiddleware.MiddlewareFunc()"]
    C --> D["3️⃣ RBAC权限检查<br/>AuthCheckRole()"]
    D --> E["4️⃣ 数据权限控制<br/>PermissionAction()"]
    E --> F["5️⃣ 业务逻辑处理<br/>Handler"]
    
    B --> B1["根据Host识别租户<br/>设置TenantID到Context"]
    C --> C1["验证JWT Token<br/>提取用户角色信息"]
    D --> D1["检查用户是否为admin"]
    D1 -->|是| D4["直接通过"]
    D1 -->|否| D2["检查URL是否在排除列表"]
    D2 -->|是| D4
    D2 -->|否| D3["Casbin RBAC检查<br/>角色+URL+Method"]
    D3 -->|通过| D4
    D3 -->|拒绝| D5["返回403错误"]
    E --> E1["查询用户数据权限范围<br/>设置到Context"]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
    style F fill:#f1f8e9
```

## 详细分析

### 第1层：租户识别（TenantResolver）

#### 功能描述
租户识别中间件负责根据请求的Host头部信息识别租户，实现多租户架构的数据隔离。

#### 核心代码
```go
func TenantResolver(c *gin.Context) {
    // 如果没有启用多租户，tenantID设置为*，并直接返回
    if !config.TenantsConfig.Enabled {
        c.Set("db", sdk.Runtime.GetTenantDB("*").WithContext(c))
        ctx := context.WithValue(c.Request.Context(), global.TenantIDKey, "*")
        c.Request = c.Request.WithContext(ctx)
        c.Next()
        return
    }
    
    // 根据host解析TenantID
    tenantID := sdk.Runtime.GetTenantID(c.Request.Host)
    if tenantID != "" {
        c.Set("db", sdk.Runtime.GetTenantDB(tenantID).WithContext(c))
        ctx := context.WithValue(c.Request.Context(), global.TenantIDKey, tenantID)
        c.Request = c.Request.WithContext(ctx)
        c.Next()
        return
    }
}
```

#### 配置方式
租户识别支持多种方式：
- **host**: 根据域名识别（默认）
- **header**: 根据HTTP头部识别
- **query**: 根据查询参数识别
- **path**: 根据URL路径识别

#### 配置示例
```yaml
tenants:
  enabled: false  # 是否启用多租户模式
  resolver:
    type: "host"  # 识别方式
    headerName: "X-Tenant-ID"  # header方式使用的头部名称
    queryParam: "tenant"  # query方式使用的参数名称
    pathIndex: 0  # path方式使用的路径索引
```

### 第2层：JWT认证（JWT Authentication）

#### 功能描述
JWT认证中间件负责验证用户身份，提取用户角色和权限信息。

#### 核心配置
```go
func AuthInit() (*jwt.GinJWTMiddleware, error) {
    return jwt.New(&jwt.GinJWTMiddleware{
        Realm:           "test zone",
        Key:             []byte(config.JwtConfig.Secret),
        Timeout:         timeout,
        MaxRefresh:      time.Hour,
        PayloadFunc:     handler.PayloadFunc,
        IdentityHandler: handler.IdentityHandler,
        Authenticator:   handler.Authenticator,
        Authorizator:    handler.Authorizator,
        Unauthorized:    handler.Unauthorized,
        TokenLookup:     "header: Authorization, query: token, cookie: jwt",
        TokenHeadName:   "Bearer",
        TimeFunc:        time.Now,
    })
}
```

#### JWT Payload结构
```go
func PayloadFunc(data interface{}) jwt.MapClaims {
    if v, ok := data.(map[string]interface{}); ok {
        u, _ := v["user"].(SysUser)
        r, _ := v["role"].(SysRole)
        return jwt.MapClaims{
            jwt.IdentityKey:  u.UserId,
            jwt.RoleIdKey:    r.RoleId,
            jwt.RoleKey:      r.RoleKey,
            jwt.NiceKey:      u.Username,
            jwt.DataScopeKey: r.DataScope,
            jwt.RoleNameKey:  r.RoleName,
        }
    }
    return jwt.MapClaims{}
}
```

### 第3层：RBAC权限检查（AuthCheckRole）

#### 功能描述
RBAC权限检查是核心的功能权限控制层，基于角色、URL路径和HTTP方法进行权限验证。

#### 核心逻辑---直接使用 Setup 方法已加载的权限规则进行权限检查
Setup 方法从 sys.casbin_rule 表中读取了完整的权限策略数据，包括：
🔐 所有角色的权限规则
👥 用户与角色的关系映射
🛡️ 资源访问控制规则
这些数据构成了整个系统的权限控制基础。

```go
func AuthCheckRole() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 获取租户ID
        ctx := c.Request.Context()
        tenantID, ok := ctx.Value(global.TenantIDKey).(string)
        if !ok {
            response.Error(c, 500, errors.New("tenant id not exist"), "")
            return
        }
        
        // 2. 获取用户角色信息
        data, _ := c.Get(jwtauth.JwtPayloadKey)
        v := data.(jwtauth.MapClaims)
        e := sdk.Runtime.GetTenantCasbin(tenantID) //在Setup方法中，写入内存
        
        // 3. admin用户直接通过
        if v["rolekey"] == "admin" {
            c.Next()
            return
        }
        
        // 4. 检查排除列表
        for _, i := range CasbinExclude {
            if util.KeyMatch2(c.Request.URL.Path, i.Url) && c.Request.Method == i.Method {
                c.Next()
                return
            }
        }
        
        // 5. Casbin RBAC检查
        res, err := e.Enforce(v["rolekey"], c.Request.URL.Path, c.Request.Method)
        if err != nil {
            response.Error(c, 500, err, "")
            return
        }
        
        if res {
            c.Next()
        } else {
            c.JSON(http.StatusOK, gin.H{
                "code": 403,
                "msg":  "对不起，您没有该接口访问权限，请联系管理员",
            })
            c.Abort()
            return
        }
    }
}
```

#### 权限排除列表
系统定义了不需要权限检查的URL列表：

```go
var CasbinExclude = []UrlInfo{
    {Url: "/api/v1/login", Method: "POST"},
    {Url: "/api/v1/logout", Method: "POST"},
    {Url: "/api/v1/getCaptcha", Method: "GET"},
    {Url: "/api/v1/getinfo", Method: "GET"},
    {Url: "/api/v1/refresh_token", Method: "GET"},
    // ... 更多排除URL
}
```

#### Casbin模型
系统使用Casbin进行RBAC权限管理，支持以下权限模型：
- **Subject**: 用户角色（rolekey）
- **Object**: API路径（URL Path）
- **Action**: HTTP方法（GET/POST/PUT/DELETE）

### 第4层：数据权限控制（PermissionAction）

#### 功能描述
数据权限控制负责限制用户只能访问其权限范围内的数据，实现行级数据安全。

#### 核心实现
```go
func PermissionAction() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 获取数据库连接
        db, err := pkg.GetOrm(c)
        if err != nil {
            log.Error("get db failure", zap.Error(err))
            return
        }
        
        // 2. 查询用户数据权限
        var p = new(DataPermission)
        if userId := user.GetUserIdStr(c); userId != "" {
            p, err = newDataPermission(db, userId)
            if err != nil {
                response.Error(c, 500, err, "权限范围鉴定错误")
                c.Abort()
                return
            }
        }
        
        // 3. 设置到Context供后续使用
        c.Set(PermissionKey, p)
        c.Next()
    }
}
```

#### 数据权限范围
```go
type DataPermission struct {
    DataScope string  // 数据权限范围
    UserId    int     // 用户ID
    DeptId    int     // 部门ID
    RoleId    int     // 角色ID
}
```

支持的数据权限范围：
- **"1"**: 全部数据权限
- **"2"**: 角色数据权限（同角色用户的数据）
- **"3"**: 部门数据权限（同部门用户的数据）
- **"4"**: 部门及以下数据权限（本部门及子部门用户的数据）
- **"5"**: 仅本人数据权限（只能访问自己创建的数据）

#### 数据权限应用---从数据库中读取的字段：
✅ sys_user.user_id - 用户ID
✅ sys_role.role_id - 角色ID
✅ sys_user.org_id - 用户所属组织ID
✅ sys_role.data_scope - 数据权限范围

```go
func Permission(tableName string, p *DataPermission) func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        if !config.ApplicationConfig.EnableDP {
            return db
        }
        switch p.DataScope {
        case "2":
            return db.Where(tableName+".create_by in (select sys_user.user_id from sys_role_dept left join sys_user on sys_user.dept_id=sys_role_dept.dept_id where sys_role_dept.role_id = ?)", p.RoleId)
        case "3":
            return db.Where(tableName+".create_by in (SELECT user_id from sys_user where dept_id = ? )", p.DeptId)
        case "4":
            return db.Where(tableName+".create_by in (SELECT user_id from sys_user where sys_user.dept_id in(select dept_id from sys_dept where dept_path like ? ))", "%/"+pkg.IntToString(p.DeptId)+"/%")
        case "5":
            return db.Where(tableName+".create_by = ?", p.UserId)
        default:
            return db
        }
    }
}
```

### 第5层：路由级权限配置

#### 功能描述
在路由级别配置具体的权限检查组合，不同的API可以使用不同的权限检查策略。

#### 配置示例

**媒体管理API权限配置**：
```go
func registerMediaRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
    err := di.Invoke(func(handler *api.MediaHandler) {
        if handler != nil {
            r := v1.Group("/media").
                Use(authMiddleware.MiddlewareFunc()).      // JWT认证
                Use(middleware.AuthCheckRole())            // RBAC权限检查
            {
                r.POST("", handler.MediaUpload)            // 创建媒体
                r.PUT("/:id", handler.MediaUpdateByID)     // 更新媒体
                r.DELETE("/:id", handler.MediaDeleteByID) // 删除媒体
                r.PUT("/batch", handler.MediaBatchUpdate) // 批量更新
                r.DELETE("/batch", handler.MediaBatchDelete) // 批量删除
            }
        }
    })
}
```

**通用CRUD权限配置**：
```go
func register{{.ClassName}}Router(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
    r := v1.Group("/{{.ModuleName}}").
        Use(authMiddleware.MiddlewareFunc()).              // JWT认证
        Use(middleware.AuthCheckRole())                    // RBAC权限检查
    {
        r.GET("", actions.PermissionAction(), api.GetPage)        // 查询+数据权限
        r.GET("/:id", actions.PermissionAction(), api.Get)        // 详情+数据权限
        r.POST("", api.Insert)                                    // 创建（无数据权限检查）
        r.PUT("/:id", actions.PermissionAction(), api.Update)     // 更新+数据权限
        r.DELETE("", actions.PermissionAction(), api.Delete)      // 删除+数据权限
    }
}
```

## 权限检查流程示例

### 场景：用户请求更新媒体

**请求**: `PUT /api/v1/media/123`

#### 执行流程：

1. **租户识别**
   - 解析Host: `app.example.com`
   - 确定租户ID: `tenant_primary`
   - 设置租户数据库连接

2. **JWT认证**
   - 验证Authorization头部的Token
   - 提取用户信息: `{userId: 1001, rolekey: "editor", dataScope: "3"}`

3. **RBAC权限检查**
   - 检查用户角色: `editor` ≠ `admin`，继续检查
   - 检查排除列表: `PUT /api/v1/media/:id` 不在排除列表中
   - Casbin检查: 查询`editor`角色是否有`PUT /api/v1/media/:id`权限
   - 结果: **通过**

4. **数据权限控制**
   - 查询用户数据权限: `dataScope: "3"` (部门数据权限)
   - 设置查询条件: 只能更新同部门用户创建的媒体

5. **业务逻辑处理**
   - 执行媒体更新逻辑
   - 应用数据权限过滤条件
   - 返回更新结果

### 可能的权限拒绝场景

1. **JWT认证失败**
   - Token过期或无效
   - 返回401 Unauthorized

2. **RBAC权限不足**
   - 用户角色没有访问该API的权限
   - 返回403 Forbidden

3. **数据权限限制**
   - 尝试更新其他部门的媒体
   - 返回404 Not Found或403 Forbidden

## 权限管理最佳实践

### 1. 角色设计原则
- **最小权限原则**: 角色只分配必要的权限
- **职责分离**: 不同角色负责不同的业务功能
- **权限继承**: 上级角色可以包含下级角色的权限

### 2. 数据权限配置
- **部门级隔离**: 使用部门数据权限确保数据安全
- **个人数据保护**: 敏感操作使用个人数据权限
- **管理员特权**: admin角色拥有全部数据访问权限

### 3. 性能优化
- **admin直通**: admin用户跳过复杂的权限检查
- **排除列表**: 公共API加入排除列表减少检查开销
- **缓存策略**: Casbin权限策略支持缓存加速

### 4. 安全加固
- **Token刷新**: 定期刷新JWT Token减少安全风险
- **审计日志**: 记录所有权限检查结果用于审计
- **异常处理**: 权限检查失败时提供明确的错误信息

## 配置文件说明

### JWT配置
```yaml
jwt:
  secret: "your-secret-key"     # JWT签名密钥
  timeout: 3600                 # Token有效期（秒）
  maxRefresh: 86400            # 最大刷新时间（秒）
```

### 多租户配置
```yaml
tenants:
  enabled: false               # 是否启用多租户
  resolver:
    type: "host"              # 租户识别方式
    headerName: "X-Tenant-ID" # Header识别时的头部名称
  list:
    - id: "tenant1"           # 租户ID
      name: "Tenant 1"        # 租户名称
      active: true            # 是否激活
      hosts: ["app1.com"]     # 绑定的域名
```

### 数据权限配置
```yaml
application:
  enableDP: true              # 是否启用数据权限
```

## 总结

证据管理系统的权限检查机制具有以下特点：

### 优势
1. **多层防护**: 5层递进式检查确保安全性
2. **多租户支持**: 完整的租户隔离机制
3. **灵活配置**: 支持多种权限检查组合
4. **高性能**: admin直通和排除列表优化性能
5. **细粒度控制**: 从功能权限到数据权限的全方位控制

### 适用场景
- 企业级SaaS应用
- 多租户管理系统
- 需要严格权限控制的业务系统
- 政府和金融等安全要求高的行业

### 扩展性
- 支持自定义权限检查中间件
- 支持动态权限策略配置
- 支持第三方权限管理系统集成

该权限检查机制为证据管理系统提供了企业级的安全保障，确保了数据的安全性和系统的可靠性。 