package testhelpers

import (
	"fmt"
	"net/http"
	"time"

	. "github.com/onsi/gomega"
	"gorm.io/gorm"

	commandTesthelpers "jxt-evidence-system/evidence-management/command/testhelpers"
)

// AddRootEnforcementTypes 创建指定数量的根执法类型
// count: 要创建的根执法类型数量
// baseURL: API的基础URL
// token: 认证token
// dbCommand: 数据库连接
// 返回创建的根执法类型编码列表
func AddRootEnforcementTypes(randNum int, count int, baseURL string, token string, dbCommand *gorm.DB) []string {
	enforcementTypeCodes := make([]string, 0, count)
	// 创建根执法类型记录
	for i := 0; i < count; i++ {
		fmt.Print(".")
		createCommand := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
			WithCode(fmt.Sprintf("ROOT_CODE%06d", randNum+i)).
			WithName(fmt.Sprintf("根执法类型%06d", randNum+i)).
			WithDesc(fmt.Sprintf("根测试执法类型%06d", randNum+i)).
			WithParentId(0).
			WithSource("test").
			WithSort(1).
			Build()

		// 发送创建请求
		resp, err := SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", createCommand, token)
		Expect(err).NotTo(HaveOccurred())
		Expect(resp.StatusCode).To(Equal(http.StatusOK))

		// 读取并验证响应
		response := ParseResponseToMap(resp)
		Expect(response["code"]).To(Equal(float64(http.StatusOK)))
		Expect(response["msg"]).To(Equal("创建执法类型成功"))

		// 验证命令数据库中是否创建了新记录
		var count int64
		enforcementType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
		err = dbCommand.Model(enforcementType).Where("enforcement_type_code = ?",
			createCommand.EnforcementTypeCode).Count(&count).Error
		Expect(err).NotTo(HaveOccurred())
		Expect(count).To(Equal(int64(1)))

		err = dbCommand.Model(enforcementType).Where("enforcement_type_code = ?",
			createCommand.EnforcementTypeCode).First(&enforcementType).Error
		Expect(err).NotTo(HaveOccurred())

		Expect(enforcementType.EnforcementTypeName).To(Equal(createCommand.EnforcementTypeName))
		Expect(enforcementType.EnforcementTypeDesc).To(Equal(createCommand.EnforcementTypeDesc))
		Expect(enforcementType.ParentId).To(Equal(createCommand.ParentId))
		Expect(enforcementType.Source).To(Equal(createCommand.Source))
		Expect(enforcementType.Sort).To(Equal(createCommand.Sort))

		// 获取新创建的执法类型编码
		enforcementTypeCodes = append(enforcementTypeCodes, createCommand.EnforcementTypeCode)

		// 这里延迟100毫秒，创建下一个根执法类型
		time.Sleep(100 * time.Millisecond)
	}

	return enforcementTypeCodes
}

// AddChildEnforcementTypes 为指定的执法类型添加多个子执法类型
// parentTypeCode: 父执法类型的编码
// count: 要添加的子执法类型数量
// baseURL: API的基础URL
// token: 认证token
// dbCommand: 数据库连接
// 返回创建的子执法类型编码列表
func AddChildEnforcementTypes(randNum int, parentTypeCode string, count int, baseURL string, token string, dbCommand *gorm.DB) []string {
	// 获取父执法类型ID
	parentType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
	err := dbCommand.Where("enforcement_type_code = ?", parentTypeCode).First(&parentType).Error
	Expect(err).NotTo(HaveOccurred())

	enforcementTypeCodes := make([]string, 0, count)
	// 创建子执法类型记录
	for i := 0; i < count; i++ {
		fmt.Print(".")
		createChildCommand := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
			WithCode(fmt.Sprintf("%s_CHILD_CODE%06d", parentTypeCode, randNum+i)).
			WithName(fmt.Sprintf("%s_子执法类型%06d", parentType.EnforcementTypeName, randNum+i)).
			WithDesc(fmt.Sprintf("%s_子测试执法类型%06d", parentType.EnforcementTypeDesc, randNum+i)).
			WithParentId(parentType.ID).
			WithSource("test").
			WithSort(1).
			Build()

		// 发送创建请求
		resp, err := SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", createChildCommand, token)
		Expect(err).NotTo(HaveOccurred())
		Expect(resp.StatusCode).To(Equal(http.StatusOK))

		// 读取并验证响应
		response := ParseResponseToMap(resp)
		Expect(response["code"]).To(Equal(float64(http.StatusOK)))
		Expect(response["msg"]).To(Equal("创建执法类型成功"))

		// 验证命令数据库中是否创建了新记录
		var count int64
		childEnforcementType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
		err = dbCommand.Model(childEnforcementType).Where("enforcement_type_code = ?",
			createChildCommand.EnforcementTypeCode).Count(&count).Error
		Expect(err).NotTo(HaveOccurred())
		Expect(count).To(Equal(int64(1)))

		err = dbCommand.Model(childEnforcementType).Where("enforcement_type_code = ?",
			createChildCommand.EnforcementTypeCode).First(&childEnforcementType).Error
		Expect(err).NotTo(HaveOccurred())

		Expect(childEnforcementType.EnforcementTypeName).To(Equal(createChildCommand.EnforcementTypeName))
		Expect(childEnforcementType.EnforcementTypeDesc).To(Equal(createChildCommand.EnforcementTypeDesc))
		Expect(childEnforcementType.ParentId).To(Equal(createChildCommand.ParentId))
		Expect(childEnforcementType.Source).To(Equal(createChildCommand.Source))
		Expect(childEnforcementType.Sort).To(Equal(createChildCommand.Sort))

		// 获取新创建的执法类型编码
		enforcementTypeCodes = append(enforcementTypeCodes, createChildCommand.EnforcementTypeCode)

		// 这里延迟100毫秒，创建下一个子执法类型
		time.Sleep(100 * time.Millisecond)
	}

	return enforcementTypeCodes
}
