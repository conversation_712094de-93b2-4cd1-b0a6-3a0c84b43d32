package router

import (
	"jxt-evidence-system/evidence-management/query/internal/interface/rest/api"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/middleware"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	//"jxt-evidence-system/evidence-management/shared/common/middleware"

	jwt "github.com/ChenBigdata421/jxt-core/sdk/pkg/jwtauth"
	"github.com/gin-gonic/gin"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerMediaQueryRouter)
}

func registerMediaQueryRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {

	// jiyuanjie 添加通过依赖注入创建的repo，service，api，代替前面的手工创建
	//解析 MediaApi
	err := di.Invoke(func(mediaHandler *api.MediaHandler) {
		if mediaHandler != nil {
			r := v1.Group("/media").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
			{
				r.GET("", mediaHandler.GetPage)
				r.GET("/:id", mediaHandler.GetByID)
			}
		} else {
			logger.Fatal("MediaHandler is nil after resolution")
		}
	})

	if err != nil {
		logger.Fatalf("Failed to resolve MediaHandler: %v", err)
	}

}
