// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: user.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserInfoService_GetUserById_FullMethodName       = "/user.UserInfoService/GetUserById"
	UserInfoService_GetUserByPoliceNo_FullMethodName = "/user.UserInfoService/GetUserByPoliceNo"
)

// UserInfoServiceClient is the client API for UserInfoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 用户信息服务
type UserInfoServiceClient interface {
	// 根据用户ID查询用户信息
	GetUserById(ctx context.Context, in *GetUserByIdReq, opts ...grpc.CallOption) (*UserInfoReply, error)
	// 根据警号查询用户信息
	GetUserByPoliceNo(ctx context.Context, in *GetUserByPoliceNoReq, opts ...grpc.CallOption) (*UserInfoReply, error)
}

type userInfoServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserInfoServiceClient(cc grpc.ClientConnInterface) UserInfoServiceClient {
	return &userInfoServiceClient{cc}
}

func (c *userInfoServiceClient) GetUserById(ctx context.Context, in *GetUserByIdReq, opts ...grpc.CallOption) (*UserInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserInfoReply)
	err := c.cc.Invoke(ctx, UserInfoService_GetUserById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userInfoServiceClient) GetUserByPoliceNo(ctx context.Context, in *GetUserByPoliceNoReq, opts ...grpc.CallOption) (*UserInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserInfoReply)
	err := c.cc.Invoke(ctx, UserInfoService_GetUserByPoliceNo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserInfoServiceServer is the server API for UserInfoService service.
// All implementations must embed UnimplementedUserInfoServiceServer
// for forward compatibility.
//
// 用户信息服务
type UserInfoServiceServer interface {
	// 根据用户ID查询用户信息
	GetUserById(context.Context, *GetUserByIdReq) (*UserInfoReply, error)
	// 根据警号查询用户信息
	GetUserByPoliceNo(context.Context, *GetUserByPoliceNoReq) (*UserInfoReply, error)
	mustEmbedUnimplementedUserInfoServiceServer()
}

// UnimplementedUserInfoServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserInfoServiceServer struct{}

func (UnimplementedUserInfoServiceServer) GetUserById(context.Context, *GetUserByIdReq) (*UserInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserById not implemented")
}
func (UnimplementedUserInfoServiceServer) GetUserByPoliceNo(context.Context, *GetUserByPoliceNoReq) (*UserInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserByPoliceNo not implemented")
}
func (UnimplementedUserInfoServiceServer) mustEmbedUnimplementedUserInfoServiceServer() {}
func (UnimplementedUserInfoServiceServer) testEmbeddedByValue()                         {}

// UnsafeUserInfoServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserInfoServiceServer will
// result in compilation errors.
type UnsafeUserInfoServiceServer interface {
	mustEmbedUnimplementedUserInfoServiceServer()
}

func RegisterUserInfoServiceServer(s grpc.ServiceRegistrar, srv UserInfoServiceServer) {
	// If the following call pancis, it indicates UnimplementedUserInfoServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserInfoService_ServiceDesc, srv)
}

func _UserInfoService_GetUserById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserInfoServiceServer).GetUserById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserInfoService_GetUserById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserInfoServiceServer).GetUserById(ctx, req.(*GetUserByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserInfoService_GetUserByPoliceNo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserByPoliceNoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserInfoServiceServer).GetUserByPoliceNo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserInfoService_GetUserByPoliceNo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserInfoServiceServer).GetUserByPoliceNo(ctx, req.(*GetUserByPoliceNoReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UserInfoService_ServiceDesc is the grpc.ServiceDesc for UserInfoService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserInfoService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "user.UserInfoService",
	HandlerType: (*UserInfoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserById",
			Handler:    _UserInfoService_GetUserById_Handler,
		},
		{
			MethodName: "GetUserByPoliceNo",
			Handler:    _UserInfoService_GetUserByPoliceNo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user.proto",
}
