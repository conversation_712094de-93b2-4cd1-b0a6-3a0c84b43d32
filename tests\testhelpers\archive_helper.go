package testhelpers

import (
	"fmt"
	"math/rand"
	"net/http"
	"time"

	. "github.com/onsi/gomega"
	"gorm.io/gorm"

	commandTesthelpers "jxt-evidence-system/evidence-management/command/testhelpers"
	queryTesthelpers "jxt-evidence-system/evidence-management/query/testhelpers"
)

// ArchiveTestHelper 档案测试助手
type ArchiveTestHelper struct {
	dbCommand *gorm.DB
	dbQuery   *gorm.DB
	suite     *TestSuite
}

// NewArchiveTestHelper 创建档案测试助手
func NewArchiveTestHelper(dbCommand, dbQuery *gorm.DB, suite *TestSuite) *ArchiveTestHelper {
	return &ArchiveTestHelper{
		dbCommand: dbCommand,
		dbQuery:   dbQuery,
		suite:     suite,
	}
}

// CreateTestArchive 创建测试档案
func (h *ArchiveTestHelper) CreateTestArchive(title string) (int64, error) {
	// 清理之前的测试数据，避免编码冲突
	h.CleanupTestData()

	// 使用极强的随机化避免编码冲突
	now := time.Now()
	// 组合多个时间元素和随机数
	pid := 1000 + rand.Intn(9000) // 进程ID模拟
	tid := 100 + rand.Intn(900)   // 线程ID模拟
	seq := now.UnixNano() % 1000000
	randomSuffix := fmt.Sprintf("%d_%d_%d_%d", pid, tid, seq, now.Nanosecond()%1000000)
	uniqueTitle := fmt.Sprintf("%s_%s", title, randomSuffix)

	// 使用更大范围的档案类型来进一步避免编码冲突
	rand.Seed(now.UnixNano() + int64(pid) + int64(tid))
	orgID := 1                       // 固定使用组织ID 1
	archiveType := 1 + rand.Intn(20) // 使用1-20之间的档案类型，进一步增大范围

	createCommand := commandTesthelpers.NewCreateArchiveCommandBuilder().
		WithTitle(uniqueTitle).
		WithOrgID(orgID).
		WithType(archiveType).
		Build()

	resp, err := SendRequestWithAuth(h.suite.BaseURL, "POST", "/api/v1/archives", createCommand, h.suite.Token)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := GetResponseBody(resp)
		return 0, fmt.Errorf("创建档案失败，状态码: %d, 响应: %+v", resp.StatusCode, body)
	}

	// 检查响应体中的业务状态码
	body, err := GetResponseBody(resp)
	if err != nil {
		return 0, fmt.Errorf("解析响应体失败: %w", err)
	}

	if code, ok := body["code"].(float64); !ok || code != 200 {
		return 0, fmt.Errorf("创建档案业务失败，响应: %+v", body)
	}

	// 等待领域事件处理
	time.Sleep(200 * time.Millisecond)

	// 获取创建的档案ID - 使用uniqueTitle查询
	createdArchive := commandTesthelpers.NewArchiveBuilder().Build()
	result := h.dbCommand.Where("archive_title = ?", uniqueTitle).First(&createdArchive)
	if result.Error != nil {
		return 0, fmt.Errorf("查询创建的档案失败: %w", result.Error)
	}

	return createdArchive.ID, nil
}

// VerifyArchiveInCommandDB 验证档案在命令数据库中存在
func (h *ArchiveTestHelper) VerifyArchiveInCommandDB(archiveID int64) {
	var count int64
	archive := commandTesthelpers.NewArchiveBuilder().Build()
	h.dbCommand.Model(archive).Where("archive_id = ?", archiveID).Count(&count)
	Expect(count).To(Equal(int64(1)), "档案在命令数据库中不存在")
}

// VerifyArchiveInQueryDB 验证档案在查询数据库中存在
func (h *ArchiveTestHelper) VerifyArchiveInQueryDB(archiveID int64) {
	queryArchive := queryTesthelpers.NewArchiveReadModelBuilder().Build()
	err := h.dbQuery.Where("archive_id = ?", archiveID).First(&queryArchive).Error
	Expect(err).NotTo(HaveOccurred(), "档案在查询数据库中不存在")
}

// VerifyArchiveStatus 验证档案状态
func (h *ArchiveTestHelper) VerifyArchiveStatus(archiveID int64, expectedStatus int) {
	// 验证命令数据库
	commandArchive := commandTesthelpers.NewArchiveBuilder().Build()
	h.dbCommand.Where("archive_id = ?", archiveID).First(&commandArchive)
	Expect(commandArchive.Status).To(Equal(expectedStatus), "命令数据库中档案状态不匹配")

	// 验证查询数据库
	queryArchive := queryTesthelpers.NewArchiveReadModelBuilder().Build()
	err := h.dbQuery.Where("archive_id = ?", archiveID).First(&queryArchive).Error
	Expect(err).NotTo(HaveOccurred())
	Expect(queryArchive.Status).To(Equal(expectedStatus), "查询数据库中档案状态不匹配")
}

// VerifyArchiveTitle 验证档案标题
func (h *ArchiveTestHelper) VerifyArchiveTitle(archiveID int64, expectedTitle string) {
	// 验证命令数据库
	commandArchive := commandTesthelpers.NewArchiveBuilder().Build()
	h.dbCommand.Where("archive_id = ?", archiveID).First(&commandArchive)
	Expect(commandArchive.ArchiveTitle).To(Equal(expectedTitle), "命令数据库中档案标题不匹配")

	// 验证查询数据库
	queryArchive := queryTesthelpers.NewArchiveReadModelBuilder().Build()
	err := h.dbQuery.Where("archive_id = ?", archiveID).First(&queryArchive).Error
	Expect(err).NotTo(HaveOccurred())
	Expect(queryArchive.ArchiveTitle).To(Equal(expectedTitle), "查询数据库中档案标题不匹配")
}

// VerifyCreateArchiveResponse 验证创建档案响应
func (h *ArchiveTestHelper) VerifyCreateArchiveResponse(resp *http.Response) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("创建档案成功"))
}

// VerifyUpdateArchiveResponse 验证更新档案响应
func (h *ArchiveTestHelper) VerifyUpdateArchiveResponse(resp *http.Response) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("档案更新成功"))
}

// VerifyDeleteArchiveResponse 验证删除档案响应
func (h *ArchiveTestHelper) VerifyDeleteArchiveResponse(resp *http.Response) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("删除档案成功"))
}

// VerifyBatchUpdateArchiveResponse 验证批量更新档案响应
func (h *ArchiveTestHelper) VerifyBatchUpdateArchiveResponse(resp *http.Response) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("批量更新档案成功"))
}

// VerifyBatchDeleteArchiveResponse 验证批量删除档案响应
func (h *ArchiveTestHelper) VerifyBatchDeleteArchiveResponse(resp *http.Response) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("批量删除档案成功"))
}

// VerifyGetArchiveResponse 验证查询档案响应
func (h *ArchiveTestHelper) VerifyGetArchiveResponse(resp *http.Response) map[string]interface{} {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("查询成功"))

	return body["data"].(map[string]interface{})
}

// VerifyGetArchiveListResponse 验证查询档案列表响应
func (h *ArchiveTestHelper) VerifyGetArchiveListResponse(resp *http.Response) ([]interface{}, map[string]interface{}) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("查询成功"))

	data := body["data"].(map[string]interface{})
	list := data["list"].([]interface{})
	return list, data
}

// ArchiveData 档案数据结构
type ArchiveData struct {
	ArchiveTitle string
	ArchiveCode  string
	ArchiveType  int
	Description  string
}

// VerifyGetArchiveByIDResponse 验证根据ID查询档案响应
func (h *ArchiveTestHelper) VerifyGetArchiveByIDResponse(resp *http.Response) *ArchiveData {
	data := h.VerifyGetArchiveResponse(resp)

	// 转换为ArchiveData结构
	archive := &ArchiveData{}
	if archiveTitle, ok := data["archiveTitle"].(string); ok {
		archive.ArchiveTitle = archiveTitle
	}
	if archiveCode, ok := data["archiveCode"].(string); ok {
		archive.ArchiveCode = archiveCode
	}
	if archiveType, ok := data["archiveType"].(float64); ok {
		archive.ArchiveType = int(archiveType)
	}
	if description, ok := data["description"].(string); ok {
		archive.Description = description
	}

	return archive
}

// VerifyGetArchiveByCodeResponse 验证根据编码查询档案响应
func (h *ArchiveTestHelper) VerifyGetArchiveByCodeResponse(resp *http.Response) *ArchiveData {
	return h.VerifyGetArchiveByIDResponse(resp)
}

// VerifyErrorResponse 验证错误响应
func (h *ArchiveTestHelper) VerifyErrorResponse(resp *http.Response, expectedMessageContains string) {
	// 检查HTTP状态码应该是200（系统使用统一的HTTP 200 + 业务状态码模式）
	Expect(resp.StatusCode).To(Equal(http.StatusOK), "系统统一使用HTTP 200状态码")

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())

	// 检查业务错误码应该是400或500
	code, ok := body["code"].(float64)
	Expect(ok).To(BeTrue(), "响应中应该包含业务状态码")
	Expect(code).To(BeElementOf([]float64{400, 500}), fmt.Sprintf("业务状态码应该是400或500，实际是: %v", code))

	// 检查错误消息包含期望的内容
	msg, ok := body["msg"].(string)
	Expect(ok).To(BeTrue(), "响应消息应该是字符串")
	Expect(msg).To(ContainSubstring(expectedMessageContains), fmt.Sprintf("期望消息包含 '%s'，实际消息: '%s'", expectedMessageContains, msg))
}

// WaitForEventProcessing 等待事件处理
func (h *ArchiveTestHelper) WaitForEventProcessing() {
	time.Sleep(200 * time.Millisecond)
}

// CleanupTestData 清理测试数据
func (h *ArchiveTestHelper) CleanupTestData() {
	// 清理命令数据库中的测试档案
	h.dbCommand.Exec("DELETE FROM t_evidence_archives WHERE archive_title LIKE '%测试%' OR archive_title LIKE '%test%' OR archive_title LIKE '%Test%' OR archive_title LIKE '%档案%'")

	// 清理查询数据库中的测试档案
	h.dbQuery.Exec("DELETE FROM t_evidence_archives_read WHERE archive_title LIKE '%测试%' OR archive_title LIKE '%test%' OR archive_title LIKE '%Test%' OR archive_title LIKE '%档案%'")

	// 等待数据清理完成
	time.Sleep(100 * time.Millisecond)
}
