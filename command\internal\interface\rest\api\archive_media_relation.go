package api

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/jwtauth/user"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"jxt-evidence-system/evidence-management/command/internal/application/command"
	"jxt-evidence-system/evidence-management/command/internal/application/service/port"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/restapi"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

func init() {
	registrations = append(registrations, registerArchiveMediaRelationApiDependencies)
}

func registerArchiveMediaRelationApiDependencies() {
	err := di.Provide(func(usecase port.ArchiveMediaRelationService) *ArchiveMediaRelationHandler {
		return &ArchiveMediaRelationHandler{
			archiveMediaRelationService: usecase,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide ArchiveMediaRelationHandler: %v", err)
	}
}

type ArchiveMediaRelationHandler struct {
	restapi.RestApi
	archiveMediaRelationService port.ArchiveMediaRelationService
}

// @Summary 创建档案媒体关联
// @Description 创建新的档案媒体关联
// @Tags ArchiveMediaRelation
// @Accept json
// @Produce json
// @Param request body command.CreateArchiveMediaRelationCommand true "请求体"
// @Success 200 {object} command.CreateArchiveMediaRelationCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "创建档案媒体关联失败"
// @Router /archive-media-relations [post]
// @Example POST /archive-media-relations
// @ExampleRequest
//
//	{
//	  "archiveId": 123,
//	  "documentId": 1001,
//	  "relationType": "primary"
//	}
func (e ArchiveMediaRelationHandler) CreateArchiveMediaRelation(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	req := command.CreateArchiveMediaRelationCommand{}

	if err := c.ShouldBindJSON(&req); err != nil {
		e.GetLogger(c).Error("bind CreateArchiveMediaRelationCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("创建档案媒体关联失败，参数验证失败: %s", err.Error()))
		return
	}

	req.SetCreateBy(user.GetUserId(c))
	req.SetUpdateBy(user.GetUserId(c))

	if err := e.archiveMediaRelationService.CreateArchiveMediaRelation(ctx, &req); err != nil {
		e.GetLogger(c).Error("Create ArchiveMediaRelation failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("创建档案媒体关联失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("ArchiveMediaRelation created successfully",
		zap.Int64("archiveId", req.ArchiveId),
		zap.Int64("documentId", req.DocumentId))
	e.OK(c, gin.H{
		"archiveId":  req.ArchiveId,
		"documentId": req.DocumentId,
	}, "创建档案媒体关联成功")
}

// @Summary 删除档案媒体关联
// @Description 删除指定的档案媒体关联
// @Tags ArchiveMediaRelation
// @Accept json
// @Produce json
// @Param id path int true "关联ID"
// @Success 200 {object} int64 "成功"
// @Failure 400 {string} string "无效的ID参数"
// @Failure 500 {string} string "删除档案媒体关联失败"
// @Router /archive-media-relations/{id} [delete]
// @Example DELETE /archive-media-relations/1
func (e ArchiveMediaRelationHandler) DeleteArchiveMediaRelation(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	// 获取路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		e.GetLogger(c).Error("invalid id parameter", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, "无效的ID参数")
		return
	}

	if err := e.archiveMediaRelationService.DeleteArchiveMediaRelationByID(ctx, id); err != nil {
		e.GetLogger(c).Error("Delete ArchiveMediaRelation failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("删除档案媒体关联失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("ArchiveMediaRelation deleted successfully", zap.Int64("relationId", id))
	e.OK(c, id, "删除档案媒体关联成功")
}

// @Summary 批量创建档案媒体关联
// @Description 批量创建档案媒体关联
// @Tags ArchiveMediaRelation
// @Accept json
// @Produce json
// @Param request body command.BatchCreateArchiveMediaRelationCommand true "请求体"
// @Success 200 {object} command.BatchCreateArchiveMediaRelationCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "批量创建档案媒体关联失败"
// @Router /archive-media-relations/batch [post]
// @Example POST /archive-media-relations/batch
// @ExampleRequest
//
//	{
//	  "archiveId": 123,
//	  "documentIds": [1001, 1002, 1003],
//	  "relationType": "primary"
//	}
func (e ArchiveMediaRelationHandler) BatchCreateArchiveMediaRelation(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 60*time.Second)
	defer cancel()

	req := command.BatchCreateArchiveMediaRelationCommand{}

	if err := c.ShouldBindJSON(&req); err != nil {
		e.GetLogger(c).Error("bind BatchCreateArchiveMediaRelationCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("批量创建档案媒体关联失败，参数验证失败: %s", err.Error()))
		return
	}

	// 验证文档ID列表不能为空
	if len(req.DocumentIds) == 0 {
		e.GetLogger(c).Error("empty document ID list")
		e.Error(c, http.StatusBadRequest, nil, "批量创建档案媒体关联失败，参数验证失败: 文档ID列表不能为空")
		return
	}

	req.SetCreateBy(user.GetUserId(c))
	req.SetUpdateBy(user.GetUserId(c))

	if err := e.archiveMediaRelationService.BatchCreateArchiveMediaRelation(ctx, &req); err != nil {
		e.GetLogger(c).Error("BatchCreate ArchiveMediaRelation failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("批量创建档案媒体关联失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("ArchiveMediaRelation batch created successfully",
		zap.Int64("archiveId", req.ArchiveId),
		zap.Int64s("documentIds", req.GetDocumentIds()))
	e.OK(c, gin.H{
		"archiveId":   req.ArchiveId,
		"documentIds": req.GetDocumentIds(),
	}, "批量创建档案媒体关联成功")
}

// @Summary 批量删除档案媒体关联
// @Description 批量删除档案媒体关联
// @Tags ArchiveMediaRelation
// @Accept json
// @Produce json
// @Param request body command.BatchDeleteArchiveMediaRelationCommand true "请求体"
// @Success 200 {object} command.BatchDeleteArchiveMediaRelationCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "批量删除档案媒体关联失败"
// @Router /archive-media-relations/batch [delete]
// @Example DELETE /archive-media-relations/batch
// @ExampleRequest
//
//	{
//	  "ids": [1, 2, 3, 4, 5]
//	}
func (e ArchiveMediaRelationHandler) BatchDeleteArchiveMediaRelation(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 60*time.Second)
	defer cancel()

	req := command.BatchDeleteArchiveMediaRelationCommand{}

	if err := c.ShouldBindJSON(&req); err != nil {
		e.GetLogger(c).Error("bind BatchDeleteArchiveMediaRelationCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("批量删除档案媒体关联失败，参数验证失败: %s", err.Error()))
		return
	}

	// 验证ID列表不能为空
	if len(req.IDs) == 0 {
		e.GetLogger(c).Error("empty relation ID list")
		e.Error(c, http.StatusBadRequest, nil, "批量删除档案媒体关联失败，参数验证失败: 关联ID列表不能为空")
		return
	}

	req.SetUpdateBy(user.GetUserId(c))

	if err := e.archiveMediaRelationService.BatchDeleteArchiveMediaRelation(ctx, &req); err != nil {
		e.GetLogger(c).Error("BatchDelete ArchiveMediaRelation failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("批量删除档案媒体关联失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("ArchiveMediaRelation batch deleted successfully", zap.Int64s("relationIds", req.GetIds()))
	e.OK(c, req.GetIds(), "批量删除档案媒体关联成功")
}
