package persistence

import (
	"context"
	"fmt"
	"jxt-evidence-system/evidence-management/query/internal/models"
	"jxt-evidence-system/evidence-management/query/internal/models/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
	"gorm.io/gorm"
)

// 添加通过依赖注入创建repo
func init() {
	registrations = append(registrations, registerArchiveMediaRelationRepoDependencies)
}

// ArchiveMediaRelationReadRepository的依赖注入
func registerArchiveMediaRelationRepoDependencies() {
	if err := di.Provide(func() repository.ArchiveMediaRelationReadRepository {
		return &gormArchiveMediaRelationReadRepository{}
	}); err != nil {
		logger.Fatalf("failed to provide ArchiveMediaRelationReadRepository: %v", err)
	}
}

type gormArchiveMediaRelationReadRepository struct {
	GormRepository
}

// Create 创建档案媒体关联查询模型
func (r *gormArchiveMediaRelationReadRepository) Create(ctx context.Context, relation *models.ArchiveMediaRelationReadModel) error {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return err
	}
	if err := db.WithContext(ctx).Create(relation).Error; err != nil {
		return fmt.Errorf("failed to create ArchiveMediaRelationReadModel: %w", err)
	}
	return nil
}

// Update 更新档案媒体关联查询模型
func (r *gormArchiveMediaRelationReadRepository) Update(ctx context.Context, relation *models.ArchiveMediaRelationReadModel) error {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return err
	}
	if err := db.WithContext(ctx).Save(relation).Error; err != nil {
		return fmt.Errorf("failed to update ArchiveMediaRelationReadModel: %w", err)
	}
	return nil
}

// Delete 删除档案媒体关联查询模型
func (r *gormArchiveMediaRelationReadRepository) Delete(ctx context.Context, id int64) error {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return err
	}
	if err := db.WithContext(ctx).Delete(&models.ArchiveMediaRelationReadModel{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete ArchiveMediaRelationReadModel: %w", err)
	}
	return nil
}

// FindByID 根据ID查找档案媒体关联
func (r *gormArchiveMediaRelationReadRepository) FindByID(ctx context.Context, id int64) (*models.ArchiveMediaRelationReadModel, error) {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}
	var relation models.ArchiveMediaRelationReadModel
	if err := db.WithContext(ctx).Where("id = ?", id).First(&relation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to find ArchiveMediaRelationReadModel by ID: %w", err)
	}
	return &relation, nil
}

// FindByArchiveID 根据档案ID查找关联的媒体列表
func (r *gormArchiveMediaRelationReadRepository) FindByArchiveID(ctx context.Context, archiveId int64, page, pageSize int) (*models.ArchiveMediaRelationReadModelList, error) {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}

	var relations []models.ArchiveMediaRelationReadModel
	var total int64

	if err := db.WithContext(ctx).Model(&models.ArchiveMediaRelationReadModel{}).
		Where("archives_id = ?", archiveId).
		Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count relations by archive ID: %w", err)
	}

	offset := (page - 1) * pageSize
	if err := db.WithContext(ctx).
		Where("archives_id = ?", archiveId).
		Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&relations).Error; err != nil {
		return nil, fmt.Errorf("failed to find relations by archive ID: %w", err)
	}

	return &models.ArchiveMediaRelationReadModelList{
		List:  relations,
		Total: total,
	}, nil
}

// FindByMediaID 根据媒体ID查找关联的档案列表
func (r *gormArchiveMediaRelationReadRepository) FindByMediaID(ctx context.Context, mediaId int64, page, pageSize int) (*models.ArchiveMediaRelationReadModelList, error) {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}

	var relations []models.ArchiveMediaRelationReadModel
	var total int64

	if err := db.WithContext(ctx).Model(&models.ArchiveMediaRelationReadModel{}).
		Where("media_id = ?", mediaId).
		Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count relations by media ID: %w", err)
	}

	offset := (page - 1) * pageSize
	if err := db.WithContext(ctx).
		Where("media_id = ?", mediaId).
		Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&relations).Error; err != nil {
		return nil, fmt.Errorf("failed to find relations by media ID: %w", err)
	}

	return &models.ArchiveMediaRelationReadModelList{
		List:  relations,
		Total: total,
	}, nil
}

// FindByArchiveAndMedia 查找特定档案和媒体的关联关系
func (r *gormArchiveMediaRelationReadRepository) FindByArchiveAndMedia(ctx context.Context, archiveId, mediaId int64) (*models.ArchiveMediaRelationReadModel, error) {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}

	var relation models.ArchiveMediaRelationReadModel
	if err := db.WithContext(ctx).
		Where("archives_id = ? AND media_id = ?", archiveId, mediaId).
		First(&relation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to find relation by archive and media: %w", err)
	}
	return &relation, nil
}

// FindWithPagination 分页查询档案媒体关联列表
func (r *gormArchiveMediaRelationReadRepository) FindWithPagination(ctx context.Context, page, pageSize int, filters map[string]interface{}) (*models.ArchiveMediaRelationReadModelList, error) {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}

	var relations []models.ArchiveMediaRelationReadModel
	var total int64

	query := db.WithContext(ctx).Model(&models.ArchiveMediaRelationReadModel{})

	// 应用过滤条件
	for key, value := range filters {
		switch key {
		case "archive_id":
			query = query.Where("archives_id = ?", value)
		case "media_id":
			query = query.Where("media_id = ?", value)
		case "media_cate":
			query = query.Where("media_cate = ?", value)
		case "org_id":
			query = query.Where("org_id = ?", value)
		case "police_id":
			query = query.Where("police_id = ?", value)
		case "recorder_id":
			query = query.Where("recorder_id = ?", value)
		case "archive_type":
			query = query.Where("archive_type = ?", value)
		case "relation_type":
			query = query.Where("relation_type = ?", value)
		}
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count relations with filters: %w", err)
	}

	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&relations).Error; err != nil {
		return nil, fmt.Errorf("failed to find relations with pagination: %w", err)
	}

	return &models.ArchiveMediaRelationReadModelList{
		List:  relations,
		Total: total,
	}, nil
}

// CountByArchiveID 统计档案关联的媒体数量
func (r *gormArchiveMediaRelationReadRepository) CountByArchiveID(ctx context.Context, archiveId int64) (int64, error) {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return 0, err
	}

	var count int64
	if err := db.WithContext(ctx).Model(&models.ArchiveMediaRelationReadModel{}).
		Where("archives_id = ?", archiveId).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count relations by archive ID: %w", err)
	}
	return count, nil
}

// CountByMediaID 统计媒体关联的档案数量
func (r *gormArchiveMediaRelationReadRepository) CountByMediaID(ctx context.Context, mediaId int64) (int64, error) {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return 0, err
	}

	var count int64
	if err := db.WithContext(ctx).Model(&models.ArchiveMediaRelationReadModel{}).
		Where("media_id = ?", mediaId).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count relations by media ID: %w", err)
	}
	return count, nil
}

// GetArchiveMediaSummary 获取档案媒体关联汇总信息
func (r *gormArchiveMediaRelationReadRepository) GetArchiveMediaSummary(ctx context.Context, archiveId int64) (*models.ArchiveMediaRelationSummary, error) {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}

	var summary models.ArchiveMediaRelationSummary
	summary.ArchiveId = archiveId

	var stats []struct {
		MediaCate int   `json:"media_cate"`
		Count     int   `json:"count"`
		TotalSize int64 `json:"total_size"`
	}

	if err := db.WithContext(ctx).Model(&models.ArchiveMediaRelationReadModel{}).
		Select("media_cate, COUNT(*) as count, SUM(file_size) as total_size").
		Where("archives_id = ?", archiveId).
		Group("media_cate").
		Find(&stats).Error; err != nil {
		return nil, fmt.Errorf("failed to get archive media summary: %w", err)
	}

	for _, stat := range stats {
		summary.MediaCount += stat.Count
		summary.TotalSize += stat.TotalSize

		switch stat.MediaCate {
		case 0:
			summary.PhotoCount = stat.Count
		case 1:
			summary.AudioCount = stat.Count
		case 2:
			summary.VideoCount = stat.Count
		case 3:
			summary.LogCount = stat.Count
		}
	}

	return &summary, nil
}

// GetMediaArchiveSummary 获取媒体档案关联汇总信息
func (r *gormArchiveMediaRelationReadRepository) GetMediaArchiveSummary(ctx context.Context, mediaId int64) (*models.MediaArchiveRelationSummary, error) {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}

	var summary models.MediaArchiveRelationSummary
	summary.MediaId = mediaId

	var count int64
	if err := db.WithContext(ctx).Model(&models.ArchiveMediaRelationReadModel{}).
		Where("media_id = ?", mediaId).
		Count(&count).Error; err != nil {
		return nil, fmt.Errorf("failed to get media archive summary: %w", err)
	}

	summary.ArchiveCount = int(count)
	summary.IsMultiLinked = summary.ArchiveCount > 1

	return &summary, nil
}

// BatchCreate 批量创建档案媒体关联查询模型
func (r *gormArchiveMediaRelationReadRepository) BatchCreate(ctx context.Context, relations []*models.ArchiveMediaRelationReadModel) error {
	if len(relations) == 0 {
		return nil
	}

	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return err
	}

	if err := db.WithContext(ctx).CreateInBatches(relations, 100).Error; err != nil {
		return fmt.Errorf("failed to batch create ArchiveMediaRelationReadModels: %w", err)
	}
	return nil
}

// BatchDelete 批量删除档案媒体关联查询模型
func (r *gormArchiveMediaRelationReadRepository) BatchDelete(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}

	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return err
	}

	if err := db.WithContext(ctx).Where("id IN (?)", ids).Delete(&models.ArchiveMediaRelationReadModel{}).Error; err != nil {
		return fmt.Errorf("failed to batch delete ArchiveMediaRelationReadModels: %w", err)
	}
	return nil
}

// BatchDeleteByArchiveID 根据档案ID批量删除关联关系
func (r *gormArchiveMediaRelationReadRepository) BatchDeleteByArchiveID(ctx context.Context, archiveId int64) error {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return err
	}

	if err := db.WithContext(ctx).Where("archives_id = ?", archiveId).Delete(&models.ArchiveMediaRelationReadModel{}).Error; err != nil {
		return fmt.Errorf("failed to batch delete relations by archive ID: %w", err)
	}
	return nil
}

// BatchDeleteByMediaID 根据媒体ID批量删除关联关系
func (r *gormArchiveMediaRelationReadRepository) BatchDeleteByMediaID(ctx context.Context, mediaId int64) error {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return err
	}

	if err := db.WithContext(ctx).Where("media_id = ?", mediaId).Delete(&models.ArchiveMediaRelationReadModel{}).Error; err != nil {
		return fmt.Errorf("failed to batch delete relations by media ID: %w", err)
	}
	return nil
}

// FindMediaTypeStatsByArchive 根据档案ID统计不同媒体类型的数量
func (r *gormArchiveMediaRelationReadRepository) FindMediaTypeStatsByArchive(ctx context.Context, archiveId int64) (map[int]int, error) {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}

	var stats []struct {
		MediaCate int `json:"media_cate"`
		Count     int `json:"count"`
	}

	if err := db.WithContext(ctx).Model(&models.ArchiveMediaRelationReadModel{}).
		Select("media_cate, COUNT(*) as count").
		Where("archives_id = ?", archiveId).
		Group("media_cate").
		Find(&stats).Error; err != nil {
		return nil, fmt.Errorf("failed to find media type stats by archive: %w", err)
	}

	result := make(map[int]int)
	for _, stat := range stats {
		result[stat.MediaCate] = stat.Count
	}

	return result, nil
}

// FindRecentRelations 查找最近创建的关联关系
func (r *gormArchiveMediaRelationReadRepository) FindRecentRelations(ctx context.Context, limit int) ([]*models.ArchiveMediaRelationReadModel, error) {
	db, err := r.GetQueryOrm(ctx)
	if err != nil {
		return nil, err
	}

	var relations []*models.ArchiveMediaRelationReadModel

	if err := db.WithContext(ctx).
		Order("created_at DESC").
		Limit(limit).
		Find(&relations).Error; err != nil {
		return nil, fmt.Errorf("failed to find recent relations: %w", err)
	}

	return relations, nil
}
