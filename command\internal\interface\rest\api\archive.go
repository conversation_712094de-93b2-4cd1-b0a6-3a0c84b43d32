package api

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/jwtauth/user"
	_ "github.com/ChenBigdata421/jxt-core/sdk/pkg/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"jxt-evidence-system/evidence-management/command/internal/application/command"
	"jxt-evidence-system/evidence-management/command/internal/application/service/port"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/restapi"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

func init() {
	registrations = append(registrations, registerArchiveApiDependencies)
}

func registerArchiveApiDependencies() {
	err := di.Provide(func(usecase port.ArchiveService) *ArchiveHandler {
		return &ArchiveHandler{
			archiveService: usecase,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide ArchiveHandler: %v", err)
	}
}

type ArchiveHandler struct {
	restapi.RestApi
	archiveService port.ArchiveService
}

// @Summary 创建档案
// @Description 创建新的档案
// @Tags Archive
// @Accept json
// @Produce json
// @Param request body command.CreateArchiveCommand true "请求体"
// @Success 200 {object} command.CreateArchiveCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "创建档案失败"
// @Router /archive [post]
// @Example POST /archive
// @ExampleRequest
//
//	{
//	  "archiveTitle": "重要案件档案",
//	  "archiveType": 1,
//	  "description": "某重要案件的相关档案材料",
//	  "orgId": 1,
//	  "storageDuration": 120,
//	  "remarks": "需要长期保存"
//	}
func (e ArchiveHandler) CreateArchive(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	req := command.CreateArchiveCommand{}

	if err := c.ShouldBindJSON(&req); err != nil {
		e.GetLogger(c).Error("bind CreateArchiveCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("创建档案失败，参数验证失败: %s", err.Error()))
		return
	}

	req.SetCreateBy(user.GetUserId(c))
	if err := e.archiveService.CreateArchive(ctx, &req); err != nil {
		e.GetLogger(c).Error("Create Archive failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("创建档案失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Archive created successfully", zap.String("archiveTitle", req.ArchiveTitle))
	e.OK(c, req.ArchiveTitle, "创建档案成功")
}

// @Summary 更新档案
// @Description 更新指定的档案
// @Tags Archive
// @Accept json
// @Produce json
// @Param id path int true "档案ID"
// @Param request body command.UpdateArchiveCommand true "请求体"
// @Success 200 {object} command.UpdateArchiveCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "更新档案失败"
// @Router /archive/{id} [put]
// @Example PUT /archive/1
// @ExampleRequest
//
//	{
//	  "archiveTitle": "更新后的档案标题",
//	  "archiveType": 2,
//	  "description": "更新后的档案描述",
//	  "storageDuration": 180,
//	  "remarks": "修改备注信息"
//	}
func (e ArchiveHandler) UpdateArchiveByID(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	command := command.UpdateArchiveCommand{}

	// Binding URI and JSON together
	if err := c.ShouldBindUri(&command); err != nil {
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("更新档案失败，参数验证失败: %s", err.Error()))
		return
	}

	// 保留command.ID的值，因为会在ShouldBindJSON后被覆盖
	commandID := command.GetId()

	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind UpdateArchiveCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("更新档案失败，参数验证失败: %s", err.Error()))
		return
	}
	// 将保留的commandID赋值给command
	command.SetId(commandID)
	command.SetUpdateBy(user.GetUserId(c))
	if err := e.archiveService.UpdateArchiveByID(ctx, &command); err != nil {
		e.GetLogger(c).Error("Update Archive failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("更新档案失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Archive updated successfully", zap.Any("archiveId", command.GetId()))
	e.OK(c, command.GetId(), "档案更新成功")
}

// @Summary 批量更新档案
// @Description 批量更新档案信息
// @Tags Archive
// @Accept json
// @Produce json
// @Param request body command.BatchUpdateArchiveCommand true "请求体"
// @Success 200 {object} command.BatchUpdateArchiveCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "批量更新档案失败"
// @Router /archive/batch/update [post]
// @Example POST /archive/batch/update
// @ExampleRequest
//
//	{
//	  "ids": [1, 2, 3],
//	  "archiveType": 2,
//	  "storageDuration": 240,
//	  "status": 0,
//	  "remarks": "批量更新备注"
//	}
func (e ArchiveHandler) BatchUpdateArchive(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	command := command.BatchUpdateArchiveCommand{}

	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind BatchUpdateArchiveCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("批量更新档案失败，参数验证失败: %s", err.Error()))
		return
	}

	// 验证ID列表不能为空
	if len(command.IDs) == 0 {
		e.GetLogger(c).Error("empty archive ID list")
		e.Error(c, http.StatusBadRequest, nil, "批量更新档案失败，参数验证失败: 档案ID列表不能为空")
		return
	}

	command.SetUpdateBy(user.GetUserId(c))
	if err := e.archiveService.BatchUpdateArchive(ctx, &command); err != nil {
		e.GetLogger(c).Error("BatchUpdate Archive failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("批量更新档案失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Archive batch updated successfully", zap.Any("archiveIds", command.GetIds()))
	e.OK(c, command.GetIds(), "批量更新档案成功")
}

// @Summary 删除档案
// @Description 删除指定的档案
// @Tags Archive
// @Accept json
// @Produce json
// @Param id path int true "档案ID"
// @Success 200 {object} int64 "成功"
// @Failure 400 {string} string "无效的ID参数"
// @Failure 500 {string} string "删除档案失败"
// @Router /archive/{id} [delete]
// @Example DELETE /archive/1
func (e ArchiveHandler) DeleteArchiveByID(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	// 从URL路径获取ID
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		e.GetLogger(c).Error("invalid archive ID", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, "删除档案失败，ID必须是有效的数字")
		return
	}

	// 调用服务删除档案
	if err := e.archiveService.DeleteArchiveByID(ctx, id); err != nil {
		e.GetLogger(c).Error("Delete Archive failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("删除档案失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Archive deleted successfully", zap.Int64("archiveId", id))
	e.OK(c, id, "档案删除成功")
}

// @Summary 批量删除档案
// @Description 批量删除档案
// @Tags Archive
// @Accept json
// @Produce json
// @Param request body command.BatchDeleteArchiveCommand true "请求体"
// @Success 200 {object} command.BatchDeleteArchiveCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "批量删除档案失败"
// @Router /archive/batch/delete [post]
// @Example POST /archive/batch/delete
// @ExampleRequest
//
//	{
//	  "ids": [1, 2, 3]
//	}
func (e ArchiveHandler) BatchDeleteArchive(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	command := command.BatchDeleteArchiveCommand{}
	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind BatchDeleteArchiveCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("批量删除档案失败，参数验证失败: %s", err.Error()))
		return
	}

	// 验证ID列表不能为空
	if len(command.IDs) == 0 {
		e.GetLogger(c).Error("empty archive ID list")
		e.Error(c, http.StatusBadRequest, nil, "批量删除档案失败，参数验证失败: 档案ID列表不能为空")
		return
	}

	command.SetUpdateBy(user.GetUserId(c))
	if err := e.archiveService.BatchDeleteArchive(ctx, &command); err != nil {
		e.GetLogger(c).Error("BatchDelete Archive failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("批量删除档案失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Archive batch deleted successfully", zap.Any("archiveIds", command.GetIds()))
	e.OK(c, command.GetIds(), "批量删除档案成功")
}

// @Summary 批量更新档案状态
// @Description 批量更新指定档案的状态
// @Tags Archive
// @Accept json
// @Produce json
// @Param request body command.BatchUpdateArchiveStatusCommand true "请求体"
// @Success 200 {object} command.BatchUpdateArchiveStatusCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "批量更新档案状态失败"
// @Router /archive/batch/update-status [post]
// @Example POST /archive/batch/update-status
// @ExampleRequest
//
//	{
//	  "ids": [1, 2, 3],
//	  "status": 0
//	}
func (e ArchiveHandler) BatchUpdateArchiveStatus(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	command := command.BatchUpdateArchiveStatusCommand{}
	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind BatchUpdateArchiveStatusCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("批量更新档案状态失败，参数验证失败: %s", err.Error()))
		return
	}

	// 验证ID列表不能为空
	if len(command.IDs) == 0 {
		e.GetLogger(c).Error("empty archive ID list")
		e.Error(c, http.StatusBadRequest, nil, "批量更新档案状态失败，参数验证失败: 档案ID列表不能为空")
		return
	}

	command.SetUpdateBy(user.GetUserId(c))

	if err := e.archiveService.BatchUpdateArchiveStatus(ctx, &command); err != nil {
		e.GetLogger(c).Error("BatchUpdateArchiveStatus failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("批量更新档案状态失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Archive status updated successfully", zap.Any("archiveIds", command.GetIds()))
	e.OK(c, command.GetIds(), "批量更新档案状态成功")
}

// @Summary 批量更新档案过期时间
// @Description 批量更新指定档案的过期时间
// @Tags Archive
// @Accept json
// @Produce json
// @Param request body command.BatchUpdateArchiveExpirationCommand true "请求体"
// @Success 200 {object} command.BatchUpdateArchiveExpirationCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "批量更新档案过期时间失败"
// @Router /archive/batch/update-expiration [post]
// @Example POST /archive/batch/update-expiration
// @ExampleRequest
//
//	{
//	  "ids": [1, 2, 3],
//	  "expirationTime": "2025-12-31T23:59:59Z"
//	}
func (e ArchiveHandler) BatchUpdateArchiveExpiration(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	command := command.BatchUpdateArchiveExpirationCommand{}
	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind BatchUpdateArchiveExpirationCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("批量更新档案过期时间失败，参数验证失败: %s", err.Error()))
		return
	}

	// 验证ID列表不能为空
	if len(command.IDs) == 0 {
		e.GetLogger(c).Error("empty archive ID list")
		e.Error(c, http.StatusBadRequest, nil, "批量更新档案过期时间失败，参数验证失败: 档案ID列表不能为空")
		return
	}

	command.SetUpdateBy(user.GetUserId(c))

	if err := e.archiveService.BatchUpdateArchiveExpiration(ctx, &command); err != nil {
		e.GetLogger(c).Error("BatchUpdateArchiveExpiration failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("批量更新档案过期时间失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Archive expiration time updated successfully", zap.Any("archiveIds", command.GetIds()))
	e.OK(c, command.GetIds(), "批量更新档案过期时间成功")
}

// @Summary 批量更新档案保存期限
// @Description 批量更新指定档案的保存期限
// @Tags Archive
// @Accept json
// @Produce json
// @Param request body command.BatchUpdateArchiveStorageDurationCommand true "请求体"
// @Success 200 {object} command.BatchUpdateArchiveStorageDurationCommand "成功"
// @Failure 400 {string} string "无效的请求体"
// @Failure 500 {string} string "批量更新档案保存期限失败"
// @Router /archive/batch/update-storage-duration [post]
// @Example POST /archive/batch/update-storage-duration
// @ExampleRequest
//
//	{
//	  "ids": [1, 2, 3],
//	  "storageDuration": 360
//	}
func (e ArchiveHandler) BatchUpdateArchiveStorageDuration(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	command := command.BatchUpdateArchiveStorageDurationCommand{}
	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind BatchUpdateArchiveStorageDurationCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("批量更新档案保存期限失败，参数验证失败: %s", err.Error()))
		return
	}

	// 验证ID列表不能为空
	if len(command.IDs) == 0 {
		e.GetLogger(c).Error("empty archive ID list")
		e.Error(c, http.StatusBadRequest, nil, "批量更新档案保存期限失败，参数验证失败: 档案ID列表不能为空")
		return
	}

	command.SetUpdateBy(user.GetUserId(c))

	if err := e.archiveService.BatchUpdateArchiveStorageDuration(ctx, &command); err != nil {
		e.GetLogger(c).Error("BatchUpdateArchiveStorageDuration failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("批量更新档案保存期限失败! %s", err.Error()))
		return
	}

	e.GetLogger(c).Info("Archive storage duration updated successfully", zap.Any("archiveIds", command.GetIds()))
	e.OK(c, command.GetIds(), "批量更新档案保存期限成功")
}
