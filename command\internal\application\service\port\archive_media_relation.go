package port

import (
	"context"
	"jxt-evidence-system/evidence-management/command/internal/application/command"
)

// ArchiveMediaRelationService 档案媒体关联服务接口
type ArchiveMediaRelationService interface {
	// 创建档案媒体关联
	CreateArchiveMediaRelation(ctx context.Context, r *command.CreateArchiveMediaRelationCommand) error

	// 删除档案媒体关联
	DeleteArchiveMediaRelationByID(ctx context.Context, id int64) error

	// 批量创建档案媒体关联
	BatchCreateArchiveMediaRelation(ctx context.Context, r *command.BatchCreateArchiveMediaRelationCommand) error

	// 批量删除档案媒体关联
	BatchDeleteArchiveMediaRelation(ctx context.Context, r *command.BatchDeleteArchiveMediaRelationCommand) error
}
