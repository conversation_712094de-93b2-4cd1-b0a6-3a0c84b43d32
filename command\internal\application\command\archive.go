package command

import (
	"time"

	common "jxt-evidence-system/evidence-management/shared/common/models"
)

// CreateArchiveCommand 创建档案命令
type CreateArchiveCommand struct {
	ArchiveTitle    string `json:"archiveTitle" binding:"required"`      // 档案标题，必填
	ArchiveType     int    `json:"archiveType" binding:"required,min=0"` // 档案类型，必填
	Description     string `json:"description"`                          // 档案描述
	OrgID           int    `json:"orgId" binding:"required"`             // 管理部门ID，必填
	StorageDuration int    `json:"storageDuration" binding:"min=1"`      // 保存期限(月)，必须大于0
	Remarks         string `json:"remarks"`                              // 备注信息
	common.ControlBy
}

// UpdateArchiveCommand 更新档案命令
type UpdateArchiveCommand struct {
	ID              int64      `uri:"id"`                                         // 档案ID
	ArchiveTitle    *string    `json:"archiveTitle"`                              // 档案标题
	ArchiveType     *int       `json:"archiveType" binding:"omitempty,min=0"`     // 档案类型
	Description     *string    `json:"description"`                               // 档案描述
	OrgID           *int       `json:"orgId"`                                     // 管理部门ID
	StorageDuration *int       `json:"storageDuration" binding:"omitempty,min=1"` // 保存期限(月)
	ExpirationTime  *time.Time `json:"expirationTime"`                            // 过期时间
	Status          *int       `json:"status" binding:"omitempty,oneof=0 1 2"`    // 档案状态(0:正常,1:删除,2:其它)
	Remarks         *string    `json:"remarks"`                                   // 备注信息
	common.ControlBy
}

func (s *UpdateArchiveCommand) GetId() int64 {
	return s.ID
}

func (s *UpdateArchiveCommand) SetId(id int64) {
	s.ID = id
}

// BatchUpdateArchiveCommand 批量更新档案命令
type BatchUpdateArchiveCommand struct {
	IDs             []int64    `json:"ids" binding:"required"`                    // 档案ID列表，必填
	ArchiveType     *int       `json:"archiveType" binding:"omitempty,min=0"`     // 档案类型
	StorageDuration *int       `json:"storageDuration" binding:"omitempty,min=1"` // 保存期限(月)
	ExpirationTime  *time.Time `json:"expirationTime"`                            // 过期时间
	Status          *int       `json:"status" binding:"omitempty,oneof=0 1 2"`    // 档案状态
	Remarks         *string    `json:"remarks"`                                   // 备注信息
	common.ControlBy
}

func (s *BatchUpdateArchiveCommand) GetIds() []int64 {
	return s.IDs
}

// BatchDeleteArchiveCommand 批量删除档案命令
type BatchDeleteArchiveCommand struct {
	IDs []int64 `json:"ids" binding:"required"` // 档案ID列表，必填
	common.ControlBy
}

func (s *BatchDeleteArchiveCommand) GetIds() []int64 {
	return s.IDs
}

// BatchUpdateArchiveStatusCommand 批量更新档案状态命令
type BatchUpdateArchiveStatusCommand struct {
	IDs    []int64 `json:"ids" binding:"required"`       // 档案ID列表，必填
	Status int     `json:"status" binding:"oneof=0 1 2"` // 档案状态(0:正常,1:删除,2:其它)
	common.ControlBy
}

func (s *BatchUpdateArchiveStatusCommand) GetIds() []int64 {
	return s.IDs
}

// BatchUpdateArchiveExpirationCommand 批量更新档案过期时间命令
type BatchUpdateArchiveExpirationCommand struct {
	IDs            []int64   `json:"ids" binding:"required"`            // 档案ID列表，必填
	ExpirationTime time.Time `json:"expirationTime" binding:"required"` // 过期时间，必填
	common.ControlBy
}

func (s *BatchUpdateArchiveExpirationCommand) GetIds() []int64 {
	return s.IDs
}

// BatchUpdateArchiveStorageDurationCommand 批量更新档案保存期限命令
type BatchUpdateArchiveStorageDurationCommand struct {
	IDs             []int64 `json:"ids" binding:"required"`                   // 档案ID列表，必填
	StorageDuration int     `json:"storageDuration" binding:"required,min=1"` // 保存期限(月)，必填且大于0
	common.ControlBy
}

func (s *BatchUpdateArchiveStorageDurationCommand) GetIds() []int64 {
	return s.IDs
}
