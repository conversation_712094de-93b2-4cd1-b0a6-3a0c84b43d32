package models

import (
	"time"
)

// MediaReadModel 媒体查询模型
type MediaReadModel struct {
	ID int64 `json:"mediaId" gorm:"primaryKey;column:media_id;autoIncrement:false;comment:媒体ID"`

	// 业务元数据
	MediaName         string    `json:"mediaName" gorm:"size:128;column:media_name;comment:媒体名称"`
	MediaCate         int       `json:"mediaCate" gorm:"size:4;column:media_cate;comment:媒体类型(0: 照片 1: 音频 2: 视频 3:日志）"`
	MediaSuffix       string    `json:"mediaSuffix" gorm:"size:16;column:media_suffix;comment:媒体后缀"`
	ShotTimeStart     time.Time `json:"shotTimeStart" gorm:"column:shot_time_start;comment:拍摄开始时间"`
	ShotTime          time.Time `json:"shotTime" gorm:"column:shot_time;comment:拍摄时间"`
	VideoClarity      int       `json:"videoClarity" gorm:"column:video_clarity;comment:视频清晰度(0: 标清 1: 高清 2: 超清)"`
	VideoDuration     int       `json:"videoDuration" gorm:"column:video_duration;comment:视频时长（单位: 毫秒）"`
	ImportantLevel    int       `json:"importantLevel" gorm:"size:4;column:important_level;comment:重要级别(平台标记)"`
	ImportantLevelRec int       `json:"importantLevelRec" gorm:"size:4;column:important_level_rec;comment:重要级别(设备标记)"`
	Width             int       `json:"width" gorm:"column:width;comment:图片宽度"`
	Height            int       `json:"height" gorm:"column:height;comment:图片高度"`

	// 业务标注数据
	IsNonEnforcementMedia int    `json:"isNonEnforcementMedia" gorm:"size:4;column:is_non_enforcement_media;comment:是否是非执法媒体"`
	Comments              string `json:"comments" gorm:"size:255;column:comments;comment:标注内容"`
	Sequence              string `json:"sequence" gorm:"size:255;column:sequence;comment:视频序列标识"`
	EnforceType           int    `json:"enforceType" gorm:"size:4;column:enforce_type;comment:执法类型"`
	EenforeTypeNames      string `json:"eenforeTypeNames" gorm:"size:255;column:eenfore_type_names;comment:执法类型名称，多个用逗号隔开"`

	// 状态元数据
	IsLocked   int        `json:"isLocked" gorm:"size:4;column:is_locked;comment:是否锁定"`
	ExpiryTime *time.Time `json:"expiryTime" gorm:"column:expiry_time;default:NULL;comment:过期时间"`

	// 归档数据
	ArchiveID   int64      `json:"archiveId" gorm:"column:archive_id;comment:档案编号"`
	IsArchive   int        `json:"isArchive" gorm:"column:is_archive;comment:是否归档"`
	ArchiveDate *time.Time `json:"archiveDate" gorm:"column:archive_date;default:NULL;comment:归档时间"`

	// 文件元数据
	FileIdentity string `json:"fileIdentity" gorm:"size:255;column:file_identity;comment:文件标识"`
	FileName     string `json:"fileName" gorm:"size:255;column:file_name;comment:文件名称"`
	FileSize     int64  `json:"fileSize" gorm:"column:file_size;comment:文件大小(单位: KB)"`
	FileMd5      string `json:"fileMd5" gorm:"size:32;column:file_md5;comment:文件MD5"`
	FileType     int    `json:"fileType" gorm:"size:4;column:file_type;comment:文件类型"`
	ContentType  string `json:"contentType" gorm:"size:128;column:content_type;comment:文件MIME类型"`

	// 存储元数据
	URI        string `json:"uri" gorm:"size:255;column:uri;comment:存放uri"`
	Thumbnail  string `json:"thumbnail" gorm:"size:255;column:thumbnail;comment:Base64缩略图名称"`
	PlayUrl    string `json:"playUrl" gorm:"size:255;column:play_url;comment:MP4播放地址"`
	StoPlayUrl string `json:"stoPlayUrl" gorm:"size:255;column:sto_play_url;comment:存储MP4播放地址"`
	FlvUrl     string `json:"flvUrl" gorm:"size:255;column:flv_url;comment:FLV播放地址"`

	SiteID   int    `json:"siteId" gorm:"column:site_id;comment:采集站ID"`
	SiteNo   string `json:"siteNo" gorm:"size:255;column:site_no;comment:采集站编号"`
	SiteName string `json:"siteName" gorm:"size:255;column:site_name;comment:采集站名称"`
	SiteHttp string `json:"siteHttp" gorm:"size:255;column:site_http;comment:采集站HTTP地址"`

	StorageID    int    `json:"storageId" gorm:"column:storage_id;comment:存储服务器ID"`
	StorageIDStr string `json:"storageIdStr" gorm:"size:255;column:storage_id_str;comment:存储服务器ID字符串"`
	StorageHttp  string `json:"storageHttp" gorm:"size:255;column:storage_http;comment:存储服务器HTTP地址"`

	StorageType int `json:"storageType" gorm:"size:4;column:storage_type;comment:存储方式(0: 采集站  1: 存储)"`
	//DataSource      string `json:"dataSource" gorm:"size:255;column:data_source;comment:数据来源"`
	//StorageWay       string    `json:"storageWay" gorm:"size:255;column:storage_way;comment:存储方式"`
	IsSendToStorage int `json:"isSendToStorage" gorm:"column:is_send_to_storage;comment:是否上传至存储(-1: 文件不存在,0: 未上传,1: 已上传)"`
	IsNoticeSend    int `json:"isNoticeSend" gorm:"column:is_notice_send;comment:是否通知发送"`

	// 关联元数据
	PoliceID     int    `json:"policeId" gorm:"column:police_id;comment:警员ID"`
	PoliceNo     string `json:"policeNo" gorm:"size:255;column:police_no;comment:警员编号"`
	PoliceName   string `json:"policeName" gorm:"size:255;column:police_name;comment:警员姓名"`
	PoliceIdCard string `json:"policeIdCard" gorm:"size:255;column:police_id_card;comment:警员身份证号"`

	OrgID   int    `json:"orgId" gorm:"column:org_id;comment:单位/组织ID"`
	OrgCode string `json:"orgCode" gorm:"size:255;column:org_code;comment:单位/组织编码"`
	OrgName string `json:"orgName" gorm:"size:255;column:org_name;comment:单位/组织名称"`
	OrgJc   string `json:"orgJc" gorm:"size:255;column:org_jc;comment:单位/组织简称"`

	TerminalType int `json:"terminalType" gorm:"column:terminal_type;comment:终端类型(1: 执法仪 2: 采集站)"`

	RecorderID int    `json:"recorderId" gorm:"column:recorder_id;comment:执法仪ID"`
	RecorderNo string `json:"recorderNo" gorm:"size:255;column:recorder_no;comment:执法仪编号"`

	SiteClientID int `json:"siteClientId" gorm:"column:site_client_id;comment:采集客户端ID"`

	TrialID int `json:"trialId" gorm:"column:trial_id;comment:固定执法场所ID"`

	IncidentCode  string     `json:"IncidentCode" gorm:"size:255;column:incident_code;comment:警情号"`
	IsAssociated  int        `json:"isAssociated" gorm:"column:is_associated;comment:是否已经关联"`
	AssociateTime *time.Time `json:"associateTime" gorm:"column:associate_time;default:NULL;comment:关联时间"`

	// 传输校验数据
	RequestIdentity string `json:"requestIdentity" gorm:"size:255;column:request_identity;comment:请求标识"`
	AuthKey         string `json:"authKey" gorm:"size:255;column:auth_key;comment:认证码"`
	TraceCode       string `json:"traceCode" gorm:"size:255;column:trace_code;comment:追溯码"`

	ImportTime      *time.Time `json:"importTime" gorm:"column:import_time;default:NULL;comment:导入时间"`
	AcquisitionTime *time.Time `json:"acquisitionTime" gorm:"column:acquisition_time;default:NULL;comment:接收时间"`

	ControlBy
	ModelTime
}

// TableName 指定表名
func (*MediaReadModel) TableName() string {
	return "t_evidence_media_read"
}
