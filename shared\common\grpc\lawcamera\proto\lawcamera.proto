syntax = "proto3";

package lawcamera;

option go_package = ".;proto";

// 执法记录仪信息服务
service LawcameraInfoService {
  // 根据执法记录仪ID查询信息
  rpc GetLawcameraById(GetLawcameraByIdReq) returns (LawcameraInfoReply) {}
  // 根据执法记录仪编号查询信息
  rpc GetLawcameraByNo(GetLawcameraByNoReq) returns (LawcameraInfoReply) {}
  // 根据管理员ID查询执法记录仪列表
  rpc GetLawcamerasByManagerId(GetLawcamerasByManagerIdReq) returns (LawcameraListReply) {}
  // 根据领用人ID查询执法记录仪列表
  rpc GetLawcamerasByRequisitionerId(GetLawcamerasByRequisitionerIdReq) returns (LawcameraListReply) {}
}

// 根据执法记录仪ID查询请求
message GetLawcameraByIdReq {
  string tenant_id = 1;  // 租户ID，用于多租户系统
  int32 id = 2;         // 执法记录仪唯一ID
}

// 根据执法记录仪编号查询请求
message GetLawcameraByNoReq {
  string tenant_id = 1;  // 租户ID，用于多租户系统
  string no = 2;         // 执法记录仪编号
}

// 根据管理员ID查询执法记录仪列表请求
message GetLawcamerasByManagerIdReq {
  string tenant_id = 1;  // 租户ID，用于多租户系统
  int32 manager_id = 2;  // 管理员ID
}

// 根据领用人ID查询执法记录仪列表请求
message GetLawcamerasByRequisitionerIdReq {
  string tenant_id = 1;        // 租户ID，用于多租户系统
  int32 requisitioner_id = 2;  // 领用人ID
}

// 执法记录仪信息响应，字段与领域模型LawcameraModel对应
message LawcameraInfoReply {
  int32 id = 1;                              // 执法记录仪ID
  string no = 2;                             // 执法记录仪编号
  string name = 3;                           // 执法记录仪名称
  int32 manager_id = 4;                      // 管理员ID
  int32 manager_org_id = 5;                  // 管理员所在组织ID
  string manager_name = 6;                   // 管理员名称
  string manager_org_full_name = 7;          // 管理员所在组织全名称
  int32 enable_use = 8;                      // 是否能被使用：1:可用; 2:不可用
  int32 state = 9;                           // 状态：1:正常; 2:报废; 3:报修; 4:库存; 5:遗失
  string cpu = 10;                           // CPU
  int32 memory = 11;                         // 内存，单位：G
  int32 disk = 12;                           // 存储，单位：G
  string network_card = 13;                  // 网卡
  int32 usb_num = 14;                        // USB数量
  string system = 15;                        // 操作系统
  string version = 16;                       // 版本
  string buy_time = 17;                      // 购买时间
  string remark = 18;                        // 备注
  
  // 领用相关信息
  int32 use_state = 19;                      // 使用状态：1:使用中; 0:闲置
  int32 requisitioner_id = 20;               // 领用人ID
  string requisitioner_name = 21;            // 领用人名称
  int32 requisitioner_org_id = 22;           // 领用人组织ID
  string requisitioner_org_full_name = 23;   // 领用人组织全名称
  string requisition_start_time = 24;        // 领用开始时间
  string expected_return_date = 25;          // 预计归还时间
  
  string created_at = 26;                    // 创建时间
  string updated_at = 27;                    // 更新时间
}

// 执法记录仪列表响应
message LawcameraListReply {
  repeated LawcameraInfoReply lawcameras = 1; // 执法记录仪列表
  int64 total = 2;                           // 总数量
} 