package router

import (
	"github.com/gin-gonic/gin"
	jwt "github.com/ChenBigdata421/jxt-core/sdk/pkg/jwtauth"

	"go-admin/app/{{.PackageName}}/apis"
)

func init() {
	routerCheckRole = append(routerCheckRole, register{{.ClassName}}Router)
}

// register{{.ClassName}}Router
func register{{.ClassName}}Router(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	api := apis.{{.ClassName}}{}
	r := v1.Group("/{{.ModuleName}}").Use(authMiddleware.MiddlewareFunc())
	{
		r.GET("", api.GetPage)
		r.GET("/:id", api.Get)
		r.POST("", api.Insert)
		r.PUT("/:id", api.Update)
		r.DELETE("", api.Delete)
	}
}