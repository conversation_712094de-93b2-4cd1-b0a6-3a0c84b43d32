package api

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/jwtauth/user"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"jxt-evidence-system/evidence-management/command/internal/application/command"
	"jxt-evidence-system/evidence-management/command/internal/application/service/port"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/restapi"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

func init() {
	registrations = append(registrations, registerEnforcementTypeApiDependencies)
}

func registerEnforcementTypeApiDependencies() {
	err := di.Provide(func(service port.EnforcementTypeService) *EnforcementTypeHandler {
		return &EnforcementTypeHandler{
			enforcementTypeService: service,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide EnforcementTypeHandler: %v", err)
	}
}

type EnforcementTypeHandler struct {
	restapi.RestApi
	enforcementTypeService port.EnforcementTypeService
}

// Create 创建执法类型
// @Summary 创建执法类型
// @Description 创建执法类型
// @Tags 执法类型管理
// @Accept json
// @Produce json
// @Param data body dto.EnforcementTypeCreateCommand true "创建执法类型请求参数"
// @Success 200 {object} api.Response
// @Router /api/v1/enforce-types [post]
func (e *EnforcementTypeHandler) EnforcementTypeCreate(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	var command command.CreateEnforcementTypeCommand
	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind EnforcementTypeCreateCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("创建执法类型失败，参数验证失败: %s", err.Error()))
		return
	}

	command.CreateBy = user.GetUserId(c)
	err := e.enforcementTypeService.CreateEnforcementType(ctx, &command)
	if err != nil {
		e.GetLogger(c).Error("Create EnforcementType failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("创建执法类型失败: %s", err.Error()))
		return
	}

	e.OK(c, nil, "创建执法类型成功")
}

// Update 更新执法类型
// @Summary 更新执法类型
// @Description 更新执法类型
// @Tags 执法类型管理
// @Accept json
// @Produce json
// @Param id path int true "执法类型ID"
// @Param data body dto.EnforcementTypeUpdateCommand true "更新执法类型请求参数"
// @Success 200 {object} api.Response
// @Router /api/v1/enforce-types/{id} [put]
func (e *EnforcementTypeHandler) EnforcementTypeUpdate(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	var command command.UpdateEnforcementTypeCommand
	if err := c.ShouldBindJSON(&command); err != nil {
		e.GetLogger(c).Error("bind EnforcementTypeUpdateCommand err", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("更新执法类型失败，参数验证失败: %s", err.Error()))
		return
	}

	// 获取有符号64位整数id
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		e.GetLogger(c).Error("invalid id", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, "更新执法类型失败，无效的ID")
		return
	}
	command.ID = id
	command.UpdateBy = user.GetUserId(c) //服务端根据登录用户设置

	err = e.enforcementTypeService.UpdateEnforcementTypeByID(ctx, &command)
	if err != nil {
		e.GetLogger(c).Error("Update EnforcementType failed", zap.Error(err))

		// 根据错误类型返回不同的状态码
		switch err.Error() {
		case "执法类型不存在":
			e.Error(c, http.StatusNotFound, err, fmt.Sprintf("更新执法类型失败: %s", err.Error()))
		case "执法类型编码不可更改", "父节点ID不能设置为当前节点的ID", "父节点不能设置为当前节点的后代":
			e.Error(c, http.StatusBadRequest, err, fmt.Sprintf("更新执法类型失败: %s", err.Error()))
		default:
			e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("更新执法类型失败: %s", err.Error()))
		}
		return
	}

	e.OK(c, nil, "更新执法类型成功")
}

// Delete 删除执法类型
// @Summary 删除执法类型
// @Description 删除执法类型
// @Tags 执法类型管理
// @Accept json
// @Produce json
// @Param id path int true "执法类型ID"
// @Success 200 {object} api.Response
// @Router /api/v1/enforce-types/{id} [delete]
func (e *EnforcementTypeHandler) EnforcementTypeDelete(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 20*time.Second)
	defer cancel()

	// 获取有符号64位整数id
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		e.GetLogger(c).Error("invalid id", zap.Error(err))
		e.Error(c, http.StatusBadRequest, err, "无效的ID")
		return
	}

	err = e.enforcementTypeService.DeleteEnforcementTypeByID(ctx, id)
	if err != nil {
		e.GetLogger(c).Error("Delete EnforceType failed", zap.Error(err))
		e.Error(c, http.StatusInternalServerError, err, fmt.Sprintf("删除执法类型失败: %s", err.Error()))
		return
	}

	e.OK(c, nil, "删除执法类型成功")
}
