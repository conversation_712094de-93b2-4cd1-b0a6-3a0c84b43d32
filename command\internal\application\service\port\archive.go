package port

import (
	"context"
	"jxt-evidence-system/evidence-management/command/internal/application/command"
)

type ArchiveService interface {
	// 创建档案
	CreateArchive(ctx context.Context, r *command.CreateArchiveCommand) error

	// 修改单个档案
	UpdateArchiveByID(ctx context.Context, r *command.UpdateArchiveCommand) error

	// 删除单个档案
	DeleteArchiveByID(ctx context.Context, id int64) error

	// 批量修改档案
	BatchUpdateArchive(ctx context.Context, r *command.BatchUpdateArchiveCommand) error

	// 批量删除档案
	BatchDeleteArchive(ctx context.Context, r *command.BatchDeleteArchiveCommand) error

	// 批量更新档案状态
	BatchUpdateArchiveStatus(ctx context.Context, r *command.BatchUpdateArchiveStatusCommand) error

	// 批量更新档案过期时间
	BatchUpdateArchiveExpiration(ctx context.Context, r *command.BatchUpdateArchiveExpirationCommand) error

	// 批量更新档案保存期限
	BatchUpdateArchiveStorageDuration(ctx context.Context, r *command.BatchUpdateArchiveStorageDurationCommand) error
}
