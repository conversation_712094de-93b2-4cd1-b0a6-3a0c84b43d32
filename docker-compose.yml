services:
  mysql-evidence-command:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: evidence_command_db
    volumes:
      - mysql-evidence-command-data:/var/lib/mysql
    ports:
      - "3307:3306"
    networks:
      - security-management_jxt-web

  postgres-evidence-query:
    image: postgres:latest
    environment:
      POSTGRES_PASSWORD: 123456
      POSTGRES_USER: root
      POSTGRES_DB: evidencedb_query_db
    volumes:
      - postgres-evidence-query-data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - security-management_jxt-web

  kafka:
    image: bitnami/kafka:3.5.1
    hostname: kafka
    container_name: kafka
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      - KAFKA_CFG_NODE_ID=1
      - KAFKA_CFG_PROCESS_ROLES=broker,controller
      - K<PERSON><PERSON>_CFG_CONTROLLER_QUORUM_VOTERS=1@kafka:9093
      - <PERSON><PERSON><PERSON>_CFG_LISTENERS=PLAINTEXT://kafka:9092,CONTROLLER://kafka:9093,EXTERNAL://0.0.0.0:29092
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092,EXTERNAL://localhost:29092
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,EXTERNAL:PLAINTEXT
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=PLAINTEXT
      - ALLOW_PLAINTEXT_LISTENER=yes
      - KAFKA_KRAFT_CLUSTER_ID=MkU3OEVBNTcwNTJENDM2Qk
      - KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE=true
    volumes:
      - kafka-data:/bitnami/kafka
    networks:
      - security-management_jxt-web

  evidence-command:
    build:
      context: .
      dockerfile: /command/Dockerfile.command
    ports:
      - "8001:8001" 
    depends_on:
      - mysql-evidence-command
      - kafka
    environment:
      - DB_HOST=mysql-evidence-command
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=123456
      - DB_NAME=evidence_command_db
      - KAFKA_BROKER=kafka:29092
    volumes:  
      - evidence-command-config:/app/config  
      - evidence-command-log:/app/log
    # 将 settings.yml 配置文件复制到容器内的 /app/config 目录
    configs:
      - source: command-settings
        target: /app/config/settings.yml
    command: server -c /app/config/settings.yml
    networks:
      - security-management_jxt-web

  evidence-query:
    build:
      context: .
      dockerfile: /query/Dockerfile.query
    ports:
      - "8002:8002"
    depends_on:
      - postgres-evidence-query
      - kafka
    environment:
      - DB_HOST=postgres-evidence-query
      - DB_PORT=5432
      - DB_USER=root
      - DB_PASSWORD=123456
      - DB_NAME=evidencedb_query_db
      - KAFKA_BROKER=kafka:29092
    volumes:  
      - evidence-query-config:/app/config  
      - evidence-query-log:/app/log
    # 将 settings.yml 配置文件复制到容器内的 /app/config 目录
    configs:
      - source: query-settings
        target: /app/config/settings.yml
    command: server -c /app/config/settings.yml     
    networks:
      - security-management_jxt-web

volumes:
  mysql-evidence-command-data:
  postgres-evidence-query-data:
  kafka-data:
  evidence-command-config:
  evidence-command-log:
  evidence-query-config:
  evidence-query-log:

networks:
  security-management_jxt-web:
    external: true 

configs:
  command-settings:
    file: ./command/config/settings.yml
  query-settings:
    file: ./query/config/settings.yml