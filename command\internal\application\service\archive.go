package service

import (
	"context"
	"errors"
	"fmt"
	"jxt-evidence-system/evidence-management/command/internal/application/command"
	"jxt-evidence-system/evidence-management/command/internal/application/service/port"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archive"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archive/repository"
	"jxt-evidence-system/evidence-management/command/internal/domain/event/publisher"
	domain_service "jxt-evidence-system/evidence-management/command/internal/domain/service"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/eventbus"
	"jxt-evidence-system/evidence-management/shared/common/global"
	client "jxt-evidence-system/evidence-management/shared/common/grpc/client/port"
	"jxt-evidence-system/evidence-management/shared/common/service"
	domain_event "jxt-evidence-system/evidence-management/shared/domain/event"
	event_repository "jxt-evidence-system/evidence-management/shared/domain/event/repository"
	"strings"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"github.com/google/uuid"
	"go.uber.org/zap"
)

func init() {
	registrations = append(registrations, registerArchiveServiceDependencies)
}

func registerArchiveServiceDependencies() {
	err := di.Provide(func(repo repository.ArchiveRepository,
		eventPublisher publisher.EventPublisher,
		eventRepo event_repository.DomainEventRepository,
		archiveDomainService domain_service.ArchiveDomainService,
		orgInfoClient client.OrgInfoServiceClient) port.ArchiveService {
		return &archiveService{
			repo:                 repo,
			eventPublisher:       eventPublisher,
			eventRepo:            eventRepo,
			archiveDomainService: archiveDomainService,
			orgInfoClient:        orgInfoClient,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide ArchiveService: %v", err)
	}
}

type archiveService struct {
	service.Service
	repo                 repository.ArchiveRepository
	eventPublisher       publisher.EventPublisher
	eventRepo            event_repository.DomainEventRepository
	archiveDomainService domain_service.ArchiveDomainService
	orgInfoClient        client.OrgInfoServiceClient
}

// generateArchiveCode 生成档案编码 - 使用UUIDv7确保全局唯一性
func (s *archiveService) generateArchiveCode(ctx context.Context, orgID int, archiveType int) (string, error) {
	// 使用UUIDv7生成基础唯一标识符
	archiveUUID, err := uuid.NewV7()
	if err != nil {
		return "", fmt.Errorf("生成UUIDv7失败: %v", err)
	}

	// 构建档案编码：前缀 + 组织ID + 档案类型 + UUIDv7
	// 格式：AR{组织ID}T{档案类型}-{UUIDv7}
	// 示例：AR0001T01-01926b8e-3c4a-7890-abcd-ef1234567890
	archiveCode := fmt.Sprintf("AR%04dT%02d-%s", orgID, archiveType, archiveUUID.String())

	s.GetLogger(ctx).Info("生成档案编码",
		zap.String("archiveCode", archiveCode),
		zap.Int("orgID", orgID),
		zap.Int("archiveType", archiveType))

	return archiveCode, nil
}

// getOrgCode 获取组织编码（保留用于组织验证）
func (s *archiveService) getOrgCode(ctx context.Context, orgID int) (string, error) {
	// 从上下文中获取租户ID
	tenantID, ok := ctx.Value(global.TenantIDKey).(string)
	if !ok || tenantID == "" {
		// 测试环境降级处理：当无法获取租户ID时，直接使用组织ID生成编码
		s.GetLogger(ctx).Warn("无法从上下文中获取租户ID，使用降级处理", zap.Int("orgID", orgID))
		return fmt.Sprintf("%04d", orgID), nil
	}

	// 通过 gRPC 获取组织信息
	orgInfo, err := s.orgInfoClient.GetOrgById(ctx, tenantID, int32(orgID))
	if err != nil {
		s.GetLogger(ctx).Warn("查询组织信息失败，使用降级处理", zap.Error(err), zap.Int("orgID", orgID))
		return fmt.Sprintf("%04d", orgID), nil
	}
	if orgInfo == nil {
		s.GetLogger(ctx).Warn("组织不存在，使用降级处理", zap.Int("orgID", orgID))
		return fmt.Sprintf("%04d", orgID), nil
	}

	// 这里假设组织有编码字段，如果没有则使用组织ID
	if orgInfo.OrgCode != "" {
		return orgInfo.OrgCode, nil
	}
	return fmt.Sprintf("%04d", orgID), nil
}

// calculateExpirationTime 计算过期时间
func (s *archiveService) calculateExpirationTime(storageDuration int) *time.Time {
	if storageDuration <= 0 {
		return nil
	}
	expirationTime := time.Now().AddDate(0, storageDuration, 0)
	return &expirationTime
}

// createArchiveFromCommand 从 command 创建领域对象
// validateCreateArchiveCommand 验证创建档案命令的输入参数
func (s *archiveService) validateCreateArchiveCommand(cmd *command.CreateArchiveCommand) error {
	if cmd == nil {
		return fmt.Errorf("创建档案命令不能为空")
	}
	if strings.TrimSpace(cmd.ArchiveTitle) == "" {
		return fmt.Errorf("档案标题不能为空")
	}
	if len(cmd.ArchiveTitle) > 255 {
		return fmt.Errorf("档案标题长度不能超过255个字符")
	}
	if cmd.ArchiveType <= 0 {
		return fmt.Errorf("档案类型必须大于0")
	}
	if cmd.OrgID <= 0 {
		return fmt.Errorf("管理部门ID必须大于0")
	}
	if cmd.StorageDuration <= 0 {
		return fmt.Errorf("保存期限必须大于0")
	}
	if len(cmd.Description) > 1024 {
		return fmt.Errorf("档案描述长度不能超过1024个字符")
	}
	if len(cmd.Remarks) > 500 {
		return fmt.Errorf("备注长度不能超过500个字符")
	}
	return nil
}

// checkArchiveTitleDuplicate 检查档案标题重复性
func (s *archiveService) checkArchiveTitleDuplicate(ctx context.Context, title string, orgID int) error {
	// 这里可以添加数据库查询逻辑检查同组织内是否有相同标题的档案
	// 为了简化实现，这里只做基础验证
	if strings.TrimSpace(title) == "" {
		return fmt.Errorf("档案标题不能为空")
	}
	// TODO: 添加数据库查询检查重复逻辑
	return nil
}

// validateOrgExists 验证组织是否存在
func (s *archiveService) validateOrgExists(ctx context.Context, orgID int) error {
	if orgID <= 0 {
		return fmt.Errorf("无效的组织ID: %d", orgID)
	}

	// 通过GRPC客户端验证组织存在性
	_, err := s.getOrgCode(ctx, orgID)
	if err != nil {
		// 在getOrgCode中已经有降级处理，这里不再返回错误
		s.GetLogger(ctx).Warn("组织验证使用降级处理", zap.Error(err), zap.Int("orgID", orgID))
	}

	return nil
}

// validateUpdateArchiveCommand 验证更新档案命令的输入参数
func (s *archiveService) validateUpdateArchiveCommand(cmd *command.UpdateArchiveCommand) error {
	if cmd == nil {
		return fmt.Errorf("更新档案命令不能为空")
	}
	if cmd.ID <= 0 {
		return fmt.Errorf("档案ID必须大于0")
	}
	if cmd.ArchiveTitle != nil && strings.TrimSpace(*cmd.ArchiveTitle) == "" {
		return fmt.Errorf("档案标题不能为空")
	}
	if cmd.ArchiveTitle != nil && len(*cmd.ArchiveTitle) > 255 {
		return fmt.Errorf("档案标题长度不能超过255个字符")
	}
	if cmd.ArchiveType != nil && *cmd.ArchiveType <= 0 {
		return fmt.Errorf("档案类型必须大于0")
	}
	if cmd.OrgID != nil && *cmd.OrgID <= 0 {
		return fmt.Errorf("管理部门ID必须大于0")
	}
	if cmd.StorageDuration != nil && *cmd.StorageDuration <= 0 {
		return fmt.Errorf("保存期限必须大于0")
	}
	if cmd.Description != nil && len(*cmd.Description) > 1024 {
		return fmt.Errorf("档案描述长度不能超过1024个字符")
	}
	if cmd.Remarks != nil && len(*cmd.Remarks) > 500 {
		return fmt.Errorf("备注长度不能超过500个字符")
	}
	return nil
}

// validateBatchUpdateArchiveCommand 验证批量更新档案命令的输入参数
func (s *archiveService) validateBatchUpdateArchiveCommand(cmd *command.BatchUpdateArchiveCommand) error {
	if cmd == nil {
		return fmt.Errorf("批量更新档案命令不能为空")
	}
	if len(cmd.IDs) == 0 {
		return fmt.Errorf("档案ID列表不能为空")
	}
	if len(cmd.IDs) > 100 {
		return fmt.Errorf("批量更新档案数量不能超过100个")
	}
	for _, id := range cmd.IDs {
		if id <= 0 {
			return fmt.Errorf("档案ID必须大于0")
		}
	}
	if cmd.ArchiveType != nil && *cmd.ArchiveType <= 0 {
		return fmt.Errorf("档案类型必须大于0")
	}
	if cmd.StorageDuration != nil && *cmd.StorageDuration <= 0 {
		return fmt.Errorf("保存期限必须大于0")
	}
	if cmd.Remarks != nil && len(*cmd.Remarks) > 500 {
		return fmt.Errorf("备注长度不能超过500个字符")
	}
	return nil
}

func (s *archiveService) createArchiveFromCommand(ctx context.Context, command *command.CreateArchiveCommand) (*archive.Archive, error) {
	// 输入参数验证
	if err := s.validateCreateArchiveCommand(command); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	// 生成档案编码
	archiveCode, err := s.generateArchiveCode(ctx, command.OrgID, command.ArchiveType)
	if err != nil {
		return nil, fmt.Errorf("生成档案编码失败: %w", err)
	}

	// 计算过期时间
	expirationTime := s.calculateExpirationTime(command.StorageDuration)

	archive := &archive.Archive{
		ArchiveCode:     archiveCode,
		ArchiveTitle:    command.ArchiveTitle,
		ArchiveType:     command.ArchiveType,
		Description:     command.Description,
		OrgID:           command.OrgID,
		StorageDuration: command.StorageDuration,
		ExpirationTime:  expirationTime,
		Status:          0, // 默认状态为正常
		Remarks:         command.Remarks,
	}

	if command.CreateBy != 0 {
		archive.CreateBy = command.CreateBy
	}
	if command.UpdateBy != 0 {
		archive.UpdateBy = command.UpdateBy
	}

	return archive, nil
}

// CreateArchive 创建档案
func (s *archiveService) CreateArchive(ctx context.Context, command *command.CreateArchiveCommand) error {
	// 创建领域对象
	archiveEntity, err := s.createArchiveFromCommand(ctx, command)
	if err != nil {
		s.GetLogger(ctx).Error("创建档案领域对象失败", zap.Error(err))
		return fmt.Errorf("创建档案失败: %w", err)
	}

	// 检查档案编码是否已存在（数据一致性保证）
	existingArchive, err := s.repo.FindByCode(ctx, archiveEntity.ArchiveCode)
	if err != nil {
		s.GetLogger(ctx).Error("检查档案编码唯一性失败", zap.Error(err), zap.String("archiveCode", archiveEntity.ArchiveCode))
		return fmt.Errorf("检查档案编码失败: %w", err)
	}
	if existingArchive != nil {
		s.GetLogger(ctx).Warn("档案编码已存在", zap.String("archiveCode", archiveEntity.ArchiveCode))
		return fmt.Errorf("档案编码[%s]已经存在，请重新生成", archiveEntity.ArchiveCode)
	}

	// 验证组织存在性
	if err := s.validateOrgExists(ctx, command.OrgID); err != nil {
		s.GetLogger(ctx).Error("组织验证失败", zap.Error(err), zap.Int("orgID", command.OrgID))
		return fmt.Errorf("组织验证失败: %w", err)
	}

	// 保存到数据库
	err = s.repo.Create(ctx, archiveEntity)
	if err != nil {
		s.GetLogger(ctx).Error("数据库持久化失败", zap.Error(err))
		return fmt.Errorf("数据库持久化失败: %w", err)
	}

	// 创建领域事件（数据库写入成功后创建，确保有ID）
	err = archiveEntity.CreateArchiveAndSave()
	if err != nil {
		s.GetLogger(ctx).Error("创建档案领域事件失败", zap.Error(err))
		return fmt.Errorf("创建领域事件失败: %w", err)
	}

	// 发布事件（失败仅记录日志，不影响主流程）
	if err := s.publishEvents(ctx, archiveEntity); err != nil {
		s.GetLogger(ctx).Error("发布ArchiveCreatedEvent失败", zap.Error(err), zap.Int64("ArchiveId", archiveEntity.ID))
		// 事件发布失败不阻断操作，但需要记录
	}

	s.GetLogger(ctx).Info("档案创建成功", zap.Int64("archiveId", archiveEntity.ID), zap.String("archiveCode", archiveEntity.ArchiveCode))
	return nil
}

// UpdateArchiveByID 更新档案
func (s *archiveService) UpdateArchiveByID(ctx context.Context, command *command.UpdateArchiveCommand) error {
	// 输入参数验证
	if err := s.validateUpdateArchiveCommand(command); err != nil {
		s.GetLogger(ctx).Error("更新档案参数验证失败", zap.Error(err))
		return fmt.Errorf("参数验证失败: %w", err)
	}

	// 检查档案是否存在
	existingArchive, err := s.repo.FindByID(ctx, command.ID)
	if err != nil {
		s.GetLogger(ctx).Error("查询档案失败", zap.Error(err), zap.Int64("archiveId", command.ID))
		return fmt.Errorf("查询档案失败: %w", err)
	}
	if existingArchive == nil {
		return fmt.Errorf("待更新的档案[%d]不存在", command.GetId())
	}

	// 构建更新字段
	updates := make(map[string]interface{})

	if command.ArchiveTitle != nil {
		updates["ArchiveTitle"] = *command.ArchiveTitle
		existingArchive.ArchiveTitle = *command.ArchiveTitle
	}
	if command.ArchiveType != nil {
		updates["ArchiveType"] = *command.ArchiveType
		existingArchive.ArchiveType = *command.ArchiveType
	}
	if command.Description != nil {
		updates["Description"] = *command.Description
		existingArchive.Description = *command.Description
	}
	if command.OrgID != nil {
		// 验证新组织是否存在
		if err := s.validateOrgExists(ctx, *command.OrgID); err != nil {
			return fmt.Errorf("新组织验证失败: %w", err)
		}
		updates["OrgID"] = *command.OrgID
		existingArchive.OrgID = *command.OrgID
	}
	if command.StorageDuration != nil {
		updates["StorageDuration"] = *command.StorageDuration
		existingArchive.StorageDuration = *command.StorageDuration
		// 重新计算过期时间
		expirationTime := s.calculateExpirationTime(*command.StorageDuration)
		updates["ExpirationTime"] = expirationTime
		existingArchive.ExpirationTime = expirationTime
	}
	if command.ExpirationTime != nil {
		updates["ExpirationTime"] = *command.ExpirationTime
		existingArchive.ExpirationTime = command.ExpirationTime
	}
	if command.Status != nil {
		updates["Status"] = *command.Status
		existingArchive.Status = *command.Status
	}
	if command.Remarks != nil {
		updates["Remarks"] = *command.Remarks
		existingArchive.Remarks = *command.Remarks
	}
	if command.UpdateBy != 0 {
		updates["UpdateBy"] = command.UpdateBy
		existingArchive.UpdateBy = command.UpdateBy
	}

	updates["UpdatedAt"] = time.Now()

	// 使用聚合根方法判断是否可更新，并创建事件
	err = existingArchive.UpdateArchive(updates)
	if err != nil {
		s.GetLogger(ctx).Error("档案业务规则验证失败", zap.Error(err))
		return fmt.Errorf("更新档案业务验证失败: %w", err)
	}

	// 执行数据库更新
	err = s.repo.UpdateByID(ctx, existingArchive.ID, updates)
	if err != nil {
		s.GetLogger(ctx).Error("更新档案数据库操作失败", zap.Error(err))
		return fmt.Errorf("更新档案失败: %w", err)
	}

	// 发布事件（失败仅记录日志，不影响主流程）
	if err := s.publishEvents(ctx, existingArchive); err != nil {
		s.GetLogger(ctx).Error("发布ArchiveUpdatedEvent失败", zap.Error(err), zap.Int64("ArchiveId", existingArchive.ID))
		// 事件发布失败不阻断操作，但需要记录
	}

	s.GetLogger(ctx).Info("档案更新成功", zap.Int64("archiveId", command.ID))
	return nil
}

// DeleteArchiveByID 删除档案
func (s *archiveService) DeleteArchiveByID(ctx context.Context, id int64) error {
	// 输入参数验证
	if id <= 0 {
		s.GetLogger(ctx).Error("删除档案ID无效", zap.Int64("archiveId", id))
		return fmt.Errorf("档案ID必须大于0")
	}

	// 从数据库中获取完整的Archive记录
	existingArchive, err := s.repo.FindByID(ctx, id)
	if err != nil {
		s.GetLogger(ctx).Error("查询档案失败", zap.Error(err), zap.Int64("archiveId", id))
		return fmt.Errorf("查询档案失败: %w", err)
	}

	if existingArchive == nil {
		return fmt.Errorf("待删除的档案[%d]不存在", id)
	}

	// 使用聚合根方法判断是否可删除，并创建事件
	err = existingArchive.DeleteArchive()
	if err != nil {
		s.GetLogger(ctx).Error("档案删除业务规则验证失败", zap.Error(err))
		return fmt.Errorf("删除档案业务验证失败: %w", err)
	}

	// 执行数据库删除
	err = s.repo.DeleteByID(ctx, id)
	if err != nil {
		s.GetLogger(ctx).Error("删除档案数据库操作失败", zap.Error(err))
		return fmt.Errorf("删除档案失败: %w", err)
	}

	// 发布事件（失败仅记录日志，不影响主流程）
	if err := s.publishEvents(ctx, existingArchive); err != nil {
		s.GetLogger(ctx).Error("发布ArchiveDeletedEvent失败", zap.Error(err), zap.Int64("ArchiveId", id))
		// 事件发布失败不阻断操作，但需要记录
	}

	s.GetLogger(ctx).Info("档案删除成功", zap.Int64("archiveId", id))
	return nil
}

// BatchUpdateArchive 批量更新档案
func (s *archiveService) BatchUpdateArchive(ctx context.Context, command *command.BatchUpdateArchiveCommand) error {
	// 输入参数验证
	if err := s.validateBatchUpdateArchiveCommand(command); err != nil {
		s.GetLogger(ctx).Error("批量更新档案参数验证失败", zap.Error(err))
		return fmt.Errorf("参数验证失败: %w", err)
	}

	// 构建更新字段
	updates := make(map[string]interface{})

	if command.ArchiveType != nil {
		updates["ArchiveType"] = *command.ArchiveType
	}
	if command.StorageDuration != nil {
		updates["StorageDuration"] = *command.StorageDuration
		// 重新计算过期时间
		expirationTime := s.calculateExpirationTime(*command.StorageDuration)
		updates["ExpirationTime"] = expirationTime
	}
	if command.ExpirationTime != nil {
		updates["ExpirationTime"] = *command.ExpirationTime
	}
	if command.Status != nil {
		updates["Status"] = *command.Status
	}
	if command.Remarks != nil {
		updates["Remarks"] = *command.Remarks
	}
	if command.UpdateBy != 0 {
		updates["UpdateBy"] = command.UpdateBy
	}

	// 领域服务负责批量更新业务逻辑，比如判断档案是否可以更新等
	// 使用领域服务创建事件，并接收返回的新上下文
	newCtx, updateIds, err := s.archiveDomainService.BatchUpdateArchive(ctx, command.GetIds(), updates)
	if err != nil {
		s.GetLogger(ctx).Error("调用领域服务批量更新档案失败", zap.Error(err), zap.Int64s("ArchiveIds", command.IDs))
		return fmt.Errorf("批量更新档案业务验证失败: %w", err)
	}

	// 应用服务负责执行批量更新持久化操作
	rowsAffected, err := s.repo.BatchUpdateByIDs(newCtx, updateIds, updates)
	if err != nil {
		s.GetLogger(ctx).Error("数据库批量更新档案失败", zap.Error(err), zap.Int64s("UpdateIds", updateIds))
		return fmt.Errorf("批量更新档案失败: %w", err)
	}

	if rowsAffected == 0 {
		s.GetLogger(ctx).Warn("没有档案被更新", zap.Int64s("UpdateIds", updateIds))
		return fmt.Errorf("要更新的档案不存在")
	}

	if err := s.publishEventsFromContext(newCtx); err != nil {
		s.GetLogger(ctx).Error("发布ArchiveBatchUpdatedEvent失败", zap.Error(err), zap.Int64s("ArchiveIds", command.IDs))
		// 事件发布失败不阻断操作，但需要记录
	}

	s.GetLogger(ctx).Info("批量更新档案成功", zap.Int64s("archiveIds", command.IDs), zap.Int64("rowsAffected", rowsAffected))
	return nil
}

// BatchDeleteArchive 批量删除档案
func (s *archiveService) BatchDeleteArchive(ctx context.Context, command *command.BatchDeleteArchiveCommand) error {
	// 领域服务负责批量删除业务逻辑，比如判断档案是否可以删除等
	var deleteIds []int64
	var err error
	// 使用领域服务创建事件，并接收返回的新上下文
	ctx, deleteIds, err = s.archiveDomainService.BatchDeleteArchive(ctx, command.IDs, int(command.UpdateBy))
	if err != nil {
		s.GetLogger(ctx).Error("调用领域服务批量删除档案失败", zap.Error(err), zap.Int64s("ArchiveIds", command.IDs))
		return err
	}

	// gorm支持一次删除多条记录， 即使ID 都不存在时，也不会返回错误
	rowsAffected, err := s.repo.BatchDeleteByIDs(ctx, deleteIds)
	if err != nil {
		s.GetLogger(ctx).Error("数据库批量删除档案失败", zap.Error(err), zap.Int64s("DeleteIds", deleteIds))
		return err
	}

	if rowsAffected == 0 {
		s.GetLogger(ctx).Warn("没有档案被删除", zap.Int64s("DeleteIds", deleteIds))
		return errors.New("要删除的档案不存在")
	}

	// 发布事件
	if err := s.publishEventsFromContext(ctx); err != nil {
		s.GetLogger(ctx).Error("发布ArchiveBatchDeletedEvent失败!",
			zap.Error(err),
			zap.Int64s("ArchiveIds", command.IDs))
		return fmt.Errorf("发布批量删除事件失败: %v", err)
	}

	return nil
}

// BatchUpdateArchiveStatus 批量更新档案状态
func (s *archiveService) BatchUpdateArchiveStatus(ctx context.Context, command *command.BatchUpdateArchiveStatusCommand) error {
	// 创建更新字段映射,使用模型中的字段名
	updates := map[string]interface{}{
		"Status":    command.Status,
		"UpdateBy":  command.UpdateBy,
		"UpdatedAt": time.Now(),
	}

	// 领域服务负责批量更新档案状态业务逻辑，包括权限验证、状态检查等
	var err error
	newCtx, ids, err := s.archiveDomainService.BatchUpdateArchiveStatus(ctx, command.IDs, command.Status, command.UpdateBy, time.Now()) // 使用领域服务处理业务逻辑
	if err != nil {
		s.GetLogger(ctx).Error("调用领域服务批量更新档案状态失败", zap.Error(err), zap.Int64s("ArchiveIds", command.IDs))
		return err
	}

	// 批量更新所有通过验证的ID
	rowsAffected, err := s.repo.BatchUpdateByIDs(newCtx, ids, updates)
	if err != nil {
		s.GetLogger(ctx).Error("数据库批量更新档案状态失败", zap.Error(err), zap.Int64s("UpdateIds", ids))
		return fmt.Errorf("批量更新档案状态失败: %s", err)
	}

	if rowsAffected == 0 {
		s.GetLogger(ctx).Warn("没有档案被更新", zap.Int64s("UpdateIds", ids))
		return fmt.Errorf("没有找到需要更新的档案记录")
	}

	// 发布领域服务创建的事件
	if err := s.publishEventsFromContext(newCtx); err != nil {
		s.GetLogger(ctx).Error("发布ArchiveBatchStatusUpdatedEvent失败!", zap.Error(err), zap.Int64s("ArchiveIds", command.IDs))
		return fmt.Errorf("发布批量更新档案状态事件失败: %v", err)
	}

	// 检查是否有部分失败的情况，记录警告日志
	if partialErrors, ok := newCtx.Value("partialErrors").([]string); ok && len(partialErrors) > 0 {
		s.GetLogger(ctx).Warn("部分档案更新失败",
			zap.Strings("errors", partialErrors),
			zap.Int64s("successIds", ids),
			zap.Int64s("requestIds", command.IDs))
	}

	return nil
}

// BatchUpdateArchiveExpiration 批量更新档案过期时间
func (s *archiveService) BatchUpdateArchiveExpiration(ctx context.Context, command *command.BatchUpdateArchiveExpirationCommand) error {
	updates := map[string]interface{}{
		"ExpirationTime": command.ExpirationTime,
		"UpdateBy":       command.UpdateBy,
		"UpdatedAt":      time.Now(),
	}

	// 领域服务负责批量更新档案过期时间业务逻辑
	var err error
	newCtx, ids, err := s.archiveDomainService.BatchUpdateArchiveExpiration(ctx, command.IDs, command.ExpirationTime, command.UpdateBy, time.Now())
	if err != nil {
		s.GetLogger(ctx).Error("调用领域服务批量更新档案过期时间失败", zap.Error(err), zap.Int64s("ArchiveIds", command.IDs))
		return err
	}

	rowsAffected, err := s.repo.BatchUpdateByIDs(newCtx, ids, updates)
	if err != nil {
		s.GetLogger(ctx).Error("数据库批量更新档案过期时间失败", zap.Error(err), zap.Int64s("UpdateIds", ids))
		return fmt.Errorf("批量更新档案过期时间失败: %s", err)
	}

	if rowsAffected == 0 {
		s.GetLogger(ctx).Warn("没有档案被更新", zap.Int64s("UpdateIds", ids))
		return fmt.Errorf("没有找到需要更新的档案记录")
	}

	if err := s.publishEventsFromContext(newCtx); err != nil {
		s.GetLogger(ctx).Error("发布ArchiveBatchExpirationUpdatedEvent失败!", zap.Error(err), zap.Int64s("ArchiveIds", command.IDs))
		return fmt.Errorf("发布批量更新档案过期时间事件失败: %v", err)
	}

	return nil
}

// BatchUpdateArchiveStorageDuration 批量更新档案保存期限
func (s *archiveService) BatchUpdateArchiveStorageDuration(ctx context.Context, command *command.BatchUpdateArchiveStorageDurationCommand) error {
	// 重新计算过期时间
	expirationTime := s.calculateExpirationTime(command.StorageDuration)

	updates := map[string]interface{}{
		"StorageDuration": command.StorageDuration,
		"ExpirationTime":  expirationTime,
		"UpdateBy":        command.UpdateBy,
		"UpdatedAt":       time.Now(),
	}

	// 领域服务负责批量更新档案保存期限业务逻辑
	var err error
	newCtx, ids, err := s.archiveDomainService.BatchUpdateArchiveStorageDuration(ctx, command.IDs, command.StorageDuration, command.UpdateBy, time.Now())
	if err != nil {
		s.GetLogger(ctx).Error("调用领域服务批量更新档案保存期限失败", zap.Error(err), zap.Int64s("ArchiveIds", command.IDs))
		return err
	}

	rowsAffected, err := s.repo.BatchUpdateByIDs(newCtx, ids, updates)
	if err != nil {
		s.GetLogger(ctx).Error("数据库批量更新档案保存期限失败", zap.Error(err), zap.Int64s("UpdateIds", ids))
		return fmt.Errorf("批量更新档案保存期限失败: %s", err)
	}

	if rowsAffected == 0 {
		s.GetLogger(ctx).Warn("没有档案被更新", zap.Int64s("UpdateIds", ids))
		return fmt.Errorf("没有找到需要更新的档案记录")
	}

	if err := s.publishEventsFromContext(newCtx); err != nil {
		s.GetLogger(ctx).Error("发布ArchiveBatchStorageDurationUpdatedEvent失败!", zap.Error(err), zap.Int64s("ArchiveIds", command.IDs))
		return fmt.Errorf("发布批量更新档案保存期限事件失败: %v", err)
	}

	return nil
}

// publishEvents 发布事件
func (s *archiveService) publishEvents(ctx context.Context, archive *archive.Archive) error {
	for _, event := range archive.Events() {
		// 从上下文中获取租户ID
		tenantID := ctx.Value(global.TenantIDKey)
		if tenantID == nil {
			return fmt.Errorf("无法从上下文中获取租户ID")
		}
		event.SetTenantId(tenantID.(string))

		if err := s.eventPublisher.Publish(ctx, eventbus.ArchiveEventTopic, event); err != nil {
			// 发布失败要持久化领域事件
			if domainEvent, ok := event.(*domain_event.DomainEvent); ok {
				err = s.eventRepo.Save(ctx, domainEvent)
				if err != nil {
					return fmt.Errorf("领域事件数据库持久化失败! %s", err)
				}
			} else {
				return fmt.Errorf("event conversion failed! %+v", event)
			}

			return fmt.Errorf("发布事件失败: %v", err)
		}
	}
	archive.ClearEvents() // 发布成功，清空事件
	return nil
}

// publishEventsFromContext 从上下文中获取并发布领域事件
func (s *archiveService) publishEventsFromContext(ctx context.Context) error {
	// 从上下文中获取领域事件
	events := domain_event.GetEventsFromContext(ctx)
	if len(events) == 0 {
		s.GetLogger(ctx).Warn("上下文中没有领域事件，无需发布")
		return nil
	}

	// 从上下文中获取租户ID
	tenantID := ctx.Value(global.TenantIDKey)
	if tenantID == nil {
		return fmt.Errorf("无法从上下文中获取租户ID")
	}

	// 发布所有事件
	for _, event := range events {
		// 设置租户ID
		event.SetTenantId(tenantID.(string))

		if err := s.eventPublisher.Publish(ctx, eventbus.ArchiveEventTopic, event); err != nil {
			s.GetLogger(ctx).Error("发布领域事件失败，尝试写入数据库",
				zap.Error(err),
				zap.String("EventID", event.GetEventID()),
				zap.String("EventType", event.GetEventType()))

			// 发布失败需要持久化领域事件
			if domainEvent, ok := event.(*domain_event.DomainEvent); ok {
				err = s.eventRepo.Save(ctx, domainEvent)
				if err != nil {
					return fmt.Errorf("领域事件数据库持久化失败! %s", err)
				}
				// 保存成功，清空上下文中的事件
				domain_event.ClearEventsFromContext(ctx)
			} else {
				s.GetLogger(ctx).Error("事件类型转换失败", zap.Any("Event", event))
				return fmt.Errorf("event conversion failed! %+v", event)
			}
			return fmt.Errorf("发布事件失败: %v", err)
		}
	}

	// 发布成功，清空上下文中的事件
	domain_event.ClearEventsFromContext(ctx)

	return nil
}
