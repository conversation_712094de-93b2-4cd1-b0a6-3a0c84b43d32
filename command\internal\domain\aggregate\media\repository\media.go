package repository

import (
	"context"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/media"
)

// MediaRepository defines the interface for Media service
type MediaRepository interface {
	FindByID(ctx context.Context, id int64) (*media.Media, error)

	FindByName(ctx context.Context, name string) (*media.Media, error)

	Create(ctx context.Context, model *media.Media) error

	// 只更新特定字段，即使字段值为 0，"", false 也会更新，且这样更新效率更高
	UpdateByID(ctx context.Context, id int64, updates map[string]interface{}) error

	DeleteByID(ctx context.Context, id int64) error

	// 批量更新
	BatchUpdateByIDs(ctx context.Context, ids []int64, updates map[string]interface{}) (rowsAffected int64, err error)

	// 批量删除
	BatchDeleteByIDs(ctx context.Context, ids []int64) (rowsAffected int64, err error)
}
