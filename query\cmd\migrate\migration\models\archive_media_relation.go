package models

import (
	"jxt-evidence-system/evidence-management/shared/common/models"
	"time"
)

// ArchiveMediaRelationReadModel 档案媒体关联查询模型
type ArchiveMediaRelationReadModel struct {
	// === 核心关联字段（必须） ===
	ID        int64 `json:"id" gorm:"primaryKey;column:id;autoIncrement:false;comment:关联ID(和写数据库id相同)"`
	ArchiveId int64 `json:"archiveId" gorm:"column:archive_id;comment:档案ID"`
	MediaId   int64 `json:"mediaId" gorm:"column:media_id;comment:媒体ID"`

	// === 稳定的档案字段（冗余，用于反向查询） ===
	ArchiveCode  string `json:"archiveCode" gorm:"size:128;column:archive_code;comment:档案编号"`
	ArchiveTitle string `json:"archiveTitle" gorm:"size:255;column:archive_title;comment:档案标题"`
	ArchiveType  int    `json:"archiveType" gorm:"size:4;column:archive_type;comment:档案类型"`

	// === 稳定的媒体标识字段（冗余，用于列表显示） ===
	MediaName     string `json:"mediaName" gorm:"size:128;column:media_name;comment:媒体名称"`
	MediaCate     int    `json:"mediaCate" gorm:"size:4;column:media_cate;comment:媒体类型(0: 照片 1: 音频 2: 视频 3:日志）"`
	MediaSuffix   string `json:"mediaSuffix" gorm:"size:16;column:media_suffix;comment:媒体后缀"`
	FileSize      int64  `json:"fileSize" gorm:"column:file_size;comment:文件大小(单位: KB)"`
	VideoDuration int    `json:"videoDuration" gorm:"column:video_duration;comment:视频时长（单位: 毫秒）"`

	// === 稳定的时间信息 ===
	ShotTime      *time.Time `json:"shotTime" gorm:"column:shot_time;default:NULL;comment:拍摄时间"`
	ShotTimeStart *time.Time `json:"shotTimeStart" gorm:"column:shot_time_start;default:NULL;comment:拍摄开始时间"`
	ImportTime    *time.Time `json:"importTime" gorm:"column:import_time;default:NULL;comment:导入时间"`
	ExpiryTime    *time.Time `json:"expiryTime" gorm:"column:expiry_time;default:NULL;comment:过期时间"`

	// === 稳定的组织信息（冗余，用于列表显示） ===
	OrgID   int    `json:"orgId" gorm:"column:org_id;comment:组织ID"`
	OrgName string `json:"orgName" gorm:"size:255;column:org_name;comment:组织名称"`

	// === 稳定的警员信息（冗余，用于列表显示） ===
	PoliceID   int    `json:"policeId" gorm:"column:police_id;comment:警员ID"`
	PoliceName string `json:"policeName" gorm:"size:255;column:police_name;comment:警员姓名"`

	// === 稳定的设备信息（冗余，用于列表显示） ===
	RecorderID int    `json:"recorderId" gorm:"column:recorder_id;comment:执法仪ID"`
	RecorderNo string `json:"recorderNo" gorm:"size:255;column:recorder_no;comment:执法仪编号"`

	// === 关联业务信息 ===
	// 关联类型（扩展字段，为将来可能的需求预留）
	RelationType string `json:"relationType" gorm:"size:32;column:relation_type;comment:关联类型"`
	Remarks      string `json:"remarks" gorm:"size:512;column:remarks;comment:备注信息"`

	// === 审计字段 ===
	models.ControlBy
	models.ModelTime
}

// TableName 指定表名
func (*ArchiveMediaRelationReadModel) TableName() string {
	return "t_archive_media_relations_read"
}
