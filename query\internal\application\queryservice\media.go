package queryservice

import (
	"context"
	query "jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/application/queryservice/port"
	"jxt-evidence-system/evidence-management/query/internal/models"
	"jxt-evidence-system/evidence-management/query/internal/models/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"github.com/ChenBigdata421/jxt-core/sdk/service"
)

func init() {
	registrations = append(registrations, registerMediaServiceDependencies)
}

func registerMediaServiceDependencies() {
	err := di.Provide(func(repo repository.MediaReadModelRepository) port.MediaQuery {
		return &mediaQueryService{
			repo: repo,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide MediaService: %v", err)
	}
}

type mediaQueryService struct {
	service.Service
	repo repository.MediaReadModelRepository
}

// GetPage 获取列表
func (e *mediaQueryService) GetPage(ctx context.Context, r *query.MediaPagedQuery) (*[]models.MediaReadModel, int64, error) {
	list, count, err := e.repo.GetPage(ctx, r)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

// GetByID 根据ID获取对象
func (e *mediaQueryService) GetByID(ctx context.Context, id int64) (*models.MediaReadModel, error) {
	return e.repo.FindByID(ctx, id)
}
