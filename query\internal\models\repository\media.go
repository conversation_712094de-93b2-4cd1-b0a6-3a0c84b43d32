package repository

import (
	"context"
	"jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/models"
)

// MediaRepository defines the interface for Media service
type MediaReadModelRepository interface {

	// 分页查询
	GetPage(ctx context.Context, r *query.MediaPagedQuery) (list *[]models.MediaReadModel, count int64, err error)

	// 根据id查询
	FindByID(ctx context.Context, id int64) (*models.MediaReadModel, error)

	// 根据名称查询
	FindByName(ctx context.Context, name string) (*models.MediaReadModel, error)

	// Create 创建媒体读模型
	Create(ctx context.Context, model *models.MediaReadModel) error

	// 只更新特定字段，即使字段值为 0，"", false 也会更新，且这样更新效率更高
	UpdateByID(ctx context.Context, id int64, updates map[string]interface{}) error

	// 批量更新
	UpdateManyByIDs(ctx context.Context, ids []int64, updates map[string]interface{}) (rowsAffected int64, err error)

	// 根据id删除
	DeleteByID(ctx context.Context, id int64) error

	// 批量删除
	DeleteManyByIDs(ctx context.Context, ids []int64) (rowsAffected int64, err error)
}
