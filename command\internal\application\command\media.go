package command

import (
	"time"

	common "jxt-evidence-system/evidence-management/shared/common/models"
)

type UploadMediaCommand struct {
	MediaName        string     `json:"mediaName" binding:"required"`               //媒体名称不可省
	MediaCate        *int       `json:"mediaCate" binding:"required,oneof=0 1 2 3"` //媒体类型不可省（0：照片   1：音频   2：视频   3：日志）
	PoliceNo         string     `json:"policeNo" binding:"required"`                //警员编号不可省，6位数字
	RecorderNo       string     `json:"recorderNo" binding:"required"`              //执法仪编号不可省
	AuthKey          string     `json:"authKey" binding:"required"`                 //认证码不可省，MD5加密
	RequestIdentity  string     `json:"requestIdentity"`                            //请求标识
	ImportantLevel   int        `json:"importantLevel"`                             //重要级别
	MediaSuffix      string     `json:"mediaSuffix"`                                //媒体后缀
	PoliceName       string     `json:"policeName"`                                 //使用者姓名
	ShotTimeStart    time.Time  `json:"shotTimeStart"`                              //拍摄开始时间
	ShotTime         time.Time  `json:"shotTime"`                                   //拍摄结束时间
	VideoClarity     int        `json:"videoClarity"`                               //视频清晰度（0：标清 1：高清 2：超清）
	VideoDuration    int        `json:"videoDuration"`                              //音视频时长（单位：毫秒）
	FileSize         int64      `json:"fileSize"`                                   //文件大小，单位KB（强调：需要和发送端对齐文件大小的单位，确认是字节还是KB）
	OrgID            int        `json:"orgId"`                                      //单位编号
	OrgName          string     `json:"orgName"`                                    //单位名称
	StorageType      int        `json:"storageType"`                                //存储方式(0：采集站  1：存储)
	SiteID           int        `json:"siteId"`                                     //采集站编号
	StorageID        int        `json:"storageId"`                                  //存储服务器ID
	SiteClientID     int        `json:"siteClientId"`                               //采集客户端ID
	TrialID          int        `json:"trialId"`                                    //固定执法场所ID
	ImportTime       *time.Time `json:"importTime"`                                 //上传时间
	AcquisitionTime  *time.Time `json:"acquisitionTime"`                            //接收时间（ftpserver接收时间）
	Comments         string     `json:"comments"`                                   //标注类型
	IncidentRecordID string     `json:"incidentRecordId"`                           //警情记录ID，执法记录仪从二维码获得
	common.ControlBy
}

type UpdateMediaCommand struct {
	ID              int64      `uri:"id"`
	MediaCate       *int       `json:"mediaCate" binding:"omitempty,oneof=0 1 2 3"` //媒体类型（0：照片   1：音频   2：视频   3：日志）
	PoliceNo        *string    `json:"policeNo"`                                    //警员编号，6位数字
	RecordeNo       *string    `json:"recorderNo"`                                  //执法仪编号
	AuthKey         string     `json:"authKey"`                                     //认证码不可省，MD5加密
	RequestIdentity string     `json:"requestIdentity"`                             //请求标识
	ImportantLevel  *int       `json:"importantLevel"`                              //重要级别
	MediaSuffix     *string    `json:"mediaSuffix"`                                 //媒体后缀
	PoliceName      *string    `json:"policeName"`                                  //使用者姓名
	ShotTimeStart   *time.Time `json:"shotTimeStart"`                               //拍摄开始时间
	ShotTime        *time.Time `json:"shotTime"`                                    //拍摄结束时间
	VideoClarity    *int       `json:"videoClarity"`                                //视频清晰度（0：标清 1：高清 2：超清）
	VideoDuration   *int       `json:"videoDuration"`                               //音视频时长（单位：毫秒）
	FileSize        *int64     `json:"fileSize"`                                    //文件大小，单位KB（强调：需要和发送端对齐文件大小的单位，确认是字节还是KB）
	OrgID           *int       `json:"orgId"`                                       //单位编号
	OrgName         *string    `json:"orgName"`                                     //单位名称
	StorageType     *int       `json:"storageType"`                                 //存储方式(0：采集站  1：存储)
	SiteID          *int       `json:"siteId"`                                      //采集站编号
	StorageID       *int       `json:"storageId"`                                   //存储服务器ID
	SiteClientID    *int       `json:"siteClientId"`                                //采集客户端ID
	TrialID         *int       `json:"trialId"`                                     //固定执法场所ID
	//ImportTime      time.Time  `json:"importTime"`                                  //上传时间，不可更改
	Comments *string `json:"comments"` //标注类型
	common.ControlBy
}

func (s *UpdateMediaCommand) GetId() int64 {
	return s.ID
}

func (s *UpdateMediaCommand) SetId(id int64) {
	s.ID = id
}

type BatchUpdateMediaCommand struct {
	IDs            []int64    `json:"ids" binding:"required"`
	ImportantLevel *int       `json:"importantLevel"` //重要级别
	Comments       *string    `json:"comments"`       //标注类型，标注内容
	ExpiryTime     *time.Time `json:"expiryTime"`     //存储期限

	common.ControlBy
}

func (s *BatchUpdateMediaCommand) GetIds() []int64 {
	return s.IDs
}

type BatchDeleteMediaCommand struct {
	IDs []int64 `json:"ids"`
	common.ControlBy
}

func (s *BatchDeleteMediaCommand) GetIds() []int64 {
	return s.IDs
}

type BatchUpdateNonEnforcementStatusCommand struct {
	IDs                   []int64 `json:"ids" binding:"required"`
	IsNonEnforcementMedia int     `json:"isNonEnforcementMedia" binding:"oneof=0 1"`
	common.ControlBy
}

func (s *BatchUpdateNonEnforcementStatusCommand) GetIds() []int64 {
	return s.IDs
}

type BatchUpdateEnforceTypeCommand struct {
	IDs         []int64 `json:"ids" binding:"required"`
	EnforceType int     `json:"enforceType" binding:"required,min=0"`
	common.ControlBy
}

func (s *BatchUpdateEnforceTypeCommand) GetIds() []int64 {
	return s.IDs
}

type BatchUpdateIsLockedCommand struct {
	IDs      []int64 `json:"ids" binding:"required"`
	IsLocked int     `json:"isLocked" binding:"oneof=0 1"`
	common.ControlBy
}

func (s *BatchUpdateIsLockedCommand) GetIds() []int64 {
	return s.IDs
}
