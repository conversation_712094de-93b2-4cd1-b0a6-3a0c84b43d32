package testhelpers

import (
	//"go-admin/models/tools"
	//"os"
	"testing"
	//"text/template"
)

func TestGoModelTemplate(t *testing.T) {
	//t1, err := template.ParseFiles("model.go.template")
	//if err != nil {
	//	t.Error(err)
	//}
	//table := tools.SysTables{}
	//table.TBName = "sys_tables"
	//tab, err := table.Get()
	//if err != nil {
	//	t.Error(err)
	//}
	//file, err := os.Create("models/" + table.PackageName + ".go")
	//if err != nil {
	//	t.Error(err)
	//}
	//defer file.Close()
	//
	//_ = t1.Execute(file, tab)
	t.Log("")
}

func TestGoApiTemplate(t *testing.T) {
	//t1, err := template.ParseFiles("api.go.template")
	//if err != nil {
	//	t.Error(err)
	//}
	//table := tools.SysTables{}
	//table.TBName = "sys_tables"
	//tab, _ := table.Get()
	//file, err := os.Create("apis/" + table.PackageName + ".go")
	//if err != nil {
	//	t.Error(err)
	//}
	//defer file.Close()
	//
	//_ = t1.Execute(file, tab)
	t.Log("")
}
