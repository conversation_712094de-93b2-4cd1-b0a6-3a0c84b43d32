package config

import "github.com/go-admin-team/redisqueue/v2"

// Queue 队列配置
type Queue struct {
	Memory MemoryQueue `mapstructure:"memory"`
	Redis  RedisQueue  `mapstructure:"redis"`
	NSQ    QueueNSQ    `mapstructure:"nsq"`
}

// MemoryQueue 内存队列配置
type MemoryQueue struct {
	PoolSize int `mapstructure:"poolSize"`
}

// RedisQueue Redis队列配置
type RedisQueue struct {
	Addr     string        `mapstructure:"addr"`
	Password string        `mapstructure:"password"`
	Producer QueueProducer `mapstructure:"producer"`
	Consumer QueueConsumer `mapstructure:"consumer"`
}

type QueueRedis struct {
	RedisConnectOptions
	Producer redisqueue.ProducerOptions
	Consumer redisqueue.ConsumerOptions
}

// QueueProducer 生产者配置
type QueueProducer struct {
	StreamMaxLength      int  `mapstructure:"streamMaxLength"`
	ApproximateMaxLength bool `mapstructure:"approximateMaxLength"`
}

// QueueConsumer 消费者配置
type QueueConsumer struct {
	VisibilityTimeout int `mapstructure:"visibilityTimeout"`
	BufferSize        int `mapstructure:"bufferSize"`
	Concurrency       int `mapstructure:"concurrency"`
	BlockingTimeout   int `mapstructure:"blockingTimeout"`
	ReclaimInterval   int `mapstructure:"reclaimInterval"`
}

type QueueNSQ struct {
	NSQOptions
	ChannelPrefix string
}
