// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: org.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	OrgInfoService_GetOrgById_FullMethodName     = "/org.OrgInfoService/GetOrgById"
	OrgInfoService_GetOrgByCode_FullMethodName   = "/org.OrgInfoService/GetOrgByCode"
	OrgInfoService_GetOrgByName_FullMethodName   = "/org.OrgInfoService/GetOrgByName"
	OrgInfoService_GetOrgFullName_FullMethodName = "/org.OrgInfoService/GetOrgFullName"
)

// OrgInfoServiceClient is the client API for OrgInfoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 组织信息服务
type OrgInfoServiceClient interface {
	// 根据组织ID查询组织信息
	GetOrgById(ctx context.Context, in *GetOrgByIdReq, opts ...grpc.CallOption) (*OrgInfoReply, error)
	// 根据组织编码查询组织信息
	GetOrgByCode(ctx context.Context, in *GetOrgByCodeReq, opts ...grpc.CallOption) (*OrgInfoReply, error)
	// 根据组织名称查询组织信息
	GetOrgByName(ctx context.Context, in *GetOrgByNameReq, opts ...grpc.CallOption) (*OrgInfoReply, error)
	// 获取组织全名（包含上级路径）
	GetOrgFullName(ctx context.Context, in *GetOrgFullNameReq, opts ...grpc.CallOption) (*OrgFullNameReply, error)
}

type orgInfoServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewOrgInfoServiceClient(cc grpc.ClientConnInterface) OrgInfoServiceClient {
	return &orgInfoServiceClient{cc}
}

func (c *orgInfoServiceClient) GetOrgById(ctx context.Context, in *GetOrgByIdReq, opts ...grpc.CallOption) (*OrgInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrgInfoReply)
	err := c.cc.Invoke(ctx, OrgInfoService_GetOrgById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orgInfoServiceClient) GetOrgByCode(ctx context.Context, in *GetOrgByCodeReq, opts ...grpc.CallOption) (*OrgInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrgInfoReply)
	err := c.cc.Invoke(ctx, OrgInfoService_GetOrgByCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orgInfoServiceClient) GetOrgByName(ctx context.Context, in *GetOrgByNameReq, opts ...grpc.CallOption) (*OrgInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrgInfoReply)
	err := c.cc.Invoke(ctx, OrgInfoService_GetOrgByName_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *orgInfoServiceClient) GetOrgFullName(ctx context.Context, in *GetOrgFullNameReq, opts ...grpc.CallOption) (*OrgFullNameReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrgFullNameReply)
	err := c.cc.Invoke(ctx, OrgInfoService_GetOrgFullName_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OrgInfoServiceServer is the server API for OrgInfoService service.
// All implementations must embed UnimplementedOrgInfoServiceServer
// for forward compatibility.
//
// 组织信息服务
type OrgInfoServiceServer interface {
	// 根据组织ID查询组织信息
	GetOrgById(context.Context, *GetOrgByIdReq) (*OrgInfoReply, error)
	// 根据组织编码查询组织信息
	GetOrgByCode(context.Context, *GetOrgByCodeReq) (*OrgInfoReply, error)
	// 根据组织名称查询组织信息
	GetOrgByName(context.Context, *GetOrgByNameReq) (*OrgInfoReply, error)
	// 获取组织全名（包含上级路径）
	GetOrgFullName(context.Context, *GetOrgFullNameReq) (*OrgFullNameReply, error)
	mustEmbedUnimplementedOrgInfoServiceServer()
}

// UnimplementedOrgInfoServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOrgInfoServiceServer struct{}

func (UnimplementedOrgInfoServiceServer) GetOrgById(context.Context, *GetOrgByIdReq) (*OrgInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrgById not implemented")
}
func (UnimplementedOrgInfoServiceServer) GetOrgByCode(context.Context, *GetOrgByCodeReq) (*OrgInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrgByCode not implemented")
}
func (UnimplementedOrgInfoServiceServer) GetOrgByName(context.Context, *GetOrgByNameReq) (*OrgInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrgByName not implemented")
}
func (UnimplementedOrgInfoServiceServer) GetOrgFullName(context.Context, *GetOrgFullNameReq) (*OrgFullNameReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrgFullName not implemented")
}
func (UnimplementedOrgInfoServiceServer) mustEmbedUnimplementedOrgInfoServiceServer() {}
func (UnimplementedOrgInfoServiceServer) testEmbeddedByValue()                        {}

// UnsafeOrgInfoServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OrgInfoServiceServer will
// result in compilation errors.
type UnsafeOrgInfoServiceServer interface {
	mustEmbedUnimplementedOrgInfoServiceServer()
}

func RegisterOrgInfoServiceServer(s grpc.ServiceRegistrar, srv OrgInfoServiceServer) {
	// If the following call pancis, it indicates UnimplementedOrgInfoServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&OrgInfoService_ServiceDesc, srv)
}

func _OrgInfoService_GetOrgById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrgByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrgInfoServiceServer).GetOrgById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrgInfoService_GetOrgById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrgInfoServiceServer).GetOrgById(ctx, req.(*GetOrgByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrgInfoService_GetOrgByCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrgByCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrgInfoServiceServer).GetOrgByCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrgInfoService_GetOrgByCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrgInfoServiceServer).GetOrgByCode(ctx, req.(*GetOrgByCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrgInfoService_GetOrgByName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrgByNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrgInfoServiceServer).GetOrgByName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrgInfoService_GetOrgByName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrgInfoServiceServer).GetOrgByName(ctx, req.(*GetOrgByNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OrgInfoService_GetOrgFullName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrgFullNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OrgInfoServiceServer).GetOrgFullName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OrgInfoService_GetOrgFullName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OrgInfoServiceServer).GetOrgFullName(ctx, req.(*GetOrgFullNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

// OrgInfoService_ServiceDesc is the grpc.ServiceDesc for OrgInfoService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OrgInfoService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "org.OrgInfoService",
	HandlerType: (*OrgInfoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOrgById",
			Handler:    _OrgInfoService_GetOrgById_Handler,
		},
		{
			MethodName: "GetOrgByCode",
			Handler:    _OrgInfoService_GetOrgByCode_Handler,
		},
		{
			MethodName: "GetOrgByName",
			Handler:    _OrgInfoService_GetOrgByName_Handler,
		},
		{
			MethodName: "GetOrgFullName",
			Handler:    _OrgInfoService_GetOrgFullName_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "org.proto",
}
