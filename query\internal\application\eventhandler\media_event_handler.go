package eventhandler

import (
	"context"
	"fmt"
	"jxt-evidence-system/evidence-management/query/internal/models"
	"jxt-evidence-system/evidence-management/query/internal/models/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/global"
	client "jxt-evidence-system/evidence-management/shared/common/grpc/client/port"
	commonModels "jxt-evidence-system/evidence-management/shared/common/models"
	"jxt-evidence-system/evidence-management/shared/domain/event"
	"strings"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"github.com/ThreeDotsLabs/watermill/message"
	jsoniter "github.com/json-iterator/go"
)

func init() {
	registrations = append(registrations, registerMediaEventHandlerDependencies)
}

func registerMediaEventHandlerDependencies() {
	err := di.Provide(func(subscriber EventSubscriber,
		repo repository.MediaReadModelRepository,
		userInfoClient client.UserInfoServiceClient,
		orgInfoClient client.OrgInfoServiceClient,
		lawcameraInfoClient client.LawcameraInfoServiceClient) *MediaEventHandler {
		return NewMediaEventHandler(subscriber, repo, userInfoClient, orgInfoClient, lawcameraInfoClient)
	})
	if err != nil {
		logger.Error("Failed to provide MediaEventHandler", "error", err)
	}
}

type MediaEventHandler struct {
	Subscriber          EventSubscriber
	repo                repository.MediaReadModelRepository
	userInfoClient      client.UserInfoServiceClient
	orgInfoClient       client.OrgInfoServiceClient
	lawcameraInfoClient client.LawcameraInfoServiceClient
}

func NewMediaEventHandler(subscriber EventSubscriber,
	repo repository.MediaReadModelRepository,
	userInfoClient client.UserInfoServiceClient,
	orgInfoClient client.OrgInfoServiceClient,
	lawcameraInfoClient client.LawcameraInfoServiceClient,

) *MediaEventHandler {
	return &MediaEventHandler{
		Subscriber:          subscriber,
		repo:                repo,
		userInfoClient:      userInfoClient,
		orgInfoClient:       orgInfoClient,
		lawcameraInfoClient: lawcameraInfoClient,
	}
}

// isConnectionClosingError 检查是否是连接关闭错误
func (h *MediaEventHandler) isConnectionClosingError(err error) bool {
	if err == nil {
		return false
	}
	errMsg := err.Error()
	return strings.Contains(errMsg, "connection is closing") ||
		strings.Contains(errMsg, "client connection is closing") ||
		strings.Contains(errMsg, "transport is closing") ||
		strings.Contains(errMsg, "context canceled") ||
		strings.Contains(errMsg, "rpc error: code = Canceled")
}

// retryWithExponentialBackoff 使用指数退避进行重试
func (h *MediaEventHandler) retryWithExponentialBackoff(ctx context.Context, operation func() error, maxRetries int) error {
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		err := operation()
		if err == nil {
			return nil
		}

		lastErr = err

		// 如果不是连接关闭错误，直接返回
		if !h.isConnectionClosingError(err) {
			logger.Warn("非连接错误，停止重试", "error", err, "attempt", attempt+1)
			return err
		}

		// 计算退避时间：1s, 2s, 4s, 8s, 16s
		backoffDuration := time.Duration(1<<uint(attempt)) * time.Second
		if backoffDuration > 30*time.Second {
			backoffDuration = 30 * time.Second // 最大30秒
		}

		logger.Warn("检测到连接关闭错误，将重试",
			"error", err,
			"attempt", attempt+1,
			"maxRetries", maxRetries,
			"backoff", backoffDuration)

		// 如果不是最后一次尝试，则等待
		if attempt < maxRetries-1 {
			timer := time.NewTimer(backoffDuration)
			select {
			case <-ctx.Done():
				timer.Stop()
				return ctx.Err()
			case <-timer.C:
				// 继续下一次重试
			}
		}
	}

	logger.Error("达到最大重试次数，仍然失败", "maxRetries", maxRetries, "lastError", lastErr)
	return fmt.Errorf("达到最大重试次数 %d，最后错误: %w", maxRetries, lastErr)
}

func (h *MediaEventHandler) ConsumeEvent(topic string) error {
	if err := h.Subscriber.Subscribe(topic, h.handleMediaEvent, time.Second*30); err != nil {
		logger.Error("Failed to subscribe to topic", "topic", topic, "error", err)
		return err
	}

	return nil
}

func (h *MediaEventHandler) handleMediaEvent(msg *message.Message) error {
	// Step 1: 反序列化为领域事件结构体
	domainEvent := &event.DomainEvent{}

	err := domainEvent.UnmarshalJSON(msg.Payload)
	if err != nil {
		return fmt.Errorf("failed to unmarshal media event: %w", err)
	}

	// Step 2. 取出领域事件的租户id
	tenantID := domainEvent.GetTenantId()
	if tenantID == "" {
		return fmt.Errorf("租户ID不能为空")
	}

	// Step 3. 把租户id记录到context，传给repo
	ctx := context.WithValue(context.Background(), global.TenantIDKey, tenantID)

	// Step 4: 根据事件类型进行处理
	eventType := domainEvent.GetEventType()
	switch eventType {
	case event.EventTypeMediaUploaded:
		return h.handleMediaUploadedEvent(ctx, domainEvent)
	case event.EventTypeMediaUpdated:
		return h.handleMediaUpdatedEvent(ctx, domainEvent)
	case event.EventTypeMediaDeleted:
		return h.handleMediaDeletedEvent(ctx, domainEvent)
	case event.EventTypeMediaIsNonLawEnforcementMediaUpdated:
		return h.handleMediaIsNonLawEnforcementMediaUpdatedEvent(ctx, domainEvent)
	case event.EventTypeMediaBatchUpdated:
		return h.handleBatchMediaUpdatedEvent(ctx, domainEvent)
	case event.EventTypeMediaBatchDeleted:
		return h.handleMediaBatchDeletedEvent(ctx, domainEvent)
	case event.EventTypeMediaBatchIsNonLawEnforcementMediaUpdated:
		return h.handleMediaBatchIsNonLawEnforcementMediaUpdatedEvent(ctx, domainEvent)
	case event.EventTypeMediaBatchEnforceTypeUpdated:
		return h.handleMediaBatchEnforceTypeUpdatedEvent(ctx, domainEvent)
	case event.EventTypeMediaBatchIsLockedUpdated:
		return h.handleMediaBatchIsLockedUpdatedEvent(ctx, domainEvent)
	default:
		logger.Errorf("unknown media event type: %s", eventType)
		return fmt.Errorf("unknown media event type: %s", eventType)
	}
}

func (h *MediaEventHandler) handleMediaUploadedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.MediaUploadedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling MediaUploadedPayload", "error", err)
		return fmt.Errorf("error unmarshalling MediaUploadedPayload: %w", err)
	}

	// 构建读模型
	mediaReadModel := &models.MediaReadModel{
		ID:                    payload.MediaID,
		MediaName:             payload.MediaName,
		MediaCate:             payload.MediaCate,
		MediaSuffix:           payload.MediaSuffix,
		ShotTimeStart:         payload.ShotTimeStart,
		ShotTime:              payload.ShotTime,
		VideoClarity:          payload.VideoClarity,
		VideoDuration:         payload.VideoDuration,
		ImportantLevel:        payload.ImportantLevel,
		ImportantLevelRec:     payload.ImportantLevelRec,
		Width:                 payload.Width,
		Height:                payload.Height,
		IsNonEnforcementMedia: payload.IsNonEnforcementMedia,
		Comments:              payload.Comments,
		Sequence:              payload.Sequence,
		EnforceType:           payload.EnforceType,
		IsLocked:              payload.IsLocked,
		ExpiryTime:            payload.ExpiryTime,
		ArchiveID:             payload.ArchiveID,
		IsArchive:             payload.IsArchived,
		ArchiveDate:           payload.ArchiveDate,
		FileIdentity:          payload.FileIdentity,
		FileName:              payload.FileName,
		FileSize:              payload.FileSize,
		FileMd5:               payload.FileMd5,
		FileType:              payload.FileType,
		ContentType:           payload.ContentType,
		URI:                   payload.URI,
		Thumbnail:             payload.Thumbnail,
		SiteID:                payload.SiteID,
		StorageID:             payload.StorageID,
		StorageType:           payload.StorageType,
		IsSendToStorage:       payload.IsSendToStorage,
		IsNoticeSend:          payload.IsNoticeSend,
		PoliceID:              payload.PoliceID,
		OrgID:                 payload.OrgID,
		RecorderID:            payload.RecorderID,
		SiteClientID:          payload.SiteClientID,
		TerminalType:          payload.TerminalType,
		TrialID:               payload.TrialID,
		IncidentCode:          payload.IncidentCode,
		IsAssociated:          payload.IsAssociated,
		AssociateTime:         payload.AssociateTime,
		ImportTime:            payload.ImportTime,
		AcquisitionTime:       payload.AcquisitionTime,
		ControlBy: commonModels.ControlBy{
			CreateBy: payload.CreateBy,
			UpdateBy: payload.UpdateBy,
		},
		ModelTime: commonModels.ModelTime{
			CreatedAt: payload.CreatedAt,
			UpdatedAt: payload.UpdatedAt,
		},
	}

	logger.Info("Received MediaUploadedEvent", "payload", payload)

	// 获取租户ID
	tenantID := domainEvent.GetTenantId()

	// ============================================
	// 关键业务流程：查询外部信息并构建完整的读模型
	// 使用应用层重试确保数据一致性
	// ============================================
	err := h.retryWithExponentialBackoff(ctx, func() error {
		// 根据用户ID查询用户信息
		userInfoRaw, err := h.userInfoClient.GetUserById(ctx, tenantID, int32(payload.PoliceID))
		if err != nil {
			logger.Error("根据用户ID查询用户信息失败", "policeID", payload.PoliceID, "error", err)
			return fmt.Errorf("根据用户ID[%d]查询用户信息失败: %v", payload.PoliceID, err)
		}
		if userInfoRaw == nil {
			logger.Error("用户ID对应的用户不存在", "policeID", payload.PoliceID)
			return fmt.Errorf("用户ID[%d]对应的用户不存在", payload.PoliceID)
		}

		// 直接访问字段
		mediaReadModel.PoliceNo = userInfoRaw.PoliceNo
		mediaReadModel.PoliceName = userInfoRaw.UserName

		// 根据组织ID查询组织信息
		orgInfoRaw, err := h.orgInfoClient.GetOrgById(ctx, tenantID, int32(payload.OrgID))
		if err != nil {
			logger.Error("根据组织ID查询组织信息失败", "orgID", payload.OrgID, "error", err)
			return fmt.Errorf("根据组织ID[%d]查询组织信息失败: %v", payload.OrgID, err)
		}
		if orgInfoRaw == nil {
			logger.Error("组织ID对应的组织不存在", "orgID", payload.OrgID)
			return fmt.Errorf("组织ID[%d]对应的组织不存在", payload.OrgID)
		}

		// 直接访问字段
		mediaReadModel.OrgCode = orgInfoRaw.OrgCode
		mediaReadModel.OrgName = orgInfoRaw.OrgName
		mediaReadModel.OrgJc = orgInfoRaw.OrgNameJc

		// 根据执法记录仪ID查询执法记录仪信息
		lawcameraInfoRaw, err := h.lawcameraInfoClient.GetLawcameraById(ctx, tenantID, int32(payload.RecorderID))
		if err != nil {
			logger.Error("根据执法记录仪ID查询执法记录仪信息失败", "recorderID", payload.RecorderID, "error", err)
			return fmt.Errorf("根据执法记录仪ID[%d]查询执法记录仪信息失败: %v", payload.RecorderID, err)
		}
		if lawcameraInfoRaw == nil {
			logger.Error("执法记录仪ID对应的执法记录仪不存在", "recorderID", payload.RecorderID)
			return fmt.Errorf("执法记录仪ID[%d]对应的执法记录仪不存在", payload.RecorderID)
		}

		// 直接访问字段
		mediaReadModel.RecorderNo = lawcameraInfoRaw.No

		// 直接使用 repository 创建读模型
		err = h.repo.Create(ctx, mediaReadModel)
		if err != nil {
			logger.Error("创建媒体读模型失败", "error", err)
			return err
		}

		return nil
	}, 3) // 关键业务流程重试3次

	if err != nil {
		logger.Error("媒体上传事件处理失败", "error", err)
		return err
	}

	logger.Info("媒体上传事件处理成功", "mediaID", payload.MediaID)
	return nil
}

func (h *MediaEventHandler) handleMediaUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.MediaUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling MediaUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling MediaUpdatedPayload: %w", err)
	}

	logger.Info("Received MediaUpdatedEvent", "payload", payload)

	// 直接使用 repository 更新读模型
	err := h.repo.UpdateByID(ctx, payload.MediaID, payload.UpdatedFields)
	if err != nil {
		logger.Error("更新媒体读模型失败", "error", err)
		return err
	}

	return nil
}

func (h *MediaEventHandler) handleMediaDeletedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.MediaDeletedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling MediaBatchDeletedPayload", "error", err)
		return fmt.Errorf("error unmarshalling MediaBatchDeletedPayload: %w", err)
	}

	logger.Info("Received MediaDeletedEvent", "payload", payload)

	// 直接使用 repository 删除读模型
	err := h.repo.DeleteByID(ctx, payload.GetId().(int64))
	if err != nil {
		logger.Error("删除媒体[%d]读模型失败", payload.MediaID, "error", err)
		return err
	}

	return nil
}

func (h *MediaEventHandler) handleMediaIsNonLawEnforcementMediaUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.MediaIsNonLawEnforcementMediaUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling MediaIsNonLawEnforcementMediaUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling MediaIsNonLawEnforcementMediaUpdatedPayload: %w", err)
	}

	logger.Info("Received MediaIsNonLawEnforcementMediaUpdatedEvent", "payload")

	// 定义需要更新的字段
	updates := map[string]interface{}{
		"IsNonEnforcementMedia": payload.IsEnforMedia,
		"UpdateBy":              payload.UpdateBy,
		"UpdatedAt":             payload.UpdatedAt,
	}

	// 直接使用 repository 更新执法媒体状态
	err := h.repo.UpdateByID(ctx, payload.MediaID, updates)
	if err != nil {
		logger.Error("更新执法媒体状态失败", "error", err)
		return err
	}

	return nil
}

func (h *MediaEventHandler) handleBatchMediaUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.MediaBatchUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling MediaBatchUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling MediaBatchUpdatedPayload: %w", err)
	}

	logger.Info("Received MediaBatchUpdatedEvent", "payload", payload)

	// 直接使用 repository 更新读模型
	_, err := h.repo.UpdateManyByIDs(ctx, payload.MediaIDs, payload.UpdatedFields)
	if err != nil {
		logger.Error("更新媒体读模型失败", "error", err)
		return err
	}

	return nil
}

func (h *MediaEventHandler) handleMediaBatchDeletedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.MediaBatchDeletedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling MediaBatchDeletedPayload", "error", err)
		return fmt.Errorf("error unmarshalling MediaBatchDeletedPayload: %w", err)
	}

	logger.Info("Received MediaBatchDeletedEvent", "payload", payload)

	// 直接使用 repository 删除读模型
	_, err := h.repo.DeleteManyByIDs(ctx, payload.GetIds().([]int64))
	if err != nil {
		logger.Error("删除媒体读模型失败", "error", err)
		return err
	}

	return nil
}

func (h *MediaEventHandler) handleMediaBatchIsNonLawEnforcementMediaUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.MediaBatchIsNonLawEnforcementMediaUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling MediaBatchIsNonLawEnforcementMediaUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling MediaBatchIsNonLawEnforcementMediaUpdatedPayload: %w", err)
	}

	logger.Info("Received MediaBatchIsNonLawEnforcementMediaUpdatedEvent", "payload", payload)

	// 定义需要更新的字段
	updates := map[string]interface{}{
		"IsNonEnforcementMedia": payload.IsEnforMedia,
		"UpdateBy":              payload.UpdateBy,
		"UpdatedAt":             payload.UpdatedAt,
	}

	// 直接使用 repository 更新执法媒体状态
	_, err := h.repo.UpdateManyByIDs(ctx, payload.MediaIDs, updates)
	if err != nil {
		logger.Error("更新执法媒体状态失败", "error", err)
		return err
	}

	return nil
}

func (h *MediaEventHandler) handleMediaBatchEnforceTypeUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.MediaBatchEnforceTypeUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling MediaBatchEnforceTypeUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling MediaBatchEnforceTypeUpdatedPayload: %w", err)
	}

	logger.Info("Received MediaBatchEnforceTypeUpdatedEvent", "payload", payload)

	// 定义需要更新的字段
	updates := map[string]interface{}{
		"EnforceType": payload.EnforceType,
		"UpdateBy":    payload.UpdateBy,
		"UpdatedAt":   payload.UpdatedAt,
	}

	// 直接使用 repository 批量更新执法类型
	_, err := h.repo.UpdateManyByIDs(ctx, payload.MediaIDs, updates)
	if err != nil {
		logger.Error("批量更新执法类型失败", "error", err)
		return err
	}

	return nil
}

func (h *MediaEventHandler) handleMediaBatchIsLockedUpdatedEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var payload event.MediaBatchIsLockedUpdatedPayload
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	if err := json.Unmarshal(domainEvent.Payload, &payload); err != nil {
		logger.Error("Error unmarshalling MediaBatchIsLockedUpdatedPayload", "error", err)
		return fmt.Errorf("error unmarshalling MediaBatchIsLockedUpdatedPayload: %w", err)
	}

	logger.Info("Received MediaBatchIsLockedUpdatedEvent", "payload", payload)

	// 定义需要更新的字段
	updates := map[string]interface{}{
		"IsLocked":  payload.IsLocked,
		"UpdateBy":  payload.UpdateBy,
		"UpdatedAt": payload.UpdatedAt,
	}

	// 直接使用 repository 批量更新锁定状态
	_, err := h.repo.UpdateManyByIDs(ctx, payload.MediaIDs, updates)
	if err != nil {
		logger.Error("批量更新锁定状态失败", "error", err)
		return err
	}

	return nil
}
