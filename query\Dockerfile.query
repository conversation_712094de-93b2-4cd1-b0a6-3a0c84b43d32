FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/golang:1.23-alpine AS builder

ENV GOPROXY https://goproxy.cn/
ENV GO111MODULE=on

# 安装必要的工具
RUN apk add --no-cache git tzdata

# 设置构建环境工作目录
WORKDIR /app

# 复制工作区和模块文件
COPY go.work go.work  
COPY go.work.sum go.work.sum


# 复制query模块的内容
COPY ./query ./query
# 复制 shared 目录
COPY ./shared ./shared
# 复制command模块的内容,虽然query不依赖command，但执行go work sync时需要command
COPY ./command ./command
# 复制 tests 目录,虽然command不依赖tests，但执行go work sync时需要tests
COPY ./tests ./tests

# 运行 go work sync 同步工作区的模块依赖关系
RUN go work sync

# 移动到 specific module 目录中
WORKDIR /app/query

# 清理和下载所有该模块所需的依赖
RUN go mod tidy
RUN go mod download all

# 打印当前工作目录和列出目录内容
RUN pwd && ls

RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-w -s" -a -installsuffix cgo -o evidence-query ./cmd

# 清理不必要的文件
# RUN rm -rf /app/query /app/shared /app/go.work /app/go.work.sum

FROM swr.cn-north-4.myhuaweicloud.com/ddn-k8s/docker.io/alpine:3.20.2

# 设置环境变量
ENV APP_ENV=production

# 设置生产环境工作目录
WORKDIR /app

COPY --from=builder /app/query/evidence-query /app

COPY --from=builder /app/query/config/settings.yml /app/config/settings.yml

COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 创建应用程序的日志目录
RUN mkdir -p /app/log

# 设置环境变量  
ENV CONFIG_PATH=/app/config/settings.yml

# 设置权限  
RUN chmod +x /app/evidence-query && \
    chown -R nobody:nobody /app /app/log  

USER nobody 

EXPOSE 8082

ENTRYPOINT ["/app/evidence-query"]  
# CMD ["server", "-c", "${CONFIG_PATH}"]  
CMD server -c $CONFIG_PATH