@startuml
'https://plantuml.com/class-diagram

class UploadMediaCommand<<Command>> #lightgreen {
--rule--
--fields--
MediaInfo
--Event--
MediaUploaded
}

class RemarkNonEnforMediaCommand<<Command>> #lightgreen {
--rule--
当某个媒体被标记为‘非执法媒体’时，
如果其在某个媒体集中，则从媒体集
中将其删除
--fields--
MediaIds
MediaCollectionId(可选)
--Event--
MediaNonEnforcementDesignation
}

class UpdateMediaCommand<<Command>> #lightgreen {
--rule--
手工修改媒体的标注内容
--fields--
MediaIds
--Event--
MediaUpdated
}

class MediaCommandHandler<<CommandHandler>> {
--method--
doUploadMediaCommand()
doRemarkNonEnforMediaCommand()
}

UploadMediaCommand --> MediaCommandHandler
RemarkNonEnforMediaCommand --> MediaCommandHandler
UpdateMediaCommand --> MediaCommandHandler

MediaCommandHandler --> Media

class Media<<Aggregate>> #lightyellow {
--rule--
1、一旦有新的媒体成功上传，则发送事件通知视频集领域服务重新计算视频集
2、如果被标注为非执法的媒体有对应媒体集，则媒体聚合创建并发送事件通
知给媒体集移除该媒体
--id--
MediaId
--fields--
MediaInfo
}

Media --> MediaUploadedEvent
Media --> MediaNonEnforcementDesignationEvent

class MediaUploadedEvent<<DomainEvent>> #lightblue {
--id--
EventId
--fields--
OccurredOn
MediaId
UploadTime
SiteIp
}

class MediaNonEnforcementDesignationEvent<<DomainEvent>> #lightblue {
--id--
EventId
--fields--
OccurredOn
MediaId
MediaCollectionId(必选)
UserId
}

class CalculateMediaCollectionService<<DomainService>> #pink {
计算视频集作为一个独立微服务
--rule--
接收到媒体上传事件后重新计算视频集，
将新上传的媒体加入合适视频集或新生
成的视频集，结合定时器平滑处理计算任务
--method--
calculateMediaCollection()
}

class MediaCollectionService<<DomainService>> #pink {
--rule--
收到MediaNonEnforcementDesignationEvent事件，
根据MediaId检查对应的MediaCollectionId是否存在，
如果存在则视频集移除该媒体
--method--
handleMediaNonEnforcementDesignationEvent()
}



MediaUploadedEvent --> CalculateMediaCollectionService
CalculateMediaCollectionService --> MediaCollection

MediaNonEnforcementDesignationEvent --> MediaCollectionService
MediaCollectionService --> MediaCollection

class AddMediaToMediaCollectionCommand<<Command>> #lightgreen {
--rule--
一个媒体只能归属于一个媒体集，
加入新媒体集，则从旧媒体集删除
--fields--
MediaIds
MediaCollectionId
--Event--
MediaAddedToMediaCollection
}

class RemoveMediaFromMediaCollectionCommand<<Command>> #lightgreen {
--rule--
属于媒体集的媒体才能从媒体集移除
--fields--
MediaIds
MediaCollectionId
--Event--
MediaRemovedFromMediaCollection
}



class MediaCollectionCommandHandler<<CommandHandler>> {
--method--
doAddMediaToMediaCollectionCommand()
doRemoveMediaFromMediaCollectionCommand()
}

AddMediaToMediaCollectionCommand --> MediaCollectionCommandHandler
RemoveMediaFromMediaCollectionCommand --> MediaCollectionCommandHandler

class MediaCollection<<Aggregate>> #lightyellow {
--rule--
1、容许移除媒体集中媒体
2、容许添加媒体集不存在的媒体
--id--
MediaCollectionId
--fields--
MediaList
}

MediaCollectionCommandHandler --> MediaCollection

MediaCollection --> MediaRemovedFromMediaCollectionEvent
MediaCollection --> MediaAddedToMediaCollectionEvent

class MediaAddedToMediaCollectionEvent<<DomainEvent>> #lightblue {
--id--
EventId
--fields--
OccurredOn
MediaCollectionId
MediaIds
UserId
}

class MediaRemovedFromMediaCollectionEvent<<DomainEvent>> #lightblue {
--id--
EventId
--fields--
OccurredOn
MediaCollectionId
MediaIds
UserId
}


class SynchronizeAlarmCommand<<Command>> #lightgreen {
--fields--
AlarmInfo
}

class UpdateAlarmCommand<<Command>> #lightgreen {
--fields--
AlarmInfo
}

class AlarmCommandHandler<<CommandHandler>> {
--method--
doSynchronizeAlarmCommand()
doUpdateAlarmCommand()
}

SynchronizeAlarmCommand --> AlarmCommandHandler
UpdateAlarmCommand --> AlarmCommandHandler
AlarmCommandHandler --> Alarm

class Alarm<<Aggregate>> #lightyellow {
--id--
AlarmId
--fields--
AlarmInfo
}

Alarm --> AlarmSynchronizedEvent
Alarm --> AlarmUpdatedEvent

class AlarmSynchronizedEvent<<DomainEvent>> #lightblue {
--id--
EventId
--fields--
OccurredOn
AlarmId
}

class AlarmUpdatedEvent<<DomainEvent>> #lightblue {
--id--
EventId
--fields--
OccurredOn
AlarmId
}

class CreateRecordCommand<<Command>> #lightgreen {
--rule--
1、执法数据只能关联一个媒体集，
如果关联新媒体集，则解除与旧
媒体集的关联关系（即删除旧档案，
解除旧视频与执法行为的关联）
2、创建档案时，要同步更新媒体的
标注内容与关联时间以及执法数据
的关联状态和时间（同步不及时可能
会影响下一个视频集的执法行为关联）
--fields--
AlarmId
MediaCollectionId
--Event--
RecordCreatedEvent
}

class UpdateRecordCommand<<Command>> #lightgreen {
--rule--
执法档案存在且未删除
--id--
RecordId
--fields--
AlarmId
MediaCollectionId
--Event--
RecordUpdatedEvent
}

class DeleteRecordCommand<<Command>> #lightgreen {
--rule--
执法档案存在且未删除
--id--
RecordId
--fields--
AlarmId
MediaCollectionId
--Event--
RecordDeletedEvent
}

class AddMediaToRecordCommand<<Command>> #lightgreen {
--rule--
一个媒体似乎可归属于多个档案，
媒体的标注内容随加入和移除档案
同步更新
--fields--
RecordId
MediaIds
--Event--
MediaAddedToRecordEvent
}

class RemoveMediaFromRecordCommand<<Command>> #lightgreen {
--rule--
属于档案的媒体才能从档案移除，
移除后媒体的标注内容同步更新
--fields--
RecordId
MediaIds
--Event--
MediaRemovedFromRecordEvent
}


class RecordCommandHandler<<CommandHandler>> {
--rule--
业务规则由领域模型(领域服务、聚合等)承接,CommandHandler起编排作用
--method--
doCreateRecordCommand()
doUpdateRecordCommand()
doDeleteRecordCommand()
doAddMediaToRecordCommand()
doRemoveMediaFromRecordCommand()
}

CreateRecordCommand --> RecordCommandHandler
UpdateRecordCommand --> RecordCommandHandler
DeleteRecordCommand --> RecordCommandHandler
AddMediaToRecordCommand --> RecordCommandHandler
RemoveMediaFromRecordCommand --> RecordCommandHandler
RecordCommandHandler --> RecordService
RecordService --> Record
RecordService --> Alarm
RecordService --> Media
RecordService --> MediaCollection
RecordService --> RecordExpireTask

class RecordService<<DomainService>>  #pink {
档案处理涉及到媒体、媒体集、Alarm等聚合，所有由应用服务协同这些聚合
--rule--
1、创建执法档案时，
a)同步更新媒体的是否执法视频、标注内容和关联时间
b)同步更新执法数据的关联状态和关联时间
c)如果执法数据已关联档案，需解除旧档案与旧视频集的关联，并删除旧档案、旧档期满管理任务
d)新增期满管理任务
2、用户删除执法档案时，则需要同步更新媒体标注内容，以及执法数据的关联状态和关联时间，删除对应的期满任务
3、档案添加媒体时，则控制媒体集添加媒体，以及同步更新媒体标注内容、关联时间和是否执法视频标记
4、档案移除媒体时，则控制媒体集移除媒体，以及同步更新媒体标注内容、关联时间
--method--
createRecord()
deleteRecord()
updateRecord()
AddMediaToMediaCollectionOfRecord()
RemoveMediaFromMediaCollectionOfRecord()
}

class Record<<Aggregate>> #lightyellow {
--rule--

--id--
RecordId
--fields--
AlarmId
MediaCollectionId
--Event--
}

Record --> RecordCreatedEvent
Record --> RecordUpdatedEvent
Record --> RecordDeletedEvent
Record --> MediaAddedToRecordEvent
Record --> MediaRemovedFromRecordEvent

class RecordCreatedEvent<<DomainEvent>> #lightblue {
--id--
EventId
--fields--
OccurredOn
RecordId
VocatId
MediaCollectionId
MediaIds
ExpireTime
UserId
}

class RecordUpdatedEvent<<DomainEvent>> #lightblue {
--id--
EventId
--fields--
OccurredOn
RecordId
ModifyInfo
UserId
}

class RecordDeletedEvent<<DomainEvent>> #lightblue {
--id--
EventId
--fields--
OccurredOn
RecordId
VocatId
MediaCollectionId
MediaIds
UserId
}

class MediaAddedToRecordEvent<<DomainEvent>> #lightblue {
--id--
EventId
--fields--
OccurredOn
RecordId
MediaCollectionId
MediaIds
UserId
}

class MediaRemovedFromRecordEvent<<DomainEvent>> #lightblue {
--id--
EventId
--fields--
OccurredOn
RecordId
MediaCollectionId
MediaIds
UserId
}

class RecordExpireTask<<Aggregate>> #lightyellow {
--rule--
档案达到过期时间发出删除通知
--id--
TaskId
--fields--
TaskName
RecordId
MediaCollectionId
--Event--
}

@enduml