package eventbus

import (
	"context"
	"fmt"
	"log"
	"sync"
	"sync/atomic"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg"
	"github.com/Shopify/sarama"
	"github.com/ThreeDotsLabs/watermill/message"
)

type HealthCheckConfig struct {
	Topic         string
	Interval      time.Duration
	MaxMessageAge time.Duration
}

func DefaultHealthCheckConfig() HealthCheckConfig {
	return HealthCheckConfig{
		Topic:         HealthCheckTopic,
		Interval:      1 * time.Minute,
		MaxMessageAge: 5 * time.Minute,
	}
}

type HealthChecker struct {
	config          HealthCheckConfig
	lastMessageTime atomic.Value // stores time.Time
	manager         *KafkaSubscriberManager
	wg              sync.WaitGroup
	rootCtx         context.Context
	rootCancel      context.CancelFunc
}

func NewHealthChecker(config HealthCheckConfig, manager *KafkaSubscriberManager) *HealthChecker {
	hc := &HealthChecker{
		config:  config,
		manager: manager,
	}
	return hc
}

func (hc *HealthChecker) updateLastMessageTime() {
	hc.lastMessageTime.Store(time.Now())
}

func (hc *HealthChecker) healthCheck() (string, error) {
	lastMessageTime := hc.lastMessageTime.Load().(time.Time)
	if time.Since(lastMessageTime) > hc.config.MaxMessageAge {
		return "", fmt.Errorf("no health check message received in the last %v", hc.config.MaxMessageAge)
	}
	return "Kafka subscriber is healthy", nil
}

func (hc *HealthChecker) subscribeToHealthCheckTopic() error {
	messages, err := hc.manager.SubscribeToHealthCheckTopic(hc.config.Topic)
	if err != nil {
		return fmt.Errorf("failed to subscribe to health check topic: %w", err)
	}
	hc.wg.Add(1)
	go hc.processHealthCheckMessages(hc.rootCtx, messages)

	return nil
}

func (hc *HealthChecker) testConnection() error {
	client, err := sarama.NewClient(hc.manager.Config.KafkaConfig.Brokers, nil)
	if err != nil {
		return fmt.Errorf("failed to create Kafka client: %w", err)
	}
	defer client.Close()

	topics, err := client.Topics()
	if err != nil {
		return fmt.Errorf("failed to fetch topics: %w", err)
	}

	if len(topics) == 0 {
		return fmt.Errorf("no topics found in Kafka cluster")
	}

	return nil
}

func (hc *HealthChecker) processHealthCheckMessages(ctx context.Context, messages <-chan *message.Message) {
	defer hc.wg.Done()
	log.Println(pkg.Green("Started processing health check messages"))

	for {
		select {
		case <-ctx.Done():
			log.Println(pkg.Yellow("Context cancelled, stopping health check message processing"))
			return
		case msg, ok := <-messages:
			if !ok {
				// 当kafka故障时间超过阈值，消息通道会关闭，需要重新订阅才能恢复
				log.Println(pkg.Yellow("Messages channel closed, stopping health check message processing"))
				return
			}
			if msg != nil {
				hc.updateLastMessageTime()
				log.Printf(pkg.Green("Received health check message: %s at %s"), string(msg.Payload), time.Now().Format(time.RFC3339))
				msg.Ack()
			} else {
				log.Println(pkg.Yellow("Received nil message, channel might be closed"))
			}
		case <-time.After(3 * hc.config.Interval):
			log.Printf(pkg.Yellow("No health check messages received in the last %d minutes"), (hc.config.Interval/time.Minute)*3)
		}
	}
}

func (hc *HealthChecker) Start() error {
	hc.rootCtx, hc.rootCancel = context.WithCancel(hc.manager.rootCtx)

	if err := hc.subscribeToHealthCheckTopic(); err != nil {
		return err
	}

	log.Printf(pkg.Green("Started subscribing to health check topic: %s"), hc.config.Topic)

	if err := hc.testConnection(); err != nil {
		log.Printf(pkg.Red("Kafka connection test failed: %v"), err)
		return err
	}
	log.Println(pkg.Green("Kafka connection test successful"))
	hc.lastMessageTime.Store(time.Now())

	return nil
}

func (hc *HealthChecker) Stop() {
	hc.rootCancel()
	hc.wg.Wait()
}
