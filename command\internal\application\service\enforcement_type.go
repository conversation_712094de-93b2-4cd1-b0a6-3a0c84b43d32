package service

import (
	"context"
	"errors"
	"fmt"
	"jxt-evidence-system/evidence-management/command/internal/application/command"
	"jxt-evidence-system/evidence-management/command/internal/application/service/port"
	"jxt-evidence-system/evidence-management/command/internal/application/transaction"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/enforcementtype"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/enforcementtype/repository"
	"jxt-evidence-system/evidence-management/command/internal/domain/event/publisher"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/eventbus"
	"jxt-evidence-system/evidence-management/shared/common/global"
	"jxt-evidence-system/evidence-management/shared/common/service"
	domain_event "jxt-evidence-system/evidence-management/shared/domain/event"
	event_repository "jxt-evidence-system/evidence-management/shared/domain/event/repository"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"time"

	"go.uber.org/zap"
)

func init() {
	registrations = append(registrations, registerEnforceTypeServiceDependencies)
}

// EnforcementTypeService 执法类型应用服务
type enforcementTypeService struct {
	service.Service
	repo           repository.EnforcementTypeRepository
	tm             transaction.TransactionManager
	eventPublisher publisher.EventPublisher
	eventRepo      event_repository.DomainEventRepository
}

func registerEnforceTypeServiceDependencies() {
	err := di.Provide(func(repo repository.EnforcementTypeRepository, tm transaction.TransactionManager, eventPublisher publisher.EventPublisher, eventRepo event_repository.DomainEventRepository) port.EnforcementTypeService {
		return &enforcementTypeService{
			repo:           repo,
			tm:             tm,
			eventPublisher: eventPublisher,
			eventRepo:      eventRepo,
		}
	})
	if err != nil {
		logger.Fatalf("failed to provide EnforceTypeService: %v", err)
	}
}

// Create 创建执法类型
func (s *enforcementTypeService) CreateEnforcementType(ctx context.Context, command *command.CreateEnforcementTypeCommand) error {
	// 检查编码是否已存在
	existing, err := s.repo.FindByCode(ctx, command.EnforcementTypeCode)
	if err == nil && existing != nil {
		return errors.New("创建执法类型失败，执法类型编码已存在")
	}

	// 转换为领域模型
	enforceType := s.createCommandToDomain(command)

	// 在事务中执行创建操作
	err = transaction.RunInTransaction(ctx, s.tm, func(tx transaction.Transaction) error {
		// 构建执法类型路径
		if enforceType.ParentId > 0 {
			parent, err := s.repo.FindByID(ctx, enforceType.ParentId)
			if err != nil {
				return err
			}
			if parent == nil {
				return errors.New("创建执法类型失败，父执法类型不存在")
			}
			enforceType.EnforcementTypePath = parent.EnforcementTypePath + "/" + enforceType.EnforcementTypeCode
		} else {
			enforceType.EnforcementTypePath = "/" + enforceType.EnforcementTypeCode
		}

		// 保存执法类型
		if err := s.repo.Create(ctx, tx, enforceType); err != nil {
			return err
		}

		// 创建执法类型创建领域事件，放在持久化之后，这样才能拿到数据库分配的id
		enforceType.Create()

		// 发布事件
		return s.publishEvents(ctx, enforceType)
	})

	return err
}

// Update 更新执法类型
func (s *enforcementTypeService) UpdateEnforcementTypeByID(ctx context.Context, command *command.UpdateEnforcementTypeCommand) error {
	// 检查是否存在
	existing, err := s.repo.FindByID(ctx, command.ID)
	if err != nil {
		return err
	}
	if existing == nil {
		return errors.New("执法类型不存在")
	}

	// // 检查编码是否已被其他记录使用，确保唯一性，业务规则检查移动到领域模型中了，这里不需要再检查
	// if existing.EnforcementTypeCode != command.EnforcementTypeCode {
	// 	codeExisting, err := s.repo.FindByCode(ctx, command.EnforcementTypeCode)
	// 	if err == nil && codeExisting != nil && codeExisting.ID != command.ID {
	// 		return errors.New("执法类型编码已存在")
	// 	}
	// }

	// 创建领域模型
	originalDomain := &enforcementtype.EnforcementType{
		ID:                  existing.ID,
		EnforcementTypeCode: existing.EnforcementTypeCode,
		EnforcementTypeName: existing.EnforcementTypeName,
		EnforcementTypeDesc: existing.EnforcementTypeDesc,
		EnforcementTypePath: existing.EnforcementTypePath,
		ParentId:            existing.ParentId,
		Source:              existing.Source,
		Sort:                existing.Sort,
		ControlBy:           existing.ControlBy,
		ModelTime:           existing.ModelTime,
	}

	// 将命令转换为更新字段映射
	updates := make(map[string]interface{})
	updates["ID"] = command.ID
	updates["EnforcementTypeCode"] = command.EnforcementTypeCode

	// 添加可选字段（如果提供了）
	if command.EnforcementTypeName != nil {
		updates["EnforcementTypeName"] = *command.EnforcementTypeName
	}
	if command.EnforcementTypeDesc != nil {
		updates["EnforcementTypeDesc"] = *command.EnforcementTypeDesc
	}
	if command.EnforcementTypePath != nil {
		updates["EnforcementTypePath"] = *command.EnforcementTypePath
	}

	// 检查父节点ID是否为0或已存在
	if command.ParentId != nil {
		if *command.ParentId != 0 {
			parent, err := s.repo.FindByID(ctx, *command.ParentId)
			if err != nil {
				return err
			}
			if parent == nil {
				return errors.New("父执法类型不存在")
			}
			if *command.ParentId == command.ID {
				return errors.New("父节点ID不能设置为当前节点的ID")
			}
			// 检查父节点不能是当前节点的后代
			children, err := s.repo.FindChildren(ctx, command.ID)
			if err != nil {
				return err
			}
			for _, child := range children {
				if child.ID == *command.ParentId {
					return errors.New("父节点不能设置为当前节点的后代")
				}
			}
			updates["ParentId"] = *command.ParentId
		}
		updates["ParentId"] = *command.ParentId
	}

	if command.Source != nil {
		updates["Source"] = *command.Source
	}
	if command.Sort != nil {
		updates["Sort"] = *command.Sort
	}
	updates["UpdateBy"] = command.UpdateBy

	// 创建新的领域模型实例
	enforceType := &enforcementtype.EnforcementType{}

	// 使用领域模型的方法更新并验证业务规则
	if err := enforceType.UpdateWithFields(updates, originalDomain); err != nil {
		return err
	}

	// 在事务中执行更新操作
	err = transaction.RunInTransaction(ctx, s.tm, func(tx transaction.Transaction) error {
		// 检查是否需要更新路径
		if enforceType.NeedsPathUpdate(originalDomain) {
			if enforceType.ParentId > 0 {
				parent, err := s.repo.FindByID(ctx, enforceType.ParentId)
				if err != nil {
					return err
				}
				if parent == nil {
					return errors.New("父执法类型不存在")
				}
				enforceType.UpdatePath(parent.EnforcementTypePath)
			} else {
				enforceType.UpdatePath("")
			}

			// 更新所有后代节点的路径
			if err := s.updateChildrenPaths(ctx, tx, enforceType.ID, enforceType.EnforcementTypePath); err != nil {
				return err
			}
		}

		// 更新执法类型
		if err := s.repo.Update(ctx, tx, enforceType); err != nil {
			return err
		}

		// 发布事件
		return s.publishEvents(ctx, enforceType)
	})

	return err
}

// Delete 删除执法类型
func (s *enforcementTypeService) DeleteEnforcementTypeByID(ctx context.Context, id int64) error {
	// 检查是否存在
	existing, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if existing == nil {
		return errors.New("要删除的执法类型不存在")
	}

	// 检查是否有子节点
	children, err := s.repo.ListByParentID(ctx, id)
	if err != nil {
		return err
	}
	if len(children) > 0 {
		return errors.New("存在子节点，无法删除")
	}

	existing.Delete() //创建删除领域事件

	// 在事务中执行删除操作
	err = transaction.RunInTransaction(ctx, s.tm, func(tx transaction.Transaction) error {
		// 删除执法类型
		if err := s.repo.Delete(ctx, tx, id); err != nil {
			return err
		}

		// 发布事件
		return s.publishEvents(ctx, existing)
	})

	return err
}

// createCommandToDomain 将创建命令转换为领域模型
func (s *enforcementTypeService) createCommandToDomain(command *command.CreateEnforcementTypeCommand) *enforcementtype.EnforcementType {
	enforceType := &enforcementtype.EnforcementType{
		EnforcementTypeCode: command.EnforcementTypeCode,
		EnforcementTypeName: command.EnforcementTypeName,
		EnforcementTypeDesc: command.EnforcementTypeDesc,
		EnforcementTypePath: command.EnforcementTypePath,
		ParentId:            command.ParentId,
		Source:              command.Source,
		Sort:                command.Sort,
	}
	enforceType.SetCreateBy(command.CreateBy)
	return enforceType
}

// publishEvents 发布事件
func (s *enforcementTypeService) publishEvents(ctx context.Context, enforcementType *enforcementtype.EnforcementType) error {
	for _, event := range enforcementType.Events() {
		// 从上下文中获取租户ID
		tenantID := ctx.Value(global.TenantIDKey)
		if tenantID == nil {
			return fmt.Errorf("无法从上下文中获取租户ID")
		}
		event.SetTenantId(tenantID.(string))
		if err := s.eventPublisher.Publish(ctx, eventbus.EnforcementTypeEventTopic, event); err != nil {
			// 发布失败要持久化领域事件，一般来说多节点部署的kafka不应该同时故障
			if domainEvent, ok := event.(*domain_event.DomainEvent); ok {
				fmt.Printf("Pointer conversion successful: %v\n", domainEvent.EventID)
				err = s.eventRepo.Save(ctx, domainEvent)
				if err != nil {
					return fmt.Errorf("领域事件数据库持久化失败! %s", err)
				}
			} else {
				return fmt.Errorf("event conversion failed! %+v", event)
			}

			return fmt.Errorf("发布事件失败: %v", err)
		}
		s.GetLogger(ctx).Info("发布事件成功", zap.Any("event", event))
	}
	enforcementType.ClearEvents() // 发布成功，清空事件
	return nil
}

// updateChildrenPaths 递归更新所有后代节点的路径
func (s *enforcementTypeService) updateChildrenPaths(ctx context.Context, tx transaction.Transaction, parentID int64, parentPath string) error {
	// 获取直接子节点
	children, err := s.repo.ListByParentID(ctx, parentID)
	if err != nil {
		return err
	}

	// 没有子节点，直接返回
	if len(children) == 0 {
		return nil
	}

	// 更新每个子节点及其后代
	for _, child := range children {
		childDomain := &enforcementtype.EnforcementType{
			ID:                  child.ID,
			EnforcementTypeCode: child.EnforcementTypeCode,
			EnforcementTypeName: child.EnforcementTypeName,
			EnforcementTypeDesc: child.EnforcementTypeDesc,
			EnforcementTypePath: child.EnforcementTypePath,
			ParentId:            child.ParentId,
			Source:              child.Source,
			Sort:                child.Sort,
			ControlBy:           child.ControlBy,
			ModelTime:           child.ModelTime,
		}
		// 设置更新时间为当前时间
		childDomain.UpdatedAt = time.Now()

		// 更新子节点路径
		childDomain.UpdatePath(parentPath)
		if err := s.repo.Update(ctx, tx, childDomain); err != nil {
			return err
		}

		// 递归更新该子节点的所有后代
		if err := s.updateChildrenPaths(ctx, tx, childDomain.ID, childDomain.EnforcementTypePath); err != nil {
			return err
		}
	}

	return nil
}
