package repository

import (
	"context"
	"jxt-evidence-system/evidence-management/query/internal/models"
)

// ArchiveMediaRelationReadRepository 档案媒体关联查询仓储接口
type ArchiveMediaRelationReadRepository interface {
	// Create 创建档案媒体关联查询模型
	Create(ctx context.Context, relation *models.ArchiveMediaRelationReadModel) error

	// Update 更新档案媒体关联查询模型
	Update(ctx context.Context, relation *models.ArchiveMediaRelationReadModel) error

	// Delete 删除档案媒体关联查询模型
	Delete(ctx context.Context, id int64) error

	// FindByID 根据ID查找档案媒体关联
	FindByID(ctx context.Context, id int64) (*models.ArchiveMediaRelationReadModel, error)

	// FindByArchiveID 根据档案ID查找关联的媒体列表
	FindByArchiveID(ctx context.Context, archiveId int64, page, pageSize int) (*models.ArchiveMediaRelationReadModelList, error)

	// FindByMediaID 根据媒体ID查找关联的档案列表
	FindByMediaID(ctx context.Context, mediaId int64, page, pageSize int) (*models.ArchiveMediaRelationReadModelList, error)

	// FindByArchiveAndMedia 查找特定档案和媒体的关联关系
	FindByArchiveAndMedia(ctx context.Context, archiveId, mediaId int64) (*models.ArchiveMediaRelationReadModel, error)

	// FindWithPagination 分页查询档案媒体关联列表
	FindWithPagination(ctx context.Context, page, pageSize int, filters map[string]interface{}) (*models.ArchiveMediaRelationReadModelList, error)

	// CountByArchiveID 统计档案关联的媒体数量
	CountByArchiveID(ctx context.Context, archiveId int64) (int64, error)

	// CountByMediaID 统计媒体关联的档案数量
	CountByMediaID(ctx context.Context, mediaId int64) (int64, error)

	// GetArchiveMediaSummary 获取档案媒体关联汇总信息（基于稳定字段）
	GetArchiveMediaSummary(ctx context.Context, archiveId int64) (*models.ArchiveMediaRelationSummary, error)

	// GetMediaArchiveSummary 获取媒体档案关联汇总信息
	GetMediaArchiveSummary(ctx context.Context, mediaId int64) (*models.MediaArchiveRelationSummary, error)

	// BatchCreate 批量创建档案媒体关联查询模型
	BatchCreate(ctx context.Context, relations []*models.ArchiveMediaRelationReadModel) error

	// BatchDelete 批量删除档案媒体关联查询模型
	BatchDelete(ctx context.Context, ids []int64) error

	// BatchDeleteByArchiveID 根据档案ID批量删除关联关系
	BatchDeleteByArchiveID(ctx context.Context, archiveId int64) error

	// BatchDeleteByMediaID 根据媒体ID批量删除关联关系
	BatchDeleteByMediaID(ctx context.Context, mediaId int64) error

	// FindMediaTypeStatsByArchive 根据档案ID统计不同媒体类型的数量（基于MediaCate字段）
	FindMediaTypeStatsByArchive(ctx context.Context, archiveId int64) (map[int]int, error)

	// FindRecentRelations 查找最近创建的关联关系
	FindRecentRelations(ctx context.Context, limit int) ([]*models.ArchiveMediaRelationReadModel, error)

	// 注意：以下高级查询功能建议在业务服务层实现，结合媒体和档案读模型
	// FindOrphanedArchives - 查找没有关联媒体的档案（建议在服务层实现）
	// FindOrphanedMedia - 查找没有关联档案的媒体（建议在服务层实现）
	// FindLockedMediaByArchive - 查找档案下锁定的媒体（需要查询媒体读模型）
	// FindImportantMediaByArchive - 查找档案下重要的媒体（需要查询媒体读模型）
}
