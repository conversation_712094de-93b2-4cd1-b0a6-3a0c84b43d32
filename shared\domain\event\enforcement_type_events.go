package event

import (
	"jxt-evidence-system/evidence-management/shared/common/models"
)

// 执法类型事件类型定义
const (
	EventTypeEnforcementTypeCreated = "EnforcementTypeCreated"
	EventTypeEnforcementTypeUpdated = "EnforcementTypeUpdated"
	EventTypeEnforcementTypeDeleted = "EnforcementTypeDeleted"
)

// 具体领域事件结构体
type EnforcementTypeCreatedPayload struct {
	EnforcementTypeID   int64            `json:"enforcementTypeId"`
	EnforcementTypeCode string           `json:"enforcementTypeCode"`
	EnforcementTypeName string           `json:"enforcementTypeName"`
	EnforcementTypeDesc string           `json:"enforcementTypeDesc"`
	EnforcementTypePath string           `json:"enforcementTypePath"`
	ParentID            int64            `json:"parentId"`
	Source              string           `json:"source"`
	Sort                int              `json:"sort"`
	ControlBy           models.ControlBy `json:"controlBy"`
	ModelTime           models.ModelTime `json:"modelTime"`
}

type EnforcementTypeUpdatedPayload struct {
	EnforcementTypeID int64                  `json:"enforcementTypeId"`
	UpdatedFields     map[string]interface{} `json:"updatedFields"`
}

type EnforcementTypeDeletedPayload struct {
	EnforcementTypeID int64 `json:"enforcementTypeId"`
}
