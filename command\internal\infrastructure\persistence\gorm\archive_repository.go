package persistence

import (
	"context"
	"errors"
	"fmt"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archive"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archive/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"gorm.io/gorm"
)

// 添加通过依赖注入创建service
func init() {
	registrations = append(registrations, registerArchiveRepoDependencies)
}

// GormArchiveRepository的依赖注入
func registerArchiveRepoDependencies() {
	if err := di.Provide(func() repository.ArchiveRepository {
		return &gormArchiveRepository{}
	}); err != nil {
		logger.Fatalf("failed to provide GormArchiveRepository: %v", err)
	}
}

type gormArchiveRepository struct {
	GormRepository
}

func (repo *gormArchiveRepository) FindByID(ctx context.Context, id int64) (*archive.Archive, error) {
	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}
	var model archive.Archive
	db = db.WithContext(ctx).First(&model, id)
	err = db.Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看档案不存在")
		return nil, err
	}
	if err = db.Error; err != nil {
		return nil, err
	}
	return &model, nil
}

func (repo *gormArchiveRepository) FindByCode(ctx context.Context, code string) (*archive.Archive, error) {
	var count int64

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}
	var model archive.Archive
	db = db.WithContext(ctx).Model(&model).Where("archive_code = ?", code).Count(&count)
	if err = db.Error; err != nil {
		return nil, err
	}
	if count > 0 {
		return &model, nil
	}
	return nil, nil
}

// FindMaxCodeByPrefix 方法已移除 - 使用UUIDv7后不再需要查询最大编码

func (repo *gormArchiveRepository) Create(ctx context.Context, model *archive.Archive) error {
	var count int64

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return err
	}
	db = db.WithContext(ctx).Model(model).Where("archive_code = ?", model.ArchiveCode).Count(&count)
	if err = db.Error; err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("当前Archive[%s]已经存在！", model.ArchiveCode)
	}
	return db.WithContext(ctx).Create(model).Error
}

func (repo *gormArchiveRepository) UpdateByID(ctx context.Context, id int64, updates map[string]interface{}) error {
	var err error

	// 检查 updates 是否为空
	if len(updates) == 0 {
		return errors.New("no updates provided")
	}

	data := archive.Archive{ID: id}

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return err
	}
	// Updates 默认情况下会更新结构体中所有非零值字段。
	// 如果你只需要更新对象的部分字段，使用 Updates 会更高效，因为它只会生成针对指定字段的 UPDATE 语句，减少了数据库的负担。
	db = db.WithContext(ctx).Model(&data).Updates(updates)
	if err = db.Error; err != nil {
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("要更新的档案不存在")
	}
	return nil
}

func (repo *gormArchiveRepository) DeleteByID(ctx context.Context, id int64) error {
	// 获取数据库连接
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return err
	}

	// 执行删除操作
	result := db.WithContext(ctx).Delete(&archive.Archive{}, id)
	if result.Error != nil {
		return result.Error
	}

	// 检查是否有记录被删除
	if result.RowsAffected == 0 {
		return errors.New("要删除的档案不存在")
	}

	return nil
}

func (repo *gormArchiveRepository) BatchUpdateByIDs(ctx context.Context, ids []int64, updates map[string]interface{}) (int64, error) {
	var err error

	// 检查参数有效性
	if len(ids) == 0 {
		return 0, nil // 空切片，无需更新
	}
	if len(updates) == 0 {
		return 0, errors.New("no updates provided")
	}

	// 获取数据库连接
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return 0, err
	}

	// 执行批量更新 - 修复列名使用archive_id而不是id
	db = db.WithContext(ctx).Model(&archive.Archive{}).Where("archive_id IN ?", ids).Updates(updates)
	if err = db.Error; err != nil {
		return 0, err
	}

	// 检查是否有记录被更新
	if db.RowsAffected == 0 {
		return 0, errors.New("要更新的档案不存在")
	}

	return db.RowsAffected, nil
}

func (repo *gormArchiveRepository) BatchDeleteByIDs(ctx context.Context, ids []int64) (int64, error) {
	var err error

	if len(ids) == 0 {
		return 0, nil // 空切片，无需删除
	}

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return 0, err
	}
	db = db.WithContext(ctx).Delete(&archive.Archive{}, ids) //ids可以是数组
	if err = db.Error; err != nil {
		return 0, err
	}
	// ✅ 检查是否有记录被删除
	if db.RowsAffected == 0 {
		err = errors.New("要删除的档案不存在")
		return 0, err
	}
	return db.RowsAffected, nil
}
