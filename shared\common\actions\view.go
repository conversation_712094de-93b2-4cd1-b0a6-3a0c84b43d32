package actions

import (
	"errors"
	"net/http"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/response"
	"go.uber.org/zap"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"jxt-evidence-system/evidence-management/shared/common/models"
	"jxt-evidence-system/evidence-management/shared/common/query"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

// ViewAction 通用详情动作
func ViewAction(control query.Control, f func() interface{}) gin.HandlerFunc {
	return func(c *gin.Context) {
		log := logger.GetRequestLogger(c)
		db, err := pkg.GetOrm(c)
		if err != nil {
			log.Error("get db failure", zap.Error(err))
			return
		}

		msgID := pkg.GenerateMsgIDFromContext(c)
		//查看详情
		req := control.Generate()
		err = req.Bind(c)
		if err != nil {
			response.Error(c, http.StatusUnprocessableEntity, err, "参数验证失败")
			return
		}
		var object models.ActiveRecord
		object, err = req.GenerateM()
		if err != nil {
			response.Error(c, 500, err, "模型生成失败")
			return
		}

		var rsp interface{}
		if f != nil {
			rsp = f()
		} else {
			rsp, _ = req.GenerateM()
		}

		//数据权限检查
		p := GetPermissionFromContext(c)

		err = db.Model(object).WithContext(c).Scopes(
			Permission(object.TableName(), p),
		).Where(req.GetId()).First(rsp).Error

		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			response.Error(c, http.StatusNotFound, nil, "查看对象不存在或无权查看")
			return
		}
		if err != nil {
			log.Error("View error", zap.Error(err), zap.String("msgID", msgID))
			response.Error(c, 500, err, "查看失败")
			return
		}
		response.OK(c, rsp, "查询成功")
		c.Next()
	}
}
