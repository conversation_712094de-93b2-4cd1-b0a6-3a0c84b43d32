package persistence

/*
需求1：增加操作数据库的超时控制
func (s *UserService) CreateUser(ctx context.Context, user repository.User) error {
	return r.db.WithContext(ctx).Create(&user).Error
}
func HandlerFunc(c *gin.Context) {
    db, _ := gorm.Open(sqlite.Open("test.db"), &gorm.Config{})
    ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
    defer cancel()

    repo := NewUserRepository(db)
    user := User{Name: "<PERSON>", Age: 30}
    if err := repo.Create(ctx, user); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Error creating user"})
        return
    }

    c.JSO<PERSON>(http.StatusOK, gin.H{"message": "User created successfully"})
}

需求2：增加操作数据库的追踪ID或用户ID传递
func HandlerFunc(c *gin.Context) {
    db, _ := gorm.Open(sqlite.Open("test.db"), &gorm.Config{})
    ctx := context.WithValue(c.Request.Context(), "traceID", "1234567890")

    traceID := ctx.Value("traceID").(string)
    fmt.Printf("TraceID: %s - Creating user\n", traceID)

    repo := NewUserRepository(db)
    user := User{Name: "John Doe", Age: 30}
    if err := repo.Create(ctx, user); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Error creating user"})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "User created successfully"})
}
需求3：按照单一职责repo去掉日志打印，只需要返回error给application service
需求4：用单例模式实现mediaRepo，所哟mediaService 共用这个单例mediaRepo
*/
import (
	"context"
	"errors"
	"fmt"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/media"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/media/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"gorm.io/gorm"
)

// jiyuanjie 添加通过依赖注入创建service
func init() {
	registrations = append(registrations, registerMediaRepoDependencies)
}

// jiyuanjie GormMediaReadModelRepository的依赖注入
func registerMediaRepoDependencies() {
	if err := di.Provide(func() repository.MediaRepository {
		return &gormMediaRepository{}
	}); err != nil {
		logger.Fatalf("failed to provide GormMediaReadModelRepository: %v", err)
	}
}

type gormMediaRepository struct {
	GormRepository
}

func (repo *gormMediaRepository) FindByID(ctx context.Context, id int64) (*media.Media, error) {

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}
	var model media.Media
	db = db.WithContext(ctx).First(&model, id)
	err = db.Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查看媒体不存在")
		return nil, err
	}
	if err = db.Error; err != nil {
		return nil, err
	}
	return &model, nil
}

func (repo *gormMediaRepository) FindByName(ctx context.Context, name string) (*media.Media, error) {

	var count int64

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}
	var model media.Media
	db = db.WithContext(ctx).Model(&model).Where("media_name = ?", name).Count(&count)
	if err = db.Error; err != nil {
		return nil, err
	}
	if count > 0 {
		return &model, nil
	}
	return nil, nil
}

func (repo *gormMediaRepository) Create(ctx context.Context, model *media.Media) error {
	var count int64

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return err
	}
	db = db.WithContext(ctx).Model(model).Where("media_name = ?", model.MediaName).Count(&count)
	if err = db.Error; err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("当前Media[%s]已经存在！", model.MediaName)
	}
	return db.WithContext(ctx).Create(model).Error
}

func (repo *gormMediaRepository) UpdateByID(ctx context.Context, id int64, updates map[string]interface{}) error {
	var err error

	// // 类型断言并检查
	// mediaID, ok := id.(int64)
	// if !ok {
	// 	return errors.New("invalid ID type; expected int64")
	// }

	// 检查 updates 是否为空
	if len(updates) == 0 {
		return errors.New("no updates provided")
	}

	data := media.Media{ID: id}

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return err
	}
	// Updates 默认情况下会更新结构体中所有非零值字段。
	// 如果你只需要更新对象的部分字段，使用 Updates 会更高效，因为它只会生成针对指定字段的 UPDATE 语句，减少了数据库的负担。
	db = db.WithContext(ctx).Model(&data).Updates(updates)
	if err = db.Error; err != nil {
		return err
	}
	if db.RowsAffected == 0 {
		return errors.New("要更新的媒体不存在")
	}
	return nil
}

func (repo *gormMediaRepository) DeleteByID(ctx context.Context, id int64) error {
	// 获取数据库连接
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return err
	}

	// 执行删除操作
	result := db.WithContext(ctx).Delete(&media.Media{}, id)
	if result.Error != nil {
		return result.Error
	}

	// 检查是否有记录被删除
	if result.RowsAffected == 0 {
		return errors.New("要删除的媒体不存在")
	}

	return nil
}

func (repo *gormMediaRepository) BatchUpdateByIDs(ctx context.Context, ids []int64, updates map[string]interface{}) (int64, error) {
	// 需要验证ids是否为切片类型
	// v := reflect.ValueOf(ids)
	// if v.Kind() != reflect.Slice {
	// 	return 0, errors.New("ids must be a slice")
	// }

	if len(ids) == 0 {
		return 0, nil // 空切片，无需更新
	}

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return 0, err
	}

	db = db.WithContext(ctx).Model(&media.Media{}).
		Where("media_id IN ?", ids).
		Updates(updates)

	return db.RowsAffected, db.Error
}

func (repo *gormMediaRepository) BatchDeleteByIDs(ctx context.Context, ids []int64) (int64, error) {
	var err error

	// // 需要验证ids是否为切片类型
	// v := reflect.ValueOf(ids)
	// if v.Kind() != reflect.Slice {
	// 	return 0, errors.New("ids must be a slice")
	// }

	if len(ids) == 0 {
		return 0, nil // 空切片，无需更新
	}

	// 不用logger异常打印，而是把err返回service，由service打印日志
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return 0, err
	}
	db = db.WithContext(ctx).Delete(&media.Media{}, ids) //ids可以是数组
	if err = db.Error; err != nil {
		return 0, err
	}
	// ✅ 检查是否有记录被删除
	if db.RowsAffected == 0 {
		err = errors.New("要删除的媒体不存在")
		return 0, err
	}
	return db.RowsAffected, nil
}
