package config

// Extend 扩展配置
//
//	extend:
//	  demo:
//	    name: demo-name
//
// 使用方法： config.ExtendConfig.(*ext.Extend).Demo.MaxAge......即可！！
type Extend struct {
	//Logger ZapLogger // 这里配置对应配置文件的结构即可
	// 添加 Kafka 配置
	//Kafka KafkaConfig2
}

type ZapLogger struct {
	Path            string
	MaxSize         int
	ErrorMaxAge     int
	InfoMaxAge      int
	GormLoggerLevel int
}

// 新增 KafkaConfig 结构体
type KafkaConfig2 struct {
	Brokers             []string
	HealthCheckInterval int // 健康检测的时间间隔，整数，单位分钟
}

var ExtConfig Extend

func init() {
	ExtConfig = Extend{
		//Logger: ZapLogger{
		//	Path:            "/temp/logs", // 默认配置
		//	MaxSize:         50,           // 默认配置
		//	ErrorMaxAge:     14,           // 默认配置
		//	InfoMaxAge:      3,            // 默认配置
		//	GormLoggerLevel: 1,            // 默认配置
		//},
		//Kafka: KafkaConfig2{
		//	Brokers:             []string{"kafka:9092"}, // 修改为切片类型
		//	HealthCheckInterval: 5,                      // 缺省5分钟做一次健康检查
		//},
	}
}
