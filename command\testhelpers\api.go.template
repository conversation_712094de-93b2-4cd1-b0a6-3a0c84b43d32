package apis

import (
"github.com/gin-gonic/gin"
"github.com/gin-gonic/gin/binding"
"go-admin/models"
"github.com/ChenBigdata421/jxt-core/sdk/pkg"
"go-admin/utils"
"net/http"
)

// @Summary 配置列表数据
// @Description 获取JSON
// @Tags 配置
// @Param configKey query string false "configKey"
// @Param configName query string false "configName"
// @Param configType query string false "configType"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} app.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/configList [get]
// @Security Bearer
func Get{{.ClassName}}List(c *gin.Context) {
var data models.{{.ClassName}}
var err error
var pageSize = 10
var pageIndex = 1

if size := c.Request.FormValue("pageSize"); size != "" {
pageSize = pkg.StrToInt(err, size)
}

if index := c.Request.FormValue("pageIndex"); index != "" {
pageIndex = pkg.StrToInt(err, index)
}

{{ range .Columns -}}
    {{$z := .IsQuery}}
    {{- if ($z) -}}
        data.{{.GoField}} = c.Request.FormValue("{{.JsonField}}")
    {{ end }}
{{- end -}}

data.DataScope = utils.GetUserIdStr(c)
result, count, err := data.GetPage(pageSize, pageIndex)
pkg.HasError(err, "", -1)

var mp = make(map[string]interface{}, 3)
mp["list"] = result
mp["count"] = count
mp["pageIndex"] = pageIndex
mp["pageIndex"] = pageSize

var res app.Response
res.Data = mp

c.JSON(http.StatusOK, res.ReturnOK())
}

// @Summary 获取配置
// @Description 获取JSON
// @Tags 配置
// @Param configId path int true "配置编码"
// @Success 200 {object} app.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/config/{configId} [get]
// @Security Bearer
func Get{{.ClassName}}(c *gin.Context) {
var data models.{{.ClassName}}
data.{{.PkGoField}}, _ = utils.StringToInt(c.Param("{{.PkJsonField}}"))
result, err := data.Get()
pkg.HasError(err, "抱歉未找到相关信息", -1)

var res app.Response
res.Data = result

c.JSON(http.StatusOK, res.ReturnOK())
}

// @Summary 添加配置
// @Description 获取JSON
// @Tags 配置
// @Accept  application/json
// @Product application/json
// @Param data body models.{{.ClassName}} true "data"
// @Success 200 {string} string    "{"code": 200, "message": "添加成功"}"
// @Success 200 {string} string    "{"code": -1, "message": "添加失败"}"
// @Router /api/v1/dict/data [post]
// @Security Bearer
func Insert{{.ClassName}}(c *gin.Context) {
var data models.{{.ClassName}}
err := c.BindWith(&data, binding.JSON)
data.CreateBy = utils.GetUserIdStr(c)
pkg.HasError(err, "", 500)
result, err := data.Create()
pkg.HasError(err, "", -1)

var res app.Response
res.Data = result
c.JSON(http.StatusOK, res.ReturnOK())

}

func Update{{.ClassName}}(c *gin.Context) {
var data models.{{.ClassName}}
err := c.BindWith(&data, binding.JSON)
pkg.HasError(err, "数据解析失败", -1)
data.UpdateBy = utils.GetUserIdStr(c)
result, err := data.Update(data.{{.PkGoField}})
pkg.HasError(err, "", -1)

var res app.Response
res.Data = result
c.JSON(http.StatusOK, res.ReturnOK())
}

func Delete{{.ClassName}}(c *gin.Context) {
var data models.{{.ClassName}}
id, err := utils.StringToInt(c.Param("{{.PkJsonField}}"))
data.UpdateBy = utils.GetUserIdStr(c)
_, err = data.Delete(id)
pkg.HasError(err, "修改失败", 500)

var res app.Response
res.Msg = "删除成功"
c.JSON(http.StatusOK, res.ReturnOK())
}
