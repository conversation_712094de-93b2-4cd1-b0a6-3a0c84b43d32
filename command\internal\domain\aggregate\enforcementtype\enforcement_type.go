package enforcementtype

import (
	"jxt-evidence-system/evidence-management/shared/common/models"
	"jxt-evidence-system/evidence-management/shared/domain/event"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"errors"

	jsoniter "github.com/json-iterator/go"
)

type EnforcementType struct {
	ID                  int64  `json:"id" gorm:"primaryKey;column:enforcement_type_id;autoIncrement;comment:主键ID"` //主键ID
	EnforcementTypeCode string `json:"enforcementTypeCode" gorm:"column:enforcement_type_code;comment:执行类型编码"`     //执行类型编码
	EnforcementTypeName string `json:"enforcementTypeName" gorm:"column:enforcement_type_name;comment:执行类型名称"`     //执行类型名称
	EnforcementTypeDesc string `json:"enforcementTypeDesc" gorm:"column:enforcement_type_desc;comment:执行类型描述"`     //执行类型描述
	EnforcementTypePath string `json:"enforcementTypePath" gorm:"column:enforcement_type_path;comment:执法类型路径"`     //
	ParentId            int64  `json:"parentId" gorm:"column:parent_id;comment:父级Id"`                              //上级部门
	Source              string `json:"source" gorm:"column:source;comment:执法类型来源"`
	Sort                int    `json:"sort" gorm:"size:4;comment:排序"` //排序
	models.ControlBy
	models.ModelTime
	DataScope string             `json:"dataScope" gorm:"-"`
	Params    string             `json:"params" gorm:"-"`
	Children  []*EnforcementType `json:"children" gorm:"-"`
	events    []event.Event      `gorm:"-"`
}

func (*EnforcementType) TableName() string {
	return "t_evidence_enforcement_types"
}

func (e *EnforcementType) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *EnforcementType) GetId() interface{} {
	return e.ID
}

// Event handling methods
func (e *EnforcementType) AddEvent(evt event.Event) {
	e.events = append(e.events, evt)
}

func (e *EnforcementType) Events() []event.Event {
	return e.events
}

func (e *EnforcementType) ClearEvents() {
	e.events = []event.Event{}
}

// Domain behavior methods
func (e *EnforcementType) Create() {
	e.AddEvent(e.createCreatedEvent())
}

func (e *EnforcementType) Update(original *EnforcementType) {
	e.AddEvent(e.createUpdatedEvent(original))
}

func (e *EnforcementType) Delete() {
	e.AddEvent(e.createDeletedEvent())
}

// Event creation methods
func (e *EnforcementType) createCreatedEvent() *event.DomainEvent {
	payload := &event.EnforcementTypeCreatedPayload{
		EnforcementTypeID:   e.ID,
		EnforcementTypeCode: e.EnforcementTypeCode,
		EnforcementTypeName: e.EnforcementTypeName,
		EnforcementTypeDesc: e.EnforcementTypeDesc,
		EnforcementTypePath: e.EnforcementTypePath,
		ParentID:            e.ParentId,
		Source:              e.Source,
		Sort:                e.Sort,
		ControlBy:           e.ControlBy,
		ModelTime:           e.ModelTime,
	}

	payloadJSON, err := jsoniter.Marshal(payload)
	if err != nil {
		logger.Error("序列化创建事件负载失败", "error", err)
		return nil
	}

	return event.NewDomainEvent(event.EventTypeEnforcementTypeCreated, e.ID, "EnforcementType", payloadJSON)
}

func (e *EnforcementType) createUpdatedEvent(original *EnforcementType) *event.DomainEvent {
	updatedFields := make(map[string]interface{})

	// 直接检查关键字段，避免使用反射可能导致的问题
	if e.EnforcementTypeCode != original.EnforcementTypeCode {
		updatedFields["EnforcementTypeCode"] = e.EnforcementTypeCode
	}
	if e.EnforcementTypeName != original.EnforcementTypeName {
		updatedFields["EnforcementTypeName"] = e.EnforcementTypeName
	}
	if e.EnforcementTypeDesc != original.EnforcementTypeDesc {
		updatedFields["EnforcementTypeDesc"] = e.EnforcementTypeDesc
	}
	if e.EnforcementTypePath != original.EnforcementTypePath {
		updatedFields["EnforcementTypePath"] = e.EnforcementTypePath
	}
	if e.ParentId != original.ParentId {
		updatedFields["ParentId"] = e.ParentId
	}
	if e.Source != original.Source {
		updatedFields["Source"] = e.Source
	}
	if e.Sort != original.Sort {
		updatedFields["Sort"] = e.Sort
	}
	if e.UpdateBy != original.UpdateBy {
		updatedFields["UpdateBy"] = e.UpdateBy
	}

	// 确保更新时间总是被包含在更新字段中
	updatedFields["UpdatedAt"] = time.Now().Format("2006-01-02 15:04:05")

	payload := &event.EnforcementTypeUpdatedPayload{
		EnforcementTypeID: e.ID,
		UpdatedFields:     updatedFields,
	}

	payloadJSON, err := jsoniter.Marshal(payload)
	if err != nil {
		logger.Error("序列化更新事件负载失败", "error", err)
		return nil
	}

	return event.NewDomainEvent(event.EventTypeEnforcementTypeUpdated, e.ID, "EnforcementType", payloadJSON)
}

func (e *EnforcementType) createDeletedEvent() *event.DomainEvent {
	payload := &event.EnforcementTypeDeletedPayload{
		EnforcementTypeID: e.ID,
	}

	payloadJSON, err := jsoniter.Marshal(payload)
	if err != nil {
		logger.Error("序列化删除事件负载失败", "error", err)
		return nil
	}

	return event.NewDomainEvent(event.EventTypeEnforcementTypeDeleted, e.ID, "EnforcementType", payloadJSON)
}

// UpdatePath 更新执法类型路径
func (e *EnforcementType) UpdatePath(parentPath string) {
	if parentPath == "" {
		e.EnforcementTypePath = "/" + e.EnforcementTypeCode
	} else {
		e.EnforcementTypePath = parentPath + "/" + e.EnforcementTypeCode
	}
}

// NeedsPathUpdate 检查是否需要更新路径
func (e *EnforcementType) NeedsPathUpdate(original *EnforcementType) bool {
	return e.ParentId != original.ParentId || e.EnforcementTypeCode != original.EnforcementTypeCode
}

// Clone 创建当前对象的副本
func (e *EnforcementType) Clone() *EnforcementType {
	clone := &EnforcementType{
		ID:                  e.ID,
		EnforcementTypeCode: e.EnforcementTypeCode,
		EnforcementTypeName: e.EnforcementTypeName,
		EnforcementTypeDesc: e.EnforcementTypeDesc,
		EnforcementTypePath: e.EnforcementTypePath,
		ParentId:            e.ParentId,
		Source:              e.Source,
		Sort:                e.Sort,
		ControlBy:           e.ControlBy,
		ModelTime:           e.ModelTime,
		DataScope:           e.DataScope,
		Params:              e.Params,
	}

	// 复制子节点（如果有）
	if e.Children != nil {
		clone.Children = make([]*EnforcementType, len(e.Children))
		for i, child := range e.Children {
			clone.Children[i] = child.Clone()
		}
	}

	return clone
}

// UpdateWithFields 使用字段映射更新领域模型，并验证业务规则
func (e *EnforcementType) UpdateWithFields(updates map[string]interface{}, original *EnforcementType) error {
	// 复制原始值
	*e = *original

	// 验证业务规则，确保执法类型编码的不可改变性
	if code, ok := updates["EnforcementTypeCode"]; ok {
		if codeStr, ok := code.(string); ok && original.EnforcementTypeCode != "" && original.EnforcementTypeCode != codeStr {
			return errors.New("执法类型编码不可更改")
		}
		// 如果编码相同，则使用原始编码
		e.EnforcementTypeCode = original.EnforcementTypeCode
	}

	// 应用更新
	for field, value := range updates {
		switch field {
		case "ID":
			if id, ok := value.(int64); ok {
				e.ID = id
			}
		case "EnforcementTypeCode":
			if code, ok := value.(string); ok {
				e.EnforcementTypeCode = code
			}
		case "EnforcementTypeName":
			if name, ok := value.(string); ok {
				e.EnforcementTypeName = name
			}
		case "EnforcementTypeDesc":
			if desc, ok := value.(string); ok {
				e.EnforcementTypeDesc = desc
			}
		case "EnforcementTypePath":
			if path, ok := value.(string); ok {
				e.EnforcementTypePath = path
			}
		case "ParentId":
			if parentID, ok := value.(int64); ok {
				e.ParentId = parentID
			}
		case "Source":
			if source, ok := value.(string); ok {
				e.Source = source
			}
		case "Sort":
			if sort, ok := value.(int); ok {
				e.Sort = sort
			}
		case "UpdateBy":
			if updateBy, ok := value.(int); ok {
				e.SetUpdateBy(updateBy)
			}
		}
	}

	// 确保保留原始的创建时间
	e.CreatedAt = original.CreatedAt
	// 设置更新时间为当前时间
	e.UpdatedAt = time.Now()

	// 记录领域事件
	e.Update(original)

	return nil
}
