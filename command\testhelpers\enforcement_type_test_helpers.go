package testhelpers

import (
	"jxt-evidence-system/evidence-management/command/internal/application/command"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/enforcementtype"
)

// CreateEnforcementTypeCommandBuilder 创建执法类型构建器
type CreateEnforcementTypeCommandBuilder struct {
	command *command.CreateEnforcementTypeCommand
}

func NewCreateEnforcementTypeCommandBuilder() *CreateEnforcementTypeCommandBuilder {
	return &CreateEnforcementTypeCommandBuilder{
		command: &command.CreateEnforcementTypeCommand{
			EnforcementTypeCode: "执法类型1",
			EnforcementTypeName: "测试执法类型",
		},
	}
}

func (b *CreateEnforcementTypeCommandBuilder) WithCode(code string) *CreateEnforcementTypeCommandBuilder {
	b.command.EnforcementTypeCode = code
	return b
}

func (b *CreateEnforcementTypeCommandBuilder) WithName(name string) *CreateEnforcementTypeCommandBuilder {
	b.command.EnforcementTypeName = name
	return b
}

func (b *CreateEnforcementTypeCommandBuilder) WithDesc(comments string) *CreateEnforcementTypeCommandBuilder {
	b.command.EnforcementTypeDesc = comments
	return b
}

func (b *CreateEnforcementTypeCommandBuilder) WithParentId(parentId int64) *CreateEnforcementTypeCommandBuilder {
	b.command.ParentId = parentId
	return b
}

func (b *CreateEnforcementTypeCommandBuilder) WithSource(source string) *CreateEnforcementTypeCommandBuilder {
	b.command.Source = source
	return b
}

func (b *CreateEnforcementTypeCommandBuilder) WithSort(sort int) *CreateEnforcementTypeCommandBuilder {
	b.command.Sort = sort
	return b
}

func (b *CreateEnforcementTypeCommandBuilder) Build() *command.CreateEnforcementTypeCommand {
	return b.command
}

// UpdateEnforcementTypeCommandBuilder 更新执法类型构建器
type UpdateEnforcementTypeCommandBuilder struct {
	command *command.UpdateEnforcementTypeCommand
}

func NewUpdateEnforcementTypeCommandBuilder() *UpdateEnforcementTypeCommandBuilder {
	return &UpdateEnforcementTypeCommandBuilder{
		command: &command.UpdateEnforcementTypeCommand{},
	}
}

func (b *UpdateEnforcementTypeCommandBuilder) WithID(id int64) *UpdateEnforcementTypeCommandBuilder {
	b.command.ID = id
	return b
}

func (b *UpdateEnforcementTypeCommandBuilder) WithCode(code string) *UpdateEnforcementTypeCommandBuilder {
	b.command.EnforcementTypeCode = code
	return b
}

func (b *UpdateEnforcementTypeCommandBuilder) WithName(name string) *UpdateEnforcementTypeCommandBuilder {
	b.command.EnforcementTypeName = &name
	return b
}

func (b *UpdateEnforcementTypeCommandBuilder) WithDesc(comments string) *UpdateEnforcementTypeCommandBuilder {
	b.command.EnforcementTypeDesc = &comments
	return b
}

func (b *UpdateEnforcementTypeCommandBuilder) WithPath(path string) *UpdateEnforcementTypeCommandBuilder {
	b.command.EnforcementTypePath = &path
	return b
}

func (b *UpdateEnforcementTypeCommandBuilder) WithParentId(parentId int64) *UpdateEnforcementTypeCommandBuilder {
	b.command.ParentId = &parentId
	return b
}

func (b *UpdateEnforcementTypeCommandBuilder) WithSource(source string) *UpdateEnforcementTypeCommandBuilder {
	b.command.Source = &source
	return b
}

func (b *UpdateEnforcementTypeCommandBuilder) WithSort(sort int) *UpdateEnforcementTypeCommandBuilder {
	b.command.Sort = &sort
	return b
}

func (b *UpdateEnforcementTypeCommandBuilder) Build() *command.UpdateEnforcementTypeCommand {
	return b.command
}

// EnforcementTypeBuilder 执法类型构建器
type EnforcementTypeBuilder struct {
	enforcementType *enforcementtype.EnforcementType
}

func NewEnforcementTypeBuilder() *EnforcementTypeBuilder {
	return &EnforcementTypeBuilder{
		enforcementType: &enforcementtype.EnforcementType{},
	}
}

func (b *EnforcementTypeBuilder) WithName(name string) *EnforcementTypeBuilder {
	b.enforcementType.EnforcementTypeName = name
	return b
}

func (b *EnforcementTypeBuilder) WithDesc(comments string) *EnforcementTypeBuilder {
	b.enforcementType.EnforcementTypeDesc = comments
	return b
}

func (b *EnforcementTypeBuilder) Build() *enforcementtype.EnforcementType {
	return b.enforcementType
}
