package command

import (
	common "jxt-evidence-system/evidence-management/shared/common/models"
)

// CreateArchiveMediaRelationCommand 创建档案媒体关联命令
type CreateArchiveMediaRelationCommand struct {
	ArchiveId    int64  `json:"archiveId" binding:"required"`  // 档案ID，必填
	DocumentId   int64  `json:"documentId" binding:"required"` // 文档ID（媒体ID），必填
	RelationType string `json:"relationType"`                  // 关联类型，可选
	common.ControlBy
}

// DeleteArchiveMediaRelationCommand 删除档案媒体关联命令
type DeleteArchiveMediaRelationCommand struct {
	ID int64 `uri:"id" binding:"required"` // 关联ID，必填
	common.ControlBy
}

func (s *DeleteArchiveMediaRelationCommand) GetId() int64 {
	return s.ID
}

// BatchCreateArchiveMediaRelationCommand 批量创建档案媒体关联命令
type BatchCreateArchiveMediaRelationCommand struct {
	ArchiveId    int64   `json:"archiveId" binding:"required"`   // 档案ID，必填
	DocumentIds  []int64 `json:"documentIds" binding:"required"` // 文档ID列表，必填
	RelationType string  `json:"relationType"`                   // 关联类型，可选
	common.ControlBy
}

func (s *BatchCreateArchiveMediaRelationCommand) GetDocumentIds() []int64 {
	return s.DocumentIds
}

// BatchDeleteArchiveMediaRelationCommand 批量删除档案媒体关联命令
type BatchDeleteArchiveMediaRelationCommand struct {
	IDs []int64 `json:"ids" binding:"required"` // 关联ID列表，必填
	common.ControlBy
}

func (s *BatchDeleteArchiveMediaRelationCommand) GetIds() []int64 {
	return s.IDs
}
