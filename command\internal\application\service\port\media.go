package port

import (
	"context"
	"jxt-evidence-system/evidence-management/command/internal/application/command"
)

type MediaService interface {
	// 接收 上传的媒体文件 创建对象
	UploadMedia(ctx context.Context, r *command.UploadMediaCommand) error

	// 修改对象
	UpdateMediaByID(ctx context.Context, r *command.UpdateMediaCommand) error

	// 删除单个媒体
	DeleteMediaByID(ctx context.Context, id int64) error

	// 批量修改对象
	BatchUpdateMedia(ctx context.Context, r *command.BatchUpdateMediaCommand) error

	// 批量删除媒体
	BatchDeleteMedia(ctx context.Context, r *command.BatchDeleteMediaCommand) error

	// 批量设置媒体的是否执法视频状态
	BatchUpdateMediaNonEnforcementStatus(ctx context.Context, r *command.BatchUpdateNonEnforcementStatusCommand) error

	// 批量更新媒体执法类型
	BatchUpdateEnforceType(ctx context.Context, r *command.BatchUpdateEnforceTypeCommand) error

	// 批量更新媒体是否锁定状态
	BatchUpdateIsLocked(ctx context.Context, r *command.BatchUpdateIsLockedCommand) error
}
