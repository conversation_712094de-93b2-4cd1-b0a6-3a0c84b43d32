package models

import (
	"jxt-evidence-system/evidence-management/shared/common/models"
)

// 为了查询实时性，执法类型是直接查询命令数据库，所以直接用执法类型的领域模型来查询
type EnforcementType struct {
	ID                  int64  `json:"id" gorm:"primaryKey;column:enforcement_type_id;autoIncrement;comment:主键ID"` //主键ID
	EnforcementTypeCode string `json:"enforcementTypeCode" gorm:"column:enforcement_type_code;comment:执行类型编码"`     //执行类型编码
	EnforcementTypeName string `json:"enforcementTypeName" gorm:"column:enforcement_type_name;comment:执行类型名称"`     //执行类型名称
	EnforcementTypeDesc string `json:"enforcementTypeDesc" gorm:"column:enforcement_type_desc;comment:执行类型描述"`     //执行类型描述
	EnforcementTypePath string `json:"enforcementTypePath" gorm:"column:enforcement_type_path;comment:执法类型路径"`     //
	ParentId            int64  `json:"parentId" gorm:"column:parent_id;comment:父级Id"`                              //上级部门
	Source              string `json:"source" gorm:"column:source;comment:执法类型来源"`
	Sort                int    `json:"sort" gorm:"size:4;comment:排序"` //排序
	models.ControlBy
	models.ModelTime
	DataScope string             `json:"dataScope" gorm:"-"`
	Params    string             `json:"params" gorm:"-"`
	Children  []*EnforcementType `json:"children" gorm:"-"`
}

func (*EnforcementType) TableName() string {
	return "t_evidence_enforcement_types"
}

func (e *EnforcementType) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *EnforcementType) GetId() interface{} {
	return e.ID
}

// 为了查询实时性，执法类型是直接查询命令数据库，所以直接用执法类型的领域模型来查询，下面的读模型只是用于写入读模型数据库
type EnforcementTypeReadModel struct {
	ID                  int64  `json:"id" gorm:"primaryKey;column:enforcement_type_id;autoIncrement:false;comment:主键ID"` //主键ID
	EnforcementTypeCode string `json:"enforcementTypeCode" gorm:"column:enforcement_type_code;comment:执行类型编码"`           //执行类型编码
	EnforcementTypeName string `json:"enforcementTypeName" gorm:"column:enforcement_type_name;comment:执行类型名称"`           //执行类型名称
	EnforcementTypeDesc string `json:"enforcementTypeDesc" gorm:"column:enforcement_type_desc;comment:执行类型描述"`           //执行类型描述
	EnforcementTypePath string `json:"enforcementTypePath" gorm:"column:enforcement_type_path;comment:执法类型路径"`           //
	ParentId            int64  `json:"parentId" gorm:"column:parent_id;comment:父级Id"`                                    //上级部门
	Source              string `json:"source" gorm:"column:source;comment:执法类型来源"`
	Sort                int    `json:"sort" gorm:"size:4;comment:排序"` //排序
	models.ControlBy
	models.ModelTime
	DataScope string                      `json:"dataScope" gorm:"-"`
	Params    string                      `json:"params" gorm:"-"`
	Children  []*EnforcementTypeReadModel `json:"children" gorm:"-"`
}

func (*EnforcementTypeReadModel) TableName() string {
	return "t_evidence_enforcement_types_read"
}

// EnforcementTypeReadModelList 执法类型查询模型列表
type EnforcementTypeReadModelList struct {
	List  []*EnforcementTypeReadModel `json:"list"`
	Total int64                       `json:"total"`
}

func (e *EnforcementTypeReadModel) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *EnforcementTypeReadModel) GetId() interface{} {
	return e.ID
}
