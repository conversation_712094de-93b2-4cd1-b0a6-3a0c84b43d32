package router

import (
	"jxt-evidence-system/evidence-management/command/internal/interface/rest/api"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/middleware"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	jwt "github.com/ChenBigdata421/jxt-core/sdk/pkg/jwtauth"
	"github.com/gin-gonic/gin"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerArchiveMediaRelationRouter)
}

func registerArchiveMediaRelationRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	// 通过依赖注入创建API处理器
	err := di.Invoke(func(handler *api.ArchiveMediaRelationHandler) {
		if handler != nil {
			r := v1.Group("/archive-media-relations").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
			{
				// 创建单个关联
				r.POST("", handler.CreateArchiveMediaRelation)

				// 删除单个关联
				r.DELETE("/:id", handler.DeleteArchiveMediaRelation)

				// 批量创建关联
				r.POST("/batch", handler.BatchCreateArchiveMediaRelation)

				// 批量删除关联
				r.DELETE("/batch", handler.BatchDeleteArchiveMediaRelation)
			}
		} else {
			logger.Fatal("ArchiveMediaRelationHandler is nil after resolution")
		}
	})

	if err != nil {
		logger.Fatalf("Failed to resolve ArchiveMediaRelationHandler: %v", err)
	}
}
