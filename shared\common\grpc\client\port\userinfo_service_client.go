package client

import (
	"context"
	proto "jxt-evidence-system/evidence-management/shared/common/grpc/user/proto"
)

// UserInfoServiceClient 用户信息服务客户端接口
type UserInfoServiceClient interface {
	// GetUserById 根据用户ID查询用户信息
	GetUserById(ctx context.Context, tenantId string, userId int32) (*proto.UserInfoReply, error)

	// GetUserByPoliceNo 根据警号查询用户信息
	GetUserByPoliceNo(ctx context.Context, tenantId string, policeNo string) (*proto.UserInfoReply, error)
}