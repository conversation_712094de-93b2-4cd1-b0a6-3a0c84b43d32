package testhelpers

import (
	"jxt-evidence-system/evidence-management/query/internal/models"
	commonModels "jxt-evidence-system/evidence-management/shared/common/models"
	"time"
)

// MediaReadModelBuilder 媒体读模型构建器
type MediaReadModelBuilder struct {
	model *models.MediaReadModel
}

func NewMediaReadModelBuilder() *MediaReadModelBuilder {
	return &MediaReadModelBuilder{
		model: &models.MediaReadModel{
			// 默认值设置
			MediaName:     "测试媒体",
			MediaCate:     2, // 视频
			MediaSuffix:   "mp4",
			ShotTimeStart: time.Now(),
			ShotTime:      time.Now().Add(time.Hour),
			VideoClarity:  1, // 高清
			VideoDuration: 3600000,
			FileSize:      1024,

			// 存储相关默认值
			SiteNo:       "SITE001",
			SiteName:     "测试采集站",
			SiteHttp:     "http://localhost:8080",
			StorageIDStr: "STORAGE001",
			StorageHttp:  "http://storage.example.com",

			// 关联元数据默认值
			PoliceNo:     "POLICE001",
			PoliceName:   "张三",
			PoliceIdCard: "110101199001011234",
			OrgCode:      "ORG001",
			OrgName:      "测试单位",
			OrgJc:        "测试",

			ControlBy: commonModels.ControlBy{
				CreateBy: 1,
				UpdateBy: 1,
			},
			ModelTime: commonModels.ModelTime{
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
		},
	}
}

// 基础信息
func (b *MediaReadModelBuilder) WithID(id int64) *MediaReadModelBuilder {
	b.model.ID = id
	return b
}

func (b *MediaReadModelBuilder) WithMediaName(name string) *MediaReadModelBuilder {
	b.model.MediaName = name
	return b
}

// 业务元数据
func (b *MediaReadModelBuilder) WithMediaCate(cate int) *MediaReadModelBuilder {
	b.model.MediaCate = cate
	return b
}

func (b *MediaReadModelBuilder) WithMediaSuffix(suffix string) *MediaReadModelBuilder {
	b.model.MediaSuffix = suffix
	return b
}

func (b *MediaReadModelBuilder) WithShotTimeStart(t time.Time) *MediaReadModelBuilder {
	b.model.ShotTimeStart = t
	return b
}

func (b *MediaReadModelBuilder) WithShotTime(t time.Time) *MediaReadModelBuilder {
	b.model.ShotTime = t
	return b
}

func (b *MediaReadModelBuilder) WithVideoClarity(clarity int) *MediaReadModelBuilder {
	b.model.VideoClarity = clarity
	return b
}

func (b *MediaReadModelBuilder) WithVideoDuration(duration int) *MediaReadModelBuilder {
	b.model.VideoDuration = duration
	return b
}

func (b *MediaReadModelBuilder) WithImportantLevel(level int) *MediaReadModelBuilder {
	b.model.ImportantLevel = level
	return b
}

func (b *MediaReadModelBuilder) WithImportantLevelRec(level int) *MediaReadModelBuilder {
	b.model.ImportantLevelRec = level
	return b
}

func (b *MediaReadModelBuilder) WithWidth(width int) *MediaReadModelBuilder {
	b.model.Width = width
	return b
}

func (b *MediaReadModelBuilder) WithHeight(height int) *MediaReadModelBuilder {
	b.model.Height = height
	return b
}

// 业务标注数据
func (b *MediaReadModelBuilder) WithIsNonEnforcementMedia(isNonEnforcement int) *MediaReadModelBuilder {
	b.model.IsNonEnforcementMedia = isNonEnforcement
	return b
}

func (b *MediaReadModelBuilder) WithComments(comments string) *MediaReadModelBuilder {
	b.model.Comments = comments
	return b
}

func (b *MediaReadModelBuilder) WithSequence(sequence string) *MediaReadModelBuilder {
	b.model.Sequence = sequence
	return b
}

func (b *MediaReadModelBuilder) WithEnforceType(enforceType int) *MediaReadModelBuilder {
	b.model.EnforceType = enforceType
	return b
}

// 状态元数据
func (b *MediaReadModelBuilder) WithIsLocked(isLocked int) *MediaReadModelBuilder {
	b.model.IsLocked = isLocked
	return b
}

func (b *MediaReadModelBuilder) WithExpiryTime(t *time.Time) *MediaReadModelBuilder {
	b.model.ExpiryTime = t
	return b
}

// 归档数据
func (b *MediaReadModelBuilder) WithArchiveID(id int64) *MediaReadModelBuilder {
	b.model.ArchiveID = id
	return b
}

func (b *MediaReadModelBuilder) WithIsArchive(isArchive int) *MediaReadModelBuilder {
	b.model.IsArchive = isArchive
	return b
}

func (b *MediaReadModelBuilder) WithArchiveDate(t *time.Time) *MediaReadModelBuilder {
	b.model.ArchiveDate = t
	return b
}

// 文件元数据
func (b *MediaReadModelBuilder) WithFileIdentity(identity string) *MediaReadModelBuilder {
	b.model.FileIdentity = identity
	return b
}

func (b *MediaReadModelBuilder) WithFileName(name string) *MediaReadModelBuilder {
	b.model.FileName = name
	return b
}

func (b *MediaReadModelBuilder) WithFileSize(size int64) *MediaReadModelBuilder {
	b.model.FileSize = size
	return b
}

func (b *MediaReadModelBuilder) WithFileMd5(md5 string) *MediaReadModelBuilder {
	b.model.FileMd5 = md5
	return b
}

func (b *MediaReadModelBuilder) WithFileType(fileType int) *MediaReadModelBuilder {
	b.model.FileType = fileType
	return b
}

func (b *MediaReadModelBuilder) WithContentType(contentType string) *MediaReadModelBuilder {
	b.model.ContentType = contentType
	return b
}

// 存储元数据
func (b *MediaReadModelBuilder) WithURI(uri string) *MediaReadModelBuilder {
	b.model.URI = uri
	return b
}

func (b *MediaReadModelBuilder) WithThumbnail(thumbnail string) *MediaReadModelBuilder {
	b.model.Thumbnail = thumbnail
	return b
}

func (b *MediaReadModelBuilder) WithSiteID(id int) *MediaReadModelBuilder {
	b.model.SiteID = id
	return b
}

func (b *MediaReadModelBuilder) WithStorageID(id int) *MediaReadModelBuilder {
	b.model.StorageID = id
	return b
}

func (b *MediaReadModelBuilder) WithStorageType(storageType int) *MediaReadModelBuilder {
	b.model.StorageType = storageType
	return b
}

func (b *MediaReadModelBuilder) WithIsSendToStorage(isSend int) *MediaReadModelBuilder {
	b.model.IsSendToStorage = isSend
	return b
}

func (b *MediaReadModelBuilder) WithIsNoticeSend(isNotice int) *MediaReadModelBuilder {
	b.model.IsNoticeSend = isNotice
	return b
}

// 关联元数据
func (b *MediaReadModelBuilder) WithPoliceID(id int) *MediaReadModelBuilder {
	b.model.PoliceID = id
	return b
}

func (b *MediaReadModelBuilder) WithOrgID(id int) *MediaReadModelBuilder {
	b.model.OrgID = id
	return b
}

func (b *MediaReadModelBuilder) WithRecorderID(id int) *MediaReadModelBuilder {
	b.model.RecorderID = id
	return b
}

func (b *MediaReadModelBuilder) WithSiteClientID(id int) *MediaReadModelBuilder {
	b.model.SiteClientID = id
	return b
}

func (b *MediaReadModelBuilder) WithTerminalType(terminalType int) *MediaReadModelBuilder {
	b.model.TerminalType = terminalType
	return b
}

func (b *MediaReadModelBuilder) WithTrialID(id int) *MediaReadModelBuilder {
	b.model.TrialID = id
	return b
}

func (b *MediaReadModelBuilder) WithIncidentCode(code string) *MediaReadModelBuilder {
	b.model.IncidentCode = code
	return b
}

func (b *MediaReadModelBuilder) WithIsAssociated(isAssociated int) *MediaReadModelBuilder {
	b.model.IsAssociated = isAssociated
	return b
}

func (b *MediaReadModelBuilder) WithAssociateTime(t *time.Time) *MediaReadModelBuilder {
	b.model.AssociateTime = t
	return b
}

// 传输校验数据
func (b *MediaReadModelBuilder) WithRequestIdentity(identity string) *MediaReadModelBuilder {
	b.model.RequestIdentity = identity
	return b
}

func (b *MediaReadModelBuilder) WithAuthKey(key string) *MediaReadModelBuilder {
	b.model.AuthKey = key
	return b
}

func (b *MediaReadModelBuilder) WithTraceCode(code string) *MediaReadModelBuilder {
	b.model.TraceCode = code
	return b
}

// 时间相关
func (b *MediaReadModelBuilder) WithImportTime(t *time.Time) *MediaReadModelBuilder {
	b.model.ImportTime = t
	return b
}

func (b *MediaReadModelBuilder) WithAcquisitionTime(t *time.Time) *MediaReadModelBuilder {
	b.model.AcquisitionTime = t
	return b
}

// 审计字段
func (b *MediaReadModelBuilder) WithCreateBy(createBy int) *MediaReadModelBuilder {
	b.model.CreateBy = createBy
	return b
}

func (b *MediaReadModelBuilder) WithUpdateBy(updateBy int) *MediaReadModelBuilder {
	b.model.UpdateBy = updateBy
	return b
}

func (b *MediaReadModelBuilder) WithCreatedAt(t time.Time) *MediaReadModelBuilder {
	b.model.CreatedAt = t
	return b
}

func (b *MediaReadModelBuilder) WithUpdatedAt(t time.Time) *MediaReadModelBuilder {
	b.model.UpdatedAt = t
	return b
}

// 存储元数据补充
func (b *MediaReadModelBuilder) WithPlayUrl(url string) *MediaReadModelBuilder {
	b.model.PlayUrl = url
	return b
}

func (b *MediaReadModelBuilder) WithStoPlayUrl(url string) *MediaReadModelBuilder {
	b.model.StoPlayUrl = url
	return b
}

func (b *MediaReadModelBuilder) WithFlvUrl(url string) *MediaReadModelBuilder {
	b.model.FlvUrl = url
	return b
}

func (b *MediaReadModelBuilder) WithSiteNo(no string) *MediaReadModelBuilder {
	b.model.SiteNo = no
	return b
}

func (b *MediaReadModelBuilder) WithSiteName(name string) *MediaReadModelBuilder {
	b.model.SiteName = name
	return b
}

func (b *MediaReadModelBuilder) WithSiteHttp(http string) *MediaReadModelBuilder {
	b.model.SiteHttp = http
	return b
}

func (b *MediaReadModelBuilder) WithStorageIDStr(idStr string) *MediaReadModelBuilder {
	b.model.StorageIDStr = idStr
	return b
}

func (b *MediaReadModelBuilder) WithStorageHttp(http string) *MediaReadModelBuilder {
	b.model.StorageHttp = http
	return b
}

// 关联元数据补充
func (b *MediaReadModelBuilder) WithPoliceNo(no string) *MediaReadModelBuilder {
	b.model.PoliceNo = no
	return b
}

func (b *MediaReadModelBuilder) WithPoliceName(name string) *MediaReadModelBuilder {
	b.model.PoliceName = name
	return b
}

func (b *MediaReadModelBuilder) WithPoliceIdCard(idCard string) *MediaReadModelBuilder {
	b.model.PoliceIdCard = idCard
	return b
}

func (b *MediaReadModelBuilder) WithOrgCode(code string) *MediaReadModelBuilder {
	b.model.OrgCode = code
	return b
}

func (b *MediaReadModelBuilder) WithOrgName(name string) *MediaReadModelBuilder {
	b.model.OrgName = name
	return b
}

func (b *MediaReadModelBuilder) WithOrgJc(jc string) *MediaReadModelBuilder {
	b.model.OrgJc = jc
	return b
}

// Build 构建最终的 MediaReadModel
func (b *MediaReadModelBuilder) Build() *models.MediaReadModel {
	return b.model
}
