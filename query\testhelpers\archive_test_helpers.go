package testhelpers

import (
	"jxt-evidence-system/evidence-management/query/internal/models"
	"time"
)

// ArchiveReadModelBuilder 档案读模型构建器
type ArchiveReadModelBuilder struct {
	model *models.ArchiveReadModel
}

func NewArchiveReadModelBuilder() *ArchiveReadModelBuilder {
	now := time.Now()
	return &ArchiveReadModelBuilder{
		model: &models.ArchiveReadModel{
			ArchiveCode:     "AR0001T01-01926b8e-3c4a-7890-abcd-ef1234567890",
			ArchiveTitle:    "测试档案",
			ArchiveType:     1,
			Description:     "测试档案描述",
			OrgID:           1,
			StorageDuration: 12,
			ExpirationTime:  &now,
			Status:          0,
			Remarks:         "测试备注",
		},
	}
}

func (b *ArchiveReadModelBuilder) WithID(id int64) *ArchiveReadModelBuilder {
	b.model.ID = id
	return b
}

func (b *ArchiveReadModelBuilder) WithArchiveID(archiveID int64) *ArchiveReadModelBuilder {
	b.model.ID = archiveID
	return b
}

func (b *ArchiveReadModelBuilder) WithCode(code string) *ArchiveReadModelBuilder {
	b.model.ArchiveCode = code
	return b
}

func (b *ArchiveReadModelBuilder) WithTitle(title string) *ArchiveReadModelBuilder {
	b.model.ArchiveTitle = title
	return b
}

func (b *ArchiveReadModelBuilder) WithType(archiveType int) *ArchiveReadModelBuilder {
	b.model.ArchiveType = archiveType
	return b
}

func (b *ArchiveReadModelBuilder) WithDescription(description string) *ArchiveReadModelBuilder {
	b.model.Description = description
	return b
}

func (b *ArchiveReadModelBuilder) WithOrgID(orgID int) *ArchiveReadModelBuilder {
	b.model.OrgID = orgID
	return b
}

func (b *ArchiveReadModelBuilder) WithStorageDuration(duration int) *ArchiveReadModelBuilder {
	b.model.StorageDuration = duration
	return b
}

func (b *ArchiveReadModelBuilder) WithExpirationTime(expirationTime *time.Time) *ArchiveReadModelBuilder {
	b.model.ExpirationTime = expirationTime
	return b
}

func (b *ArchiveReadModelBuilder) WithStatus(status int) *ArchiveReadModelBuilder {
	b.model.Status = status
	return b
}

func (b *ArchiveReadModelBuilder) WithRemarks(remarks string) *ArchiveReadModelBuilder {
	b.model.Remarks = remarks
	return b
}

func (b *ArchiveReadModelBuilder) WithCreatedAt(createdAt time.Time) *ArchiveReadModelBuilder {
	b.model.CreatedAt = createdAt
	return b
}

func (b *ArchiveReadModelBuilder) WithUpdatedAt(updatedAt time.Time) *ArchiveReadModelBuilder {
	b.model.UpdatedAt = updatedAt
	return b
}

func (b *ArchiveReadModelBuilder) WithCreateBy(createBy int) *ArchiveReadModelBuilder {
	b.model.CreateBy = createBy
	return b
}

func (b *ArchiveReadModelBuilder) WithUpdateBy(updateBy int) *ArchiveReadModelBuilder {
	b.model.UpdateBy = updateBy
	return b
}

func (b *ArchiveReadModelBuilder) Build() *models.ArchiveReadModel {
	return b.model
}
