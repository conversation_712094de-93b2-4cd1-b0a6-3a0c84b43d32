// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: equipment_management/interface/grpc/lawcamera/proto/lawcamera.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 根据执法记录仪ID查询请求
type GetLawcameraByIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      string                 `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"` // 租户ID，用于多租户系统
	Id            int32                  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                            // 执法记录仪唯一ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLawcameraByIdReq) Reset() {
	*x = GetLawcameraByIdReq{}
	mi := &file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLawcameraByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLawcameraByIdReq) ProtoMessage() {}

func (x *GetLawcameraByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLawcameraByIdReq.ProtoReflect.Descriptor instead.
func (*GetLawcameraByIdReq) Descriptor() ([]byte, []int) {
	return file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDescGZIP(), []int{0}
}

func (x *GetLawcameraByIdReq) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *GetLawcameraByIdReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 根据执法记录仪编号查询请求
type GetLawcameraByNoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      string                 `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"` // 租户ID，用于多租户系统
	No            string                 `protobuf:"bytes,2,opt,name=no,proto3" json:"no,omitempty"`                             // 执法记录仪编号
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLawcameraByNoReq) Reset() {
	*x = GetLawcameraByNoReq{}
	mi := &file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLawcameraByNoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLawcameraByNoReq) ProtoMessage() {}

func (x *GetLawcameraByNoReq) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLawcameraByNoReq.ProtoReflect.Descriptor instead.
func (*GetLawcameraByNoReq) Descriptor() ([]byte, []int) {
	return file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDescGZIP(), []int{1}
}

func (x *GetLawcameraByNoReq) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *GetLawcameraByNoReq) GetNo() string {
	if x != nil {
		return x.No
	}
	return ""
}

// 根据管理员ID查询执法记录仪列表请求
type GetLawcamerasByManagerIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TenantId      string                 `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`     // 租户ID，用于多租户系统
	ManagerId     int32                  `protobuf:"varint,2,opt,name=manager_id,json=managerId,proto3" json:"manager_id,omitempty"` // 管理员ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLawcamerasByManagerIdReq) Reset() {
	*x = GetLawcamerasByManagerIdReq{}
	mi := &file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLawcamerasByManagerIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLawcamerasByManagerIdReq) ProtoMessage() {}

func (x *GetLawcamerasByManagerIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLawcamerasByManagerIdReq.ProtoReflect.Descriptor instead.
func (*GetLawcamerasByManagerIdReq) Descriptor() ([]byte, []int) {
	return file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDescGZIP(), []int{2}
}

func (x *GetLawcamerasByManagerIdReq) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *GetLawcamerasByManagerIdReq) GetManagerId() int32 {
	if x != nil {
		return x.ManagerId
	}
	return 0
}

// 根据领用人ID查询执法记录仪列表请求
type GetLawcamerasByRequisitionerIdReq struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	TenantId        string                 `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`                       // 租户ID，用于多租户系统
	RequisitionerId int32                  `protobuf:"varint,2,opt,name=requisitioner_id,json=requisitionerId,proto3" json:"requisitioner_id,omitempty"` // 领用人ID
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetLawcamerasByRequisitionerIdReq) Reset() {
	*x = GetLawcamerasByRequisitionerIdReq{}
	mi := &file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLawcamerasByRequisitionerIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLawcamerasByRequisitionerIdReq) ProtoMessage() {}

func (x *GetLawcamerasByRequisitionerIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLawcamerasByRequisitionerIdReq.ProtoReflect.Descriptor instead.
func (*GetLawcamerasByRequisitionerIdReq) Descriptor() ([]byte, []int) {
	return file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDescGZIP(), []int{3}
}

func (x *GetLawcamerasByRequisitionerIdReq) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *GetLawcamerasByRequisitionerIdReq) GetRequisitionerId() int32 {
	if x != nil {
		return x.RequisitionerId
	}
	return 0
}

// 执法记录仪信息响应，字段与领域模型LawcameraModel对应
type LawcameraInfoReply struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                              // 执法记录仪ID
	No                 string                 `protobuf:"bytes,2,opt,name=no,proto3" json:"no,omitempty"`                                                               // 执法记录仪编号
	Name               string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                           // 执法记录仪名称
	ManagerId          int32                  `protobuf:"varint,4,opt,name=manager_id,json=managerId,proto3" json:"manager_id,omitempty"`                               // 管理员ID
	ManagerOrgId       int32                  `protobuf:"varint,5,opt,name=manager_org_id,json=managerOrgId,proto3" json:"manager_org_id,omitempty"`                    // 管理员所在组织ID
	ManagerName        string                 `protobuf:"bytes,6,opt,name=manager_name,json=managerName,proto3" json:"manager_name,omitempty"`                          // 管理员名称
	ManagerOrgFullName string                 `protobuf:"bytes,7,opt,name=manager_org_full_name,json=managerOrgFullName,proto3" json:"manager_org_full_name,omitempty"` // 管理员所在组织全名称
	EnableUse          int32                  `protobuf:"varint,8,opt,name=enable_use,json=enableUse,proto3" json:"enable_use,omitempty"`                               // 是否能被使用：1:可用; 2:不可用
	State              int32                  `protobuf:"varint,9,opt,name=state,proto3" json:"state,omitempty"`                                                        // 状态：1:正常; 2:报废; 3:报修; 4:库存; 5:遗失
	Cpu                string                 `protobuf:"bytes,10,opt,name=cpu,proto3" json:"cpu,omitempty"`                                                            // CPU
	Memory             int32                  `protobuf:"varint,11,opt,name=memory,proto3" json:"memory,omitempty"`                                                     // 内存，单位：G
	Disk               int32                  `protobuf:"varint,12,opt,name=disk,proto3" json:"disk,omitempty"`                                                         // 存储，单位：G
	NetworkCard        string                 `protobuf:"bytes,13,opt,name=network_card,json=networkCard,proto3" json:"network_card,omitempty"`                         // 网卡
	UsbNum             int32                  `protobuf:"varint,14,opt,name=usb_num,json=usbNum,proto3" json:"usb_num,omitempty"`                                       // USB数量
	System             string                 `protobuf:"bytes,15,opt,name=system,proto3" json:"system,omitempty"`                                                      // 操作系统
	Version            string                 `protobuf:"bytes,16,opt,name=version,proto3" json:"version,omitempty"`                                                    // 版本
	BuyTime            string                 `protobuf:"bytes,17,opt,name=buy_time,json=buyTime,proto3" json:"buy_time,omitempty"`                                     // 购买时间
	Remark             string                 `protobuf:"bytes,18,opt,name=remark,proto3" json:"remark,omitempty"`                                                      // 备注
	// 领用相关信息
	UseState                 int32  `protobuf:"varint,19,opt,name=use_state,json=useState,proto3" json:"use_state,omitempty"`                                                    // 使用状态：1:使用中; 0:闲置
	RequisitionerId          int32  `protobuf:"varint,20,opt,name=requisitioner_id,json=requisitionerId,proto3" json:"requisitioner_id,omitempty"`                               // 领用人ID
	RequisitionerName        string `protobuf:"bytes,21,opt,name=requisitioner_name,json=requisitionerName,proto3" json:"requisitioner_name,omitempty"`                          // 领用人名称
	RequisitionerOrgId       int32  `protobuf:"varint,22,opt,name=requisitioner_org_id,json=requisitionerOrgId,proto3" json:"requisitioner_org_id,omitempty"`                    // 领用人组织ID
	RequisitionerOrgFullName string `protobuf:"bytes,23,opt,name=requisitioner_org_full_name,json=requisitionerOrgFullName,proto3" json:"requisitioner_org_full_name,omitempty"` // 领用人组织全名称
	RequisitionStartTime     string `protobuf:"bytes,24,opt,name=requisition_start_time,json=requisitionStartTime,proto3" json:"requisition_start_time,omitempty"`               // 领用开始时间
	ExpectedReturnDate       string `protobuf:"bytes,25,opt,name=expected_return_date,json=expectedReturnDate,proto3" json:"expected_return_date,omitempty"`                     // 预计归还时间
	CreatedAt                string `protobuf:"bytes,26,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                                  // 创建时间
	UpdatedAt                string `protobuf:"bytes,27,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                                  // 更新时间
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *LawcameraInfoReply) Reset() {
	*x = LawcameraInfoReply{}
	mi := &file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LawcameraInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LawcameraInfoReply) ProtoMessage() {}

func (x *LawcameraInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LawcameraInfoReply.ProtoReflect.Descriptor instead.
func (*LawcameraInfoReply) Descriptor() ([]byte, []int) {
	return file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDescGZIP(), []int{4}
}

func (x *LawcameraInfoReply) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LawcameraInfoReply) GetNo() string {
	if x != nil {
		return x.No
	}
	return ""
}

func (x *LawcameraInfoReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LawcameraInfoReply) GetManagerId() int32 {
	if x != nil {
		return x.ManagerId
	}
	return 0
}

func (x *LawcameraInfoReply) GetManagerOrgId() int32 {
	if x != nil {
		return x.ManagerOrgId
	}
	return 0
}

func (x *LawcameraInfoReply) GetManagerName() string {
	if x != nil {
		return x.ManagerName
	}
	return ""
}

func (x *LawcameraInfoReply) GetManagerOrgFullName() string {
	if x != nil {
		return x.ManagerOrgFullName
	}
	return ""
}

func (x *LawcameraInfoReply) GetEnableUse() int32 {
	if x != nil {
		return x.EnableUse
	}
	return 0
}

func (x *LawcameraInfoReply) GetState() int32 {
	if x != nil {
		return x.State
	}
	return 0
}

func (x *LawcameraInfoReply) GetCpu() string {
	if x != nil {
		return x.Cpu
	}
	return ""
}

func (x *LawcameraInfoReply) GetMemory() int32 {
	if x != nil {
		return x.Memory
	}
	return 0
}

func (x *LawcameraInfoReply) GetDisk() int32 {
	if x != nil {
		return x.Disk
	}
	return 0
}

func (x *LawcameraInfoReply) GetNetworkCard() string {
	if x != nil {
		return x.NetworkCard
	}
	return ""
}

func (x *LawcameraInfoReply) GetUsbNum() int32 {
	if x != nil {
		return x.UsbNum
	}
	return 0
}

func (x *LawcameraInfoReply) GetSystem() string {
	if x != nil {
		return x.System
	}
	return ""
}

func (x *LawcameraInfoReply) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *LawcameraInfoReply) GetBuyTime() string {
	if x != nil {
		return x.BuyTime
	}
	return ""
}

func (x *LawcameraInfoReply) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *LawcameraInfoReply) GetUseState() int32 {
	if x != nil {
		return x.UseState
	}
	return 0
}

func (x *LawcameraInfoReply) GetRequisitionerId() int32 {
	if x != nil {
		return x.RequisitionerId
	}
	return 0
}

func (x *LawcameraInfoReply) GetRequisitionerName() string {
	if x != nil {
		return x.RequisitionerName
	}
	return ""
}

func (x *LawcameraInfoReply) GetRequisitionerOrgId() int32 {
	if x != nil {
		return x.RequisitionerOrgId
	}
	return 0
}

func (x *LawcameraInfoReply) GetRequisitionerOrgFullName() string {
	if x != nil {
		return x.RequisitionerOrgFullName
	}
	return ""
}

func (x *LawcameraInfoReply) GetRequisitionStartTime() string {
	if x != nil {
		return x.RequisitionStartTime
	}
	return ""
}

func (x *LawcameraInfoReply) GetExpectedReturnDate() string {
	if x != nil {
		return x.ExpectedReturnDate
	}
	return ""
}

func (x *LawcameraInfoReply) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *LawcameraInfoReply) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

// 执法记录仪列表响应
type LawcameraListReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Lawcameras    []*LawcameraInfoReply  `protobuf:"bytes,1,rep,name=lawcameras,proto3" json:"lawcameras,omitempty"` // 执法记录仪列表
	Total         int64                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`          // 总数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LawcameraListReply) Reset() {
	*x = LawcameraListReply{}
	mi := &file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LawcameraListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LawcameraListReply) ProtoMessage() {}

func (x *LawcameraListReply) ProtoReflect() protoreflect.Message {
	mi := &file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LawcameraListReply.ProtoReflect.Descriptor instead.
func (*LawcameraListReply) Descriptor() ([]byte, []int) {
	return file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDescGZIP(), []int{5}
}

func (x *LawcameraListReply) GetLawcameras() []*LawcameraInfoReply {
	if x != nil {
		return x.Lawcameras
	}
	return nil
}

func (x *LawcameraListReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto protoreflect.FileDescriptor

const file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDesc = "" +
	"\n" +
	"Cequipment_management/interface/grpc/lawcamera/proto/lawcamera.proto\x12\tlawcamera\"B\n" +
	"\x13GetLawcameraByIdReq\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\tR\btenantId\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x05R\x02id\"B\n" +
	"\x13GetLawcameraByNoReq\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\tR\btenantId\x12\x0e\n" +
	"\x02no\x18\x02 \x01(\tR\x02no\"Y\n" +
	"\x1bGetLawcamerasByManagerIdReq\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\tR\btenantId\x12\x1d\n" +
	"\n" +
	"manager_id\x18\x02 \x01(\x05R\tmanagerId\"k\n" +
	"!GetLawcamerasByRequisitionerIdReq\x12\x1b\n" +
	"\ttenant_id\x18\x01 \x01(\tR\btenantId\x12)\n" +
	"\x10requisitioner_id\x18\x02 \x01(\x05R\x0frequisitionerId\"\x85\a\n" +
	"\x12LawcameraInfoReply\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x0e\n" +
	"\x02no\x18\x02 \x01(\tR\x02no\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"manager_id\x18\x04 \x01(\x05R\tmanagerId\x12$\n" +
	"\x0emanager_org_id\x18\x05 \x01(\x05R\fmanagerOrgId\x12!\n" +
	"\fmanager_name\x18\x06 \x01(\tR\vmanagerName\x121\n" +
	"\x15manager_org_full_name\x18\a \x01(\tR\x12managerOrgFullName\x12\x1d\n" +
	"\n" +
	"enable_use\x18\b \x01(\x05R\tenableUse\x12\x14\n" +
	"\x05state\x18\t \x01(\x05R\x05state\x12\x10\n" +
	"\x03cpu\x18\n" +
	" \x01(\tR\x03cpu\x12\x16\n" +
	"\x06memory\x18\v \x01(\x05R\x06memory\x12\x12\n" +
	"\x04disk\x18\f \x01(\x05R\x04disk\x12!\n" +
	"\fnetwork_card\x18\r \x01(\tR\vnetworkCard\x12\x17\n" +
	"\ausb_num\x18\x0e \x01(\x05R\x06usbNum\x12\x16\n" +
	"\x06system\x18\x0f \x01(\tR\x06system\x12\x18\n" +
	"\aversion\x18\x10 \x01(\tR\aversion\x12\x19\n" +
	"\bbuy_time\x18\x11 \x01(\tR\abuyTime\x12\x16\n" +
	"\x06remark\x18\x12 \x01(\tR\x06remark\x12\x1b\n" +
	"\tuse_state\x18\x13 \x01(\x05R\buseState\x12)\n" +
	"\x10requisitioner_id\x18\x14 \x01(\x05R\x0frequisitionerId\x12-\n" +
	"\x12requisitioner_name\x18\x15 \x01(\tR\x11requisitionerName\x120\n" +
	"\x14requisitioner_org_id\x18\x16 \x01(\x05R\x12requisitionerOrgId\x12=\n" +
	"\x1brequisitioner_org_full_name\x18\x17 \x01(\tR\x18requisitionerOrgFullName\x124\n" +
	"\x16requisition_start_time\x18\x18 \x01(\tR\x14requisitionStartTime\x120\n" +
	"\x14expected_return_date\x18\x19 \x01(\tR\x12expectedReturnDate\x12\x1d\n" +
	"\n" +
	"created_at\x18\x1a \x01(\tR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x1b \x01(\tR\tupdatedAt\"i\n" +
	"\x12LawcameraListReply\x12=\n" +
	"\n" +
	"lawcameras\x18\x01 \x03(\v2\x1d.lawcamera.LawcameraInfoReplyR\n" +
	"lawcameras\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x03R\x05total2\x96\x03\n" +
	"\x14LawcameraInfoService\x12S\n" +
	"\x10GetLawcameraById\x12\x1e.lawcamera.GetLawcameraByIdReq\x1a\x1d.lawcamera.LawcameraInfoReply\"\x00\x12S\n" +
	"\x10GetLawcameraByNo\x12\x1e.lawcamera.GetLawcameraByNoReq\x1a\x1d.lawcamera.LawcameraInfoReply\"\x00\x12c\n" +
	"\x18GetLawcamerasByManagerId\x12&.lawcamera.GetLawcamerasByManagerIdReq\x1a\x1d.lawcamera.LawcameraListReply\"\x00\x12o\n" +
	"\x1eGetLawcamerasByRequisitionerId\x12,.lawcamera.GetLawcamerasByRequisitionerIdReq\x1a\x1d.lawcamera.LawcameraListReply\"\x00B\tZ\a.;protob\x06proto3"

var (
	file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDescOnce sync.Once
	file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDescData []byte
)

func file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDescGZIP() []byte {
	file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDescOnce.Do(func() {
		file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDesc), len(file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDesc)))
	})
	return file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDescData
}

var file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_goTypes = []any{
	(*GetLawcameraByIdReq)(nil),               // 0: lawcamera.GetLawcameraByIdReq
	(*GetLawcameraByNoReq)(nil),               // 1: lawcamera.GetLawcameraByNoReq
	(*GetLawcamerasByManagerIdReq)(nil),       // 2: lawcamera.GetLawcamerasByManagerIdReq
	(*GetLawcamerasByRequisitionerIdReq)(nil), // 3: lawcamera.GetLawcamerasByRequisitionerIdReq
	(*LawcameraInfoReply)(nil),                // 4: lawcamera.LawcameraInfoReply
	(*LawcameraListReply)(nil),                // 5: lawcamera.LawcameraListReply
}
var file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_depIdxs = []int32{
	4, // 0: lawcamera.LawcameraListReply.lawcameras:type_name -> lawcamera.LawcameraInfoReply
	0, // 1: lawcamera.LawcameraInfoService.GetLawcameraById:input_type -> lawcamera.GetLawcameraByIdReq
	1, // 2: lawcamera.LawcameraInfoService.GetLawcameraByNo:input_type -> lawcamera.GetLawcameraByNoReq
	2, // 3: lawcamera.LawcameraInfoService.GetLawcamerasByManagerId:input_type -> lawcamera.GetLawcamerasByManagerIdReq
	3, // 4: lawcamera.LawcameraInfoService.GetLawcamerasByRequisitionerId:input_type -> lawcamera.GetLawcamerasByRequisitionerIdReq
	4, // 5: lawcamera.LawcameraInfoService.GetLawcameraById:output_type -> lawcamera.LawcameraInfoReply
	4, // 6: lawcamera.LawcameraInfoService.GetLawcameraByNo:output_type -> lawcamera.LawcameraInfoReply
	5, // 7: lawcamera.LawcameraInfoService.GetLawcamerasByManagerId:output_type -> lawcamera.LawcameraListReply
	5, // 8: lawcamera.LawcameraInfoService.GetLawcamerasByRequisitionerId:output_type -> lawcamera.LawcameraListReply
	5, // [5:9] is the sub-list for method output_type
	1, // [1:5] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_init() }
func file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_init() {
	if File_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDesc), len(file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_goTypes,
		DependencyIndexes: file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_depIdxs,
		MessageInfos:      file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_msgTypes,
	}.Build()
	File_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto = out.File
	file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_goTypes = nil
	file_equipment_management_interface_grpc_lawcamera_proto_lawcamera_proto_depIdxs = nil
}
