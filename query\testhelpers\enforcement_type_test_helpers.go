package testhelpers

import "jxt-evidence-system/evidence-management/query/internal/models"

// EnforcementTypeReadModelBuilder 执法类型读模型构建器
type EnforcementTypeReadModelBuilder struct {
	model *models.EnforcementTypeReadModel
}

func NewEnforcementTypeReadModelBuilder() *EnforcementTypeReadModelBuilder {
	return &EnforcementTypeReadModelBuilder{
		model: &models.EnforcementTypeReadModel{},
	}
}

func (b *EnforcementTypeReadModelBuilder) WithID(id int64) *EnforcementTypeReadModelBuilder {
	b.model.ID = id
	return b
}

func (b *EnforcementTypeReadModelBuilder) WithName(name string) *EnforcementTypeReadModelBuilder {
	b.model.EnforcementTypeName = name
	return b
}

func (b *EnforcementTypeReadModelBuilder) WithDesc(comments string) *EnforcementTypeReadModelBuilder {
	b.model.EnforcementTypeDesc = comments
	return b
}

func (b *EnforcementTypeReadModelBuilder) Build() *models.EnforcementTypeReadModel {
	return b.model
}
