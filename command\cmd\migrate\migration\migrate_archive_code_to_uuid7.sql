-- 档案编码迁移到UUIDv7格式
-- 此脚本仅为示例，实际生产环境需要根据具体数据进行调整

-- 1. 备份现有数据
CREATE TABLE t_evidence_archives_backup AS SELECT * FROM t_evidence_archives;

-- 2. 添加临时列存储新的编码格式
ALTER TABLE t_evidence_archives ADD COLUMN new_archive_code VARCHAR(128);

-- 3. 为现有数据生成新的编码格式（此处需要应用程序配合）
-- 注意：由于UUIDv7是基于时间的，实际迁移时可能需要应用程序代码协助
-- 这里只是示例SQL，实际实施时需要通过应用程序API批量更新

-- 4. 检查新编码是否都已生成
-- SELECT COUNT(*) FROM t_evidence_archives WHERE new_archive_code IS NULL;

-- 5. 更新主编码字段（谨慎操作）
-- UPDATE t_evidence_archives SET archive_code = new_archive_code WHERE new_archive_code IS NOT NULL;

-- 6. 删除临时列
-- ALTER TABLE t_evidence_archives DROP COLUMN new_archive_code;

-- 7. 更新索引（如果有的话）
-- DROP INDEX IF EXISTS idx_archive_code;
-- CREATE INDEX idx_archive_code ON t_evidence_archives(archive_code);

-- 注意事项：
-- 1. 此迁移脚本需要在停机时间内执行
-- 2. 建议先在测试环境验证
-- 3. 备份数据表必不可少
-- 4. 实际的新编码生成需要通过应用程序API完成 