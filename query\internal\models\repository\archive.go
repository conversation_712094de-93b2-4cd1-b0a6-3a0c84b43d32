package repository

import (
	"context"
	query "jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/models"
)

// ArchiveReadModelRepository 定义档案读模型仓储接口
type ArchiveReadModelRepository interface {
	// 分页查询
	GetPage(ctx context.Context, r *query.ArchivePagedQuery) (list *[]models.ArchiveReadModel, count int64, err error)

	// 根据id查询
	FindByID(ctx context.Context, id int64) (*models.ArchiveReadModel, error)

	// 根据编码查询
	FindByCode(ctx context.Context, code string) (*models.ArchiveReadModel, error)

	// Create 创建档案读模型
	Create(ctx context.Context, model *models.ArchiveReadModel) error

	// 只更新特定字段，即使字段值为 0，"", false 也会更新，且这样更新效率更高
	UpdateByID(ctx context.Context, id int64, updates map[string]interface{}) error

	// 批量更新
	UpdateManyByIDs(ctx context.Context, ids []int64, updates map[string]interface{}) (rowsAffected int64, err error)

	// 根据id删除
	DeleteByID(ctx context.Context, id int64) error

	// 批量删除
	DeleteManyByIDs(ctx context.Context, ids []int64) (rowsAffected int64, err error)
}
