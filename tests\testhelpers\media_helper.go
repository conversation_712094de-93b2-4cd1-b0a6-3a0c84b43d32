package testhelpers

import (
	"fmt"
	"math/rand"
	"net/http"
	"time"

	. "github.com/onsi/gomega"
	"gorm.io/gorm"

	commandTesthelpers "jxt-evidence-system/evidence-management/command/testhelpers"
	queryTesthelpers "jxt-evidence-system/evidence-management/query/testhelpers"
)

// MediaTestHelper 媒体测试助手
type MediaTestHelper struct {
	dbCommand *gorm.DB
	dbQuery   *gorm.DB
	suite     *TestSuite
}

// NewMediaTestHelper 创建媒体测试助手
func NewMediaTestHelper(dbCommand, dbQuery *gorm.DB, suite *TestSuite) *MediaTestHelper {
	return &MediaTestHelper{
		dbCommand: dbCommand,
		dbQuery:   dbQuery,
		suite:     suite,
	}
}

// CreateTestMedia 创建测试媒体
func (h *MediaTestHelper) CreateTestMedia(mediaName string) (int64, error) {
	// 清理之前的测试数据
	//h.CleanupTestData() // 批量创建媒体时，不能执行这步否则刚创建好就被清理掉了

	// 使用强随机化避免媒体名称冲突
	now := time.Now()
	pid := 1000 + rand.Intn(9000)
	tid := 100 + rand.Intn(900)
	seq := now.UnixNano() % 1000000
	randomSuffix := fmt.Sprintf("%d_%d_%d_%d", pid, tid, seq, now.Nanosecond()%1000000)
	uniqueMediaName := fmt.Sprintf("%s_%s", mediaName, randomSuffix)

	importTime := time.Now()
	uploadMediaCommand := commandTesthelpers.NewUploadMediaCommandBuilder().
		WithMediaName(uniqueMediaName).
		WithMediaCate(2).
		WithPoliceNo("0001").
		WithRecorderNo("REC001").
		WithAuthKey("md5hashhere").
		WithRequestIdentity("REQ001").
		WithImportantLevel(1).
		WithMediaSuffix("mp4").
		WithPoliceName("张三").
		WithShotTimeStart(time.Now()).
		WithShotTime(time.Now().Add(time.Hour)).
		WithVideoClarity(1).
		WithVideoDuration(3600000).
		WithFileSize(1024).
		WithOrgID(1).
		WithOrgName("公安局").
		WithStorageType(0).
		WithSiteID(1).
		WithStorageID(1).
		WithSiteClientID(1).
		WithTrialID(1).
		WithImportTime(&importTime).
		WithComments("测试媒体").
		Build()

	resp, err := SendRequestWithAuth(h.suite.BaseURL, "POST", "/api/v1/media", uploadMediaCommand, h.suite.Token)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := GetResponseBody(resp)
		return 0, fmt.Errorf("创建媒体失败，状态码: %d, 响应: %+v", resp.StatusCode, body)
	}

	// 检查响应体中的业务状态码
	body, err := GetResponseBody(resp)
	if err != nil {
		return 0, fmt.Errorf("解析响应体失败: %w", err)
	}

	if code, ok := body["code"].(float64); !ok || code != 200 {
		return 0, fmt.Errorf("创建媒体业务失败，响应: %+v", body)
	}

	// 等待领域事件处理
	h.WaitForEventProcessing()

	// 获取创建的媒体ID
	createdMedia := commandTesthelpers.NewMediaBuilder().Build()
	result := h.dbCommand.Where("media_name = ?", uniqueMediaName).First(&createdMedia)
	if result.Error != nil {
		return 0, fmt.Errorf("查询创建的媒体失败: %w", result.Error)
	}

	return createdMedia.ID, nil
}

// CreateTestMediaList 创建多个测试媒体
func (h *MediaTestHelper) CreateTestMediaList(count int, namePrefix string) ([]int64, error) {
	ids := make([]int64, count)
	for i := 0; i < count; i++ {
		mediaName := fmt.Sprintf("%s_%d", namePrefix, i+1)
		id, err := h.CreateTestMedia(mediaName)
		if err != nil {
			return nil, fmt.Errorf("创建第%d个测试媒体失败: %w", i+1, err)
		}
		ids[i] = id

		// 使用原始测试的等待时间
		time.Sleep(200 * time.Millisecond)
	}

	return ids, nil
}

// VerifyMediaInCommandDB 验证媒体在命令数据库中存在
func (h *MediaTestHelper) VerifyMediaInCommandDB(mediaID int64) {
	media := commandTesthelpers.NewMediaBuilder().Build()
	err := h.dbCommand.First(&media, mediaID).Error
	Expect(err).NotTo(HaveOccurred(), fmt.Sprintf("媒体ID %d 在命令数据库中不存在", mediaID))
}

// VerifyMediaInQueryDB 验证媒体在查询数据库中存在
func (h *MediaTestHelper) VerifyMediaInQueryDB(mediaID int64) {
	queryMedia := queryTesthelpers.NewMediaReadModelBuilder().Build()
	err := h.dbQuery.Where("media_id = ?", mediaID).First(&queryMedia).Error
	Expect(err).NotTo(HaveOccurred(), "媒体在查询数据库中不存在")
}

// VerifyMediaNotExistsInCommandDB 验证媒体在命令数据库中不存在
func (h *MediaTestHelper) VerifyMediaNotExistsInCommandDB(mediaID int64) {
	var count int64
	media := commandTesthelpers.NewMediaBuilder().Build()
	h.dbCommand.Model(media).Where("media_id = ?", mediaID).Count(&count)
	Expect(count).To(Equal(int64(0)), "媒体在命令数据库中仍然存在")
}

// VerifyMediaNotExistsInQueryDB 验证媒体在查询数据库中不存在
func (h *MediaTestHelper) VerifyMediaNotExistsInQueryDB(mediaID int64) {
	var count int64
	queryMedia := queryTesthelpers.NewMediaReadModelBuilder().Build()
	h.dbQuery.Model(queryMedia).Where("media_id = ?", mediaID).Count(&count)
	Expect(count).To(Equal(int64(0)), "媒体在查询数据库中仍然存在")
}

// VerifyMediaProperty 验证媒体属性
func (h *MediaTestHelper) VerifyMediaProperty(mediaID int64, propertyName string, expectedValue interface{}) {
	// 等待事件处理
	h.WaitForEventProcessing()

	// 验证命令数据库 - 使用原始测试的方式（通过主键查询）
	commandMedia := commandTesthelpers.NewMediaBuilder().Build()
	err := h.dbCommand.First(&commandMedia, mediaID).Error
	Expect(err).NotTo(HaveOccurred(), fmt.Sprintf("媒体ID %d 在命令数据库中不存在", mediaID))

	// 验证查询数据库 - 使用原始测试的方式（通过主键查询）
	queryMedia := queryTesthelpers.NewMediaReadModelBuilder().Build()
	err = h.dbQuery.First(&queryMedia, mediaID).Error
	Expect(err).NotTo(HaveOccurred(), fmt.Sprintf("媒体ID %d 在查询数据库中不存在", mediaID))

	// 使用反射验证属性值
	switch propertyName {
	case "ImportantLevel":
		Expect(commandMedia.ImportantLevel).To(Equal(expectedValue), "命令数据库中媒体重要级别不匹配")
		Expect(queryMedia.ImportantLevel).To(Equal(expectedValue), "查询数据库中媒体重要级别不匹配")
	case "Comments":
		Expect(commandMedia.Comments).To(Equal(expectedValue), "命令数据库中媒体备注不匹配")
		Expect(queryMedia.Comments).To(Equal(expectedValue), "查询数据库中媒体备注不匹配")
	case "IsNonEnforcementMedia":
		Expect(commandMedia.IsNonEnforcementMedia).To(Equal(expectedValue), "命令数据库中非执法媒体状态不匹配")
		Expect(queryMedia.IsNonEnforcementMedia).To(Equal(expectedValue), "查询数据库中非执法媒体状态不匹配")
	case "EnforceType":
		Expect(commandMedia.EnforceType).To(Equal(expectedValue), "命令数据库中执法类型不匹配")
		Expect(queryMedia.EnforceType).To(Equal(expectedValue), "查询数据库中执法类型不匹配")
	case "IsLocked":
		Expect(commandMedia.IsLocked).To(Equal(expectedValue), "命令数据库中锁定状态不匹配")
		Expect(queryMedia.IsLocked).To(Equal(expectedValue), "查询数据库中锁定状态不匹配")
	}
}

// VerifyCreateMediaResponse 验证创建媒体响应
func (h *MediaTestHelper) VerifyCreateMediaResponse(resp *http.Response) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("创建媒体成功"))
}

// VerifyUpdateMediaResponse 验证更新媒体响应
func (h *MediaTestHelper) VerifyUpdateMediaResponse(resp *http.Response) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("Media updated successfully"))
}

// VerifyBatchUpdateMediaResponse 验证批量更新媒体响应
func (h *MediaTestHelper) VerifyBatchUpdateMediaResponse(resp *http.Response) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("批量更新媒体成功"))
}

// VerifyBatchDeleteMediaResponse 验证批量删除媒体响应
func (h *MediaTestHelper) VerifyBatchDeleteMediaResponse(resp *http.Response) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("批量删除媒体成功"))
}

// VerifyMarkNonEnforcementResponse 验证标记非执法媒体响应
func (h *MediaTestHelper) VerifyMarkNonEnforcementResponse(resp *http.Response) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("标注非执法视频成功"))
}

// VerifyBatchUpdateEnforceTypeResponse 验证批量更新执法类型响应
func (h *MediaTestHelper) VerifyBatchUpdateEnforceTypeResponse(resp *http.Response) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("批量更新执法类型成功"))
}

// VerifyBatchUpdateIsLockedResponse 验证批量更新锁定状态响应
func (h *MediaTestHelper) VerifyBatchUpdateIsLockedResponse(resp *http.Response) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("批量更新锁定状态成功"))
}

// VerifyGetMediaResponse 验证查询媒体响应
func (h *MediaTestHelper) VerifyGetMediaResponse(resp *http.Response) map[string]interface{} {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))

	return body["data"].(map[string]interface{})
}

// VerifyGetMediaListResponse 验证查询媒体列表响应
func (h *MediaTestHelper) VerifyGetMediaListResponse(resp *http.Response) ([]interface{}, map[string]interface{}) {
	AssertResponseSuccess(resp)

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())
	Expect(body["code"]).To(Equal(float64(http.StatusOK)))
	Expect(body["msg"]).To(Equal("查询成功"))

	data := body["data"].(map[string]interface{})
	list := data["list"].([]interface{})
	return list, data
}

// VerifyErrorResponse 验证错误响应
func (h *MediaTestHelper) VerifyErrorResponse(resp *http.Response, expectedMessageContains string) {
	// 检查HTTP状态码应该是200（系统使用统一的HTTP 200 + 业务状态码模式）
	Expect(resp.StatusCode).To(Equal(http.StatusOK), "系统统一使用HTTP 200状态码")

	body, err := GetResponseBody(resp)
	Expect(err).NotTo(HaveOccurred())

	// 检查业务错误码应该是400或500
	code, ok := body["code"].(float64)
	Expect(ok).To(BeTrue(), "响应中应该包含业务状态码")
	Expect(code).To(BeElementOf([]float64{400, 500}), fmt.Sprintf("业务状态码应该是400或500，实际是: %v", code))

	// 检查错误消息包含期望的内容
	msg, ok := body["msg"].(string)
	Expect(ok).To(BeTrue(), "响应消息应该是字符串")
	Expect(msg).To(ContainSubstring(expectedMessageContains), fmt.Sprintf("期望消息包含 '%s'，实际消息: '%s'", expectedMessageContains, msg))
}

// WaitForEventProcessing 等待事件处理
func (h *MediaTestHelper) WaitForEventProcessing() {
	time.Sleep(200 * time.Millisecond)
}

// CleanupTestData 清理测试数据
func (h *MediaTestHelper) CleanupTestData() {
	// 清理命令数据库中的测试媒体
	h.dbCommand.Exec("DELETE FROM t_evidence_media WHERE media_name LIKE '%测试%' OR media_name LIKE '%test%' OR media_name LIKE '%Test%' OR media_name LIKE '%媒体%'")

	// 清理查询数据库中的测试媒体
	h.dbQuery.Exec("DELETE FROM t_evidence_media_read WHERE media_name LIKE '%测试%' OR media_name LIKE '%test%' OR media_name LIKE '%Test%' OR media_name LIKE '%媒体%'")

	// 等待数据清理完成
	time.Sleep(100 * time.Millisecond)
}
