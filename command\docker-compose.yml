version: '1.0'
services:
  jxt-evidence-management:
    container_name: jxt-evidence-management2
    image: jxt-evidence-management:latest
    privileged: true
    restart: always
    ports:
      - 8001:8001
    volumes:
      - ./config/:/jxt-evidence/config/
      - ./static/:/jxt-evidence/static/
      - ./temp/:/jxt-evidence/temp
    command: ./jxt-evidence-management  server -c ./config/settings.yml
    networks:
      - jxt-security-management_jxt_web

networks:
  jxt-security-management_jxt_web:
    external: true
