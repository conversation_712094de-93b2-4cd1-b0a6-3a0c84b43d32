package repository

import (
	"context"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archivemediarelation"
)

// ArchiveMediaRelationRepository 档案媒体关联仓储接口
type ArchiveMediaRelationRepository interface {
	// FindByID 根据ID查找关联
	FindByID(ctx context.Context, id int64) (*archivemediarelation.ArchiveMediaRelation, error)

	// FindByArchiveAndMedia 根据档案ID和媒体ID查找关联
	FindByArchiveAndMedia(ctx context.Context, archiveId, mediaId int64) (*archivemediarelation.ArchiveMediaRelation, error)

	// FindByArchiveId 根据档案ID查找所有关联
	FindByArchiveId(ctx context.Context, archiveId int64) ([]*archivemediarelation.ArchiveMediaRelation, error)

	// FindByMediaId 根据媒体ID查找所有关联
	FindByMediaId(ctx context.Context, mediaId int64) ([]*archivemediarelation.ArchiveMediaRelation, error)

	// Create 创建关联
	Create(ctx context.Context, model *archivemediarelation.ArchiveMediaRelation) error

	// DeleteByID 根据ID删除关联
	DeleteByID(ctx context.Context, id int64) error

	// BatchCreate 批量创建关联
	BatchCreate(ctx context.Context, models []*archivemediarelation.ArchiveMediaRelation) error

	// BatchDeleteByIDs 批量删除关联
	BatchDeleteByIDs(ctx context.Context, ids []int64) (rowsAffected int64, err error)

	// DeleteByArchiveId 删除某档案的所有关联
	DeleteByArchiveId(ctx context.Context, archiveId int64) (rowsAffected int64, err error)

	// DeleteByMediaId 删除某媒体的所有关联
	DeleteByMediaId(ctx context.Context, mediaId int64) (rowsAffected int64, err error)
}
