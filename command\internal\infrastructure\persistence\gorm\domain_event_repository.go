package persistence

import (
	"context"
	"fmt"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/domain/event"
	event_repository "jxt-evidence-system/evidence-management/shared/domain/event/repository"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

// 初始化函数，用于依赖注入
func init() {
	registrations = append(registrations, registerDomainEventRepoDependencies)
}

// 注册MediaEventRepository的依赖
func registerDomainEventRepoDependencies() {
	if err := di.Provide(func() event_repository.DomainEventRepository {
		return &gormDomainEventRepository{}
	}); err != nil {
		logger.Fatalf("failed to provide GormDomainEventRepository: %v", err)
	}
}

// GormDomainEventRepository 实现 DomainEventRepository 接口
type gormDomainEventRepository struct {
	GormRepository
}

// Save 发布领域事件失败，持久化领域事件
func (repo *gormDomainEventRepository) Save(ctx context.Context, event *event.DomainEvent) error {
	// 当事件总线故障，需要未发送成功的领域事件写入数据库
	// 当事件总线故障恢复，需要从数据库读取未发送成功的领域事件重新发送
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return err
	}
	return db.WithContext(ctx).Save(event).Error
}

// FindByAggregateID 根据聚合根ID查找领域事件
func (repo *gormDomainEventRepository) FindByAggregateID(ctx context.Context, aggregateID string) ([]event.DomainEvent, error) {
	var events []event.DomainEvent
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}
	err = db.WithContext(ctx).Where("aggregate_id = ?", aggregateID).Find(&events).Error
	return events, err
}

// FindByEventType 根据事件类型查找领域事件
func (repo *gormDomainEventRepository) FindByEventType(ctx context.Context, eventType string) ([]event.DomainEvent, error) {
	var events []event.DomainEvent
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}
	err = db.WithContext(ctx).Where("event_type = ?", eventType).Find(&events).Error
	return events, err
}

// FindByTimeRange 根据时间范围查找领域事件
func (repo *gormDomainEventRepository) FindByTimeRange(ctx context.Context, startTime, endTime int64) ([]event.DomainEvent, error) {
	var events []event.DomainEvent
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}
	err = db.WithContext(ctx).Where("created_at BETWEEN ? AND ?", time.Unix(startTime, 0), time.Unix(endTime, 0)).Find(&events).Error
	return events, err
}

// Delete 删除领域事件
func (repo *gormDomainEventRepository) Delete(ctx context.Context, eventID string) error {
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return err
	}
	return db.WithContext(ctx).Where("event_id = ?", eventID).Delete(&event.DomainEvent{}).Error
}

// DeleteByAggregateID 根据聚合根ID删除领域事件
func (repo *gormDomainEventRepository) DeleteByAggregateID(ctx context.Context, aggregateID string) error {
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return err
	}
	return db.WithContext(ctx).Where("aggregate_id = ?", aggregateID).Delete(&event.DomainEvent{}).Error
}

// FindEarliestEvents 查询OccurredAt时间最早的domainEvent，最多返回50个
func (repo *gormDomainEventRepository) FindEarliestEvents(ctx context.Context) ([]event.DomainEvent, error) {
	// 当事件总线故障，需要未发送成功的领域事件写入数据库
	// 当事件总线故障恢复，需要从数据库读取未发送成功的领域事件重新发送
	var events []event.DomainEvent
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}

	err = db.WithContext(ctx).
		Order("occurred_at ASC").
		Limit(50).
		Find(&events).Error

	if err != nil {
		return nil, fmt.Errorf("查询最早的领域事件失败: %w", err)
	}

	return events, nil
}

// FindEarliestEventsByAggregateType 查询某个聚合类型OccurredAt时间最早的domainEvent，最多返回50个
func (repo *gormDomainEventRepository) FindEarliestEventsByAggregateType(ctx context.Context, aggregateType string) ([]event.DomainEvent, error) {
	var events []event.DomainEvent
	db, err := repo.GetOrm(ctx)
	if err != nil {
		return nil, err
	}

	err = db.WithContext(ctx).
		Where("aggregate_type = ?", aggregateType).
		Order("occurred_at ASC").
		Limit(50).
		Find(&events).Error

	if err != nil {
		return nil, fmt.Errorf("查询聚合类型 %s 最早的领域事件失败: %w", aggregateType, err)
	}

	return events, nil
}
