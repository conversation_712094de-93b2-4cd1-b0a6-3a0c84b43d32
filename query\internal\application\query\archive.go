package query

import (
	"jxt-evidence-system/evidence-management/shared/common/query"
	"time"
)

type ArchivePagedQuery struct {
	query.Pagination `search:"-"`
	ArchiveCode      string    `form:"archiveCode" search:"type:icontains;column:archive_code;table:t_archive_read"`
	ArchiveTitle     string    `form:"archiveTitle" search:"type:icontains;column:archive_title;table:t_archive_read"`
	ArchiveType      int       `form:"archiveType" search:"type:exact;column:archive_type;table:t_archive_read"`
	OrgID            int       `form:"orgId" search:"type:exact;column:org_id;table:t_archive_read"`
	OrgCode          string    `form:"orgCode" search:"type:icontains;column:org_code;table:t_archive_read"`
	OrgName          string    `form:"orgName" search:"type:icontains;column:org_name;table:t_archive_read"`
	Status           int       `form:"status" search:"type:exact;column:status;table:t_archive_read"`
	InputTimeStart   time.Time `form:"inputTimeStart" search:"type:gte;column:input_time;table:t_archive_read"`
	InputTimeEnd     time.Time `form:"inputTimeEnd" search:"type:lte;column:input_time;table:t_archive_read"`
	ExpirationStart  time.Time `form:"expirationStart" search:"type:gte;column:expiration_time;table:t_archive_read"`
	ExpirationEnd    time.Time `form:"expirationEnd" search:"type:lte;column:expiration_time;table:t_archive_read"`
	StorageDuration  int       `form:"storageDuration" search:"type:exact;column:storage_duration;table:t_archive_read"`
	InputUserName    string    `form:"inputUserName" search:"type:icontains;column:input_user_name;table:t_archive_read"`
	ArchiveOrder
}

type ArchiveOrder struct {
	ArchiveCodeOrder  string `search:"type:order;column:archive_code;table:t_archive_read" form:"archiveCodeOrder"`
	ArchiveTitleOrder string `search:"type:order;column:archive_title;table:t_archive_read" form:"archiveTitleOrder"`
	InputTimeOrder    string `search:"type:order;column:input_time;table:t_archive_read" form:"inputTimeOrder"`
	ExpirationOrder   string `search:"type:order;column:expiration_time;table:t_archive_read" form:"expirationOrder"`
	CreatedAtOrder    string `search:"type:order;column:created_at;table:t_archive_read" form:"createdAtOrder"`
}

func (a *ArchivePagedQuery) GetNeedSearch() interface{} {
	return *a
}
