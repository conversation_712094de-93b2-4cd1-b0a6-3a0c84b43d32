package eventbus

import (
	"context"
	"log"
)

func Close(ctx context.Context) error {

	// 关闭 Kafka 管理器
	if DefaultKafkaPublisherManager != nil {
		if err := DefaultKafkaPublisherManager.Shutdown(ctx); err != nil {
			log.Printf("Kafka Manager Shutdown: %v\n", err)
		}
	}

	if DefaultKafkaSubscriberManager != nil {
		if err := DefaultKafkaSubscriberManager.Shutdown(ctx); err != nil {
			log.Printf("Kafka Manager Shutdown: %v\n", err)
		}
	}

	return nil
}
