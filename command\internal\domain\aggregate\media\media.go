package media

import (
	"errors"
	"fmt"
	"jxt-evidence-system/evidence-management/shared/common/models"
	"jxt-evidence-system/evidence-management/shared/domain/event"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	jsoniter "github.com/json-iterator/go"

	"time"
)

type Media struct {
	ID int64 `json:"mediaId" gorm:"primaryKey;column:media_id;autoIncrement;comment:媒体ID"`

	// 业务元数据
	MediaName         string    `json:"mediaName" gorm:"size:128;column:media_name;comment:媒体名称"`
	MediaCate         int       `json:"mediaCate" gorm:"size:4;column:media_cate;comment:媒体类型(0: 照片 1: 音频 2: 视频 3:日志）"`
	MediaSuffix       string    `json:"mediaSuffix" gorm:"size:16;column:media_suffix;comment:媒体后缀"`
	ShotTimeStart     time.Time `json:"shotTimeStart" gorm:"column:shot_time_start;comment:拍摄开始时间"`
	ShotTime          time.Time `json:"shotTime" gorm:"column:shot_time;comment:拍摄时间"`
	VideoClarity      int       `json:"videoClarity" gorm:"column:video_clarity;comment:视频清晰度(0: 标清 1: 高清 2: 超清)"`
	VideoDuration     int       `json:"videoDuration" gorm:"column:video_duration;comment:视频时长（单位: 毫秒）"`
	Width             int       `json:"width" gorm:"column:width;comment:图片宽度"`
	Height            int       `json:"height" gorm:"column:height;comment:图片高度"`
	ImportantLevel    int       `json:"importantLevel" gorm:"size:4;column:important_level;comment:重要级别(平台标记)"`
	ImportantLevelRec int       `json:"importantLevelRec" gorm:"size:4;column:important_level_rec;comment:重要级别(设备标记)"`

	// 文件元数据
	FileIdentity string `json:"fileIdentity" gorm:"size:255;column:file_identity;comment:文件标识"`
	FileName     string `json:"fileName" gorm:"size:255;column:file_name;comment:文件名称"`
	FileSize     int64  `json:"fileSize" gorm:"column:file_size;comment:文件大小(单位: KB)"`
	FileMd5      string `json:"fileMd5" gorm:"size:32;column:file_md5;comment:文件MD5"`
	FileType     int    `json:"fileType" gorm:"size:4;column:file_type;comment:文件类型"`
	ContentType  string `json:"contentType" gorm:"size:128;column:content_type;comment:文件MIME类型"`

	// 业务标注数据
	IsNonEnforcementMedia int    `json:"isNonEnforcementMedia" gorm:"size:4;column:is_non_enforcement_media;comment:是否是非执法媒体"`
	Comments              string `json:"comments" gorm:"size:255;column:comments;comment:标注内容"`
	Sequence              string `json:"sequence" gorm:"size:255;column:sequence;comment:视频序列标识"`

	// 状态元数据
	IsLocked   int        `json:"isLocked" gorm:"size:4;column:is_locked;comment:是否锁定"`
	ExpiryTime *time.Time `json:"expiryTime" gorm:"column:expiry_time;default:NULL;comment:过期时间"`

	// 归档元数据
	ArchiveID   int64      `json:"archiveId" gorm:"column:archive_id;comment:档案编号"` //这个字段不用，实际使用查档案与媒体关联表
	IsArchived  int        `json:"isArchived" gorm:"column:is_archived;comment:是否归档"`
	ArchiveDate *time.Time `json:"archiveDate" gorm:"column:archive_date;default:NULL;comment:归档时间"` //这个字段实际未使用（实际上创建时间戳存储在关系表的createTime字段中），它和ArchiveID字段一起使用

	// 存储元数据
	URI             string `json:"uri" gorm:"size:255;column:uri;comment:存放uri"`
	Thumbnail       string `json:"thumbnail" gorm:"size:255;column:thumbnail;comment:Base64缩略图名称"`
	SiteID          int    `json:"siteId" gorm:"column:site_id;comment:采集站ID"`
	StorageID       int    `json:"storageId" gorm:"column:storage_id;comment:存储服务器ID"`
	StorageType     int    `json:"storageType" gorm:"size:4;column:storage_type;comment:存储方式(0: 采集站  1: 存储)"`
	IsSendToStorage int    `json:"isSendToStorage" gorm:"column:is_send_to_storage;comment:是否上传至存储(-1: 文件不存在,0: 未上传,1: 已上传)"`
	IsNoticeSend    int    `json:"isNoticeSend" gorm:"column:is_notice_send;comment:是否通知发送"`

	// 关联元数据
	PoliceID      int        `json:"policeId" gorm:"column:police_id;comment:警员ID"`
	OrgID         int        `json:"orgId" gorm:"column:org_id;comment:单位/组织ID"`
	RecorderID    int        `json:"recorderId" gorm:"column:recorder_id;comment:执法仪ID"`
	SiteClientID  int        `json:"siteClientId" gorm:"column:site_client_id;comment:采集客户端ID"`
	TerminalType  int        `json:"terminalType" gorm:"column:terminal_type;comment:终端类型(1: 执法仪 2: 采集站)"`
	TrialID       int        `json:"trialId" gorm:"column:trial_id;comment:固定执法场所ID"`
	EnforceType   int        `json:"enforceType" gorm:"size:4;column:enforce_type;comment:执法类型"`
	IncidentCode  string     `json:"IncidentCode" gorm:"size:255;column:incident_code;comment:警情号"`
	IsAssociated  int        `json:"isAssociated" gorm:"column:is_associated;comment:是否已经关联"`
	AssociateTime *time.Time `json:"associateTime" gorm:"column:associate_time;default:NULL;comment:关联时间"`

	// 传输校验数据
	RequestIdentity string `json:"requestIdentity" gorm:"size:255;column:request_identity;comment:请求标识"`
	AuthKey         string `json:"authKey" gorm:"size:255;column:auth_key;comment:认证码"`
	TraceCode       string `json:"traceCode" gorm:"size:255;column:trace_code;comment:追溯码"`

	// 审计字段
	ImportTime      *time.Time `json:"importTime" gorm:"column:import_time;default:NULL;comment:导入时间"`           //从执法记录仪导入采集站时间
	AcquisitionTime *time.Time `json:"acquisitionTime" gorm:"column:acquisition_time;default:NULL;comment:接收时间"` //ftpserver接收完成时间
	models.ControlBy
	models.ModelTime

	// 领域事件
	events []event.Event `gorm:"-"` // 使用 gorm:"-" 标签明确告诉 GORM 忽略这个字段
}

func (*Media) TableName() string {
	return "t_evidence_media"
}

func (e *Media) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *Media) GetId() interface{} {
	return e.ID
}

func (e *Media) AddEvent(event event.Event) {
	e.events = append(e.events, event)
}

func (e *Media) Events() []event.Event {
	return e.events
}

func (e *Media) ClearEvents() {
	e.events = []event.Event{}
}

// CreateMedia 创建媒体
func (e *Media) CreateMediaAndSave() error {
	domainEvent := e.createMediaUploadedEvent()
	e.AddEvent(domainEvent)
	return nil
}

// UpdateMedia 更新媒体
func (e *Media) UpdateMedia(updates map[string]interface{}) error {
	// 检查媒体是否上锁
	if e.IsLocked == 1 {
		return errors.New("媒体已锁定，无法修改")
	}
	domainEvent := e.createMediaUpdatedEvent(updates)
	e.AddEvent(domainEvent)
	return nil
}

// ChangeIsEnforMedia 设置媒体是否为执法媒体，0为非执法媒体，1为执法媒体
func (e *Media) ChangeIsEnforMediaStatus(updates map[string]interface{}) error {
	domainEvent := e.createMediaIsNonLawEnforcementMediaUpdatedEvent(e.ID, updates)
	e.AddEvent(domainEvent)
	return nil
}

// DeleteMedia 删除媒体
func (e *Media) DeleteMedia() error {
	domainEvent := e.createMediaDeletedEvent()
	e.AddEvent(domainEvent)
	return nil
}

// UpdateIsLockedStatus 更新媒体锁定状态 - 只有媒体所有者可以操作
func (e *Media) UpdateIsLockedStatus(updateBy int, isLocked int) error {
	// 核心业务规则：只有所有者可以更新锁定状态（加锁/解锁）
	if updateBy != e.PoliceID {
		return fmt.Errorf("权限不足：只有媒体所有者(ID:%d)才能更新锁定状态，当前操作者ID:%d", e.PoliceID, updateBy)
	}

	// 其他相关业务规则
	if e.IsArchived == 1 {
		return errors.New("已归档的媒体无法修改锁定状态")
	}

	// 状态变更
	e.IsLocked = isLocked

	// 注意：批量操作不在聚合根级别创建事件，而是在领域服务中统一创建批量事件

	return nil
}

func (e *Media) createMediaUploadedEvent() *event.DomainEvent {
	payload := &event.MediaUploadedPayload{
		// 基础信息
		MediaID:   e.ID,
		MediaName: e.MediaName,

		// 媒体属性
		MediaCate:      e.MediaCate,
		MediaSuffix:    e.MediaSuffix,
		ImportantLevel: e.ImportantLevel,

		// 时间相关
		ShotTimeStart:   e.ShotTimeStart,
		ShotTime:        e.ShotTime,
		ImportTime:      e.ImportTime,
		AcquisitionTime: e.AcquisitionTime,
		ExpiryTime:      e.ExpiryTime,
		AssociateTime:   e.AssociateTime,
		ArchiveDate:     e.ArchiveDate,

		// 视频属性
		VideoClarity:  e.VideoClarity,
		VideoDuration: e.VideoDuration,
		FileSize:      e.FileSize,

		// 关联ID
		PoliceID:     e.PoliceID,
		OrgID:        e.OrgID,
		RecorderID:   e.RecorderID,
		SiteClientID: e.SiteClientID,
		TrialID:      e.TrialID,

		// 存储相关
		StorageType: e.StorageType,
		SiteID:      e.SiteID,
		StorageID:   e.StorageID,

		// 业务数据
		Sequence:              e.Sequence,
		IsNonEnforcementMedia: e.IsNonEnforcementMedia,
		Comments:              e.Comments,

		// 审计字段
		CreateBy:  e.CreateBy,
		UpdateBy:  e.UpdateBy,
		CreatedAt: e.CreatedAt,
		UpdatedAt: e.UpdatedAt,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("序列化 MediaCreatedPayload 失败", "error", err)
		return nil
	}

	return event.NewDomainEvent(event.EventTypeMediaUploaded, e.ID, "Media", payloadJSON)
}

func (e *Media) createMediaUpdatedEvent(updates map[string]interface{}) *event.DomainEvent {
	//updatedFields := make(map[string]interface{})

	// 添加所有可能被更新的字段
	//updatedFields["mediaName"] = e.MediaName
	//updatedFields["importantLevel"] = e.ImportantLevel
	//updatedFields["mediaCate"] = e.MediaCate
	//updatedFields["mediaSuffix"] = e.MediaSuffix
	//updatedFields["shotTimeStart"] = e.ShotTimeStart
	//updatedFields["shotTime"] = e.ShotTime
	//updatedFields["videoClarity"] = e.VideoClarity
	//updatedFields["videoDuration"] = e.VideoDuration
	//updatedFields["fileSize"] = e.FileSize
	//updatedFields["policeId"] = e.PoliceID
	//updatedFields["orgId"] = e.OrgID
	//updatedFields["recorderId"] = e.RecorderID
	//updatedFields["storageType"] = e.StorageType
	//updatedFields["siteId"] = e.SiteID
	//updatedFields["storageId"] = e.StorageID
	//updatedFields["siteClientId"] = e.SiteClientID
	//updatedFields["trialId"] = e.TrialID
	//updatedFields["importTime"] = e.ImportTime
	//updatedFields["associateTime"] = e.AssociateTime
	//updatedFields["sequence"] = e.Sequence
	//updatedFields["isLawEnforMedia"] = e.IsEnforMedia
	//updatedFields["comments"] = e.Comments

	payload := event.MediaUpdatedPayload{
		MediaID:       e.ID,
		UpdatedFields: updates,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 MediaUpdatedPayload 转换为 JSON", "error", err)
		return nil
	}

	return event.NewDomainEvent(event.EventTypeMediaUpdated, e.ID, "Media", payloadJSON)

}

func (e *Media) createMediaIsNonLawEnforcementMediaUpdatedEvent(id int64, updates map[string]interface{}) *event.DomainEvent {
	payload := event.MediaIsNonLawEnforcementMediaUpdatedPayload{
		MediaID:      id,
		IsEnforMedia: updates["IsEnforMedia"].(int),
		UpdateBy:     updates["UpdateBy"].(int),
		UpdatedAt:    updates["UpdatedAt"].(time.Time),
	}
	var json = jsoniter.ConfigCompatibleWithStandardLibrary

	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		logger.Error("无法将 MediaIsNonLawEnforcementMediaUpdatedPayload 转换为 JSON", "error", err)
		return nil
	}

	return event.NewDomainEvent(event.EventTypeMediaIsNonLawEnforcementMediaUpdated, 0, "Media", payloadJSON)
}

func (e *Media) createMediaDeletedEvent() *event.DomainEvent {
	payload := event.MediaDeletedPayload{
		MediaID:   e.ID,
		UpdateBy:  e.UpdateBy,
		DeletedAt: e.DeletedAt.Time,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 MediaDeletedPayload 转换为 JSON", "error", err)
		return nil
	}

	return event.NewDomainEvent(event.EventTypeMediaDeleted, e.ID, "Media", payloadJSON)
}
