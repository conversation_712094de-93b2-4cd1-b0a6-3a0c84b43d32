@startuml
:                **index.html**
          查询执法数据列表
       //__enfor/queryEnforTyps__//
 //__alarm/getDicNamebyAlrmId__//
          //__alarmEs/getDatas__//
          查询执法数据详情
      //__alarm/getAlarmByCode__//;
 floating note
        前端执法
        管理模块
 end note

   :               **video_relation.html**
                  **video_relation.js**
                       查询视频集
              //__enfor/queryEnforType__//
          //__media/getCalcVideosByPS__//
                 标注不是执法视频
//__media/updateRemarkNoEnforVideo__//
                  往视频集添加视频
                   //__media/getDatas__//
            //__media/addToCurVideos__//
                  从视频集删除视频
 //__media/deleteRemoveToCurVideos__//
                               播放
          //__media/queryPlayVideosByIds__//
             //__media/queryEncryptFile__//
           //__media/downloadMediaFile__//;

:            **file_information.html**
                    **file_show.js**
          查询创建档案需要的数据
             //__enfor/queryEnforType__//
               //__doc/getRandDocNo__//
                //__alarm/getAlarm__//
             //__media/getVideosByIds__//
                从档案中删除视频
//__doc/deleteRemoveToCurDocTemp__//
                向档案中添加视频
               //__enfor/queryEnforTyps__//
                   //__media/getDatas__//
             //__media/addToCurVideos__//
                              播放
           //__media/queryVideosByMark__//
            //__media/queryEncryptFile__//
           //__media/downloadMediaFile__//
                          保存档案
                       //__doc/saveDoc__//;


@enduml
