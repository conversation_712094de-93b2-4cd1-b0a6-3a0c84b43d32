package database

import (
	"log"
	"reflect"
	"strings"

	"github.com/ChenBigdata421/jxt-core/sdk"
	toolsConfig "github.com/ChenBigdata421/jxt-core/sdk/config"
	"github.com/ChenBigdata421/jxt-core/sdk/pkg"
	mycasbin "github.com/ChenBigdata421/jxt-core/sdk/pkg/casbin"
	toolsDB "github.com/ChenBigdata421/jxt-core/tools/database"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"

	mylogger "github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

// Setup 配置主数据库（用户信息，角色信息，cashbin信息在这个库）
func MasterDbSetup() {
	// 如果未配置多租户，只初始化默认数据库，唯一的tenantID设置为"*"
	if !toolsConfig.TenantsConfig.Enabled {
		setupSimpleDatabase("*", toolsConfig.DatabaseConfig)
		return
	}

	// 如果多租户为true，则初始化每个租户的数据库
	for k := range toolsConfig.TenantsConfig.List {
		setupSimpleDatabase(toolsConfig.TenantsConfig.List[k].ID, &toolsConfig.TenantsConfig.List[k].Database)
	}
}

// Setup 配置命令数据库,支持多数据库连接,k可以理解成租户id
func CommandDbSetup() {
	// 如果未配置多租户，只初始化默认数据库，唯一的tenantID设置为"*"
	if !toolsConfig.TenantsConfig.Enabled {
		setupSimpleCommandDatabase("*", toolsConfig.DatabaseConfig)
		return
	}

	// 如果多租户为true，则初始化每个租户的数据库
	for k := range toolsConfig.TenantsConfig.List {
		setupSimpleCommandDatabase(toolsConfig.TenantsConfig.List[k].ID, &toolsConfig.TenantsConfig.List[k].Database)
	}
}

// Setup 配置查询数据库,支持多数据库连接,k可以理解成租户id
func QueryDbSetup() {
	// 如果未配置多租户，只初始化默认数据库，唯一的tenantID设置为"*"
	if !toolsConfig.TenantsConfig.Enabled {
		setupSimpleQueryDatabase("*", toolsConfig.DatabaseConfig)
		return
	}

	// 如果多租户为true，则初始化每个租户的数据库
	for k := range toolsConfig.TenantsConfig.List {
		setupSimpleQueryDatabase(toolsConfig.TenantsConfig.List[k].ID, &toolsConfig.TenantsConfig.List[k].Database)
	}
}

/*
dbresolver 插件为 GORM 提供了数据库读写分离和分片的功能支持。通过配置该插件，
开发者可以轻松地实现数据库的读写分离和分片策略
*/
func setupSimpleDatabase(tenantID string, c *toolsConfig.Database) {
	// 获取配置的优先级逻辑：
	// 1. 如果多租户为false，直接从config.Database获取
	// 2. 如果多租户为true：
	//    a. 先从Tenants.list[tenantID].Database获取
	//    b. 如果没有，再从Tenants.Defaults.Database获取
	//    c. 如果还没有，最后从config.Database获取
	getConfig := func(field string, required bool) interface{} {
		var result interface{}

		// 如果多租户为false，直接从config.Database获取
		if !toolsConfig.TenantsConfig.Enabled {
			result = getFieldValue(toolsConfig.DatabaseConfig, field)
		} else {
			// 多租户为true的情况
			// 1. 先从当前租户配置获取
			if c != nil {
				result = getFieldValue(c, field)
				if result != nil && result != "" && result != 0 {
					return result
				}
			}

			// 2. 然后从默认租户配置获取
			result = getFieldValue(&toolsConfig.TenantsConfig.Defaults.Database, field)
			if result != nil && result != "" && result != 0 {
				return result
			}

			// 3. 最后从全局数据库配置获取
			result = getFieldValue(toolsConfig.DatabaseConfig, field)
		}

		// 如果结果为nil或空值
		if result == nil || result == "" || result == 0 {
			// 如果是必需的配置项，报错
			if required {
				log.Fatalf("错误：无法从配置中获取 %s，请检查配置文件", field)
			}
			// 否则返回nil
			return nil
		}

		return result
	}

	// 1. 获取所有配置值
	// 获取数据库驱动（必需）
	driverValue := getConfig("MasterDB.Driver", true)
	var driverStr string
	if str, ok := driverValue.(string); ok {
		driverStr = str
	} else {
		log.Fatalf("错误：MasterDB.Driver 不是字符串类型：%v", driverValue)
	}

	// 获取数据库连接字符串（必需）
	sourceValue := getConfig("MasterDB.Source", true)
	var sourceStr string
	if str, ok := sourceValue.(string); ok {
		sourceStr = str
	} else {
		log.Fatalf("错误：MasterDB.Source 不是字符串类型：%v", sourceValue)
	}

	// 获取连接池配置（非必需，有默认值）
	maxIdleConnsValue := getConfig("MasterDB.MaxIdleConns", false)
	maxOpenConnsValue := getConfig("MasterDB.MaxOpenConns", false)
	connMaxIdleTimeValue := getConfig("MasterDB.ConnMaxIdleTime", false)
	connMaxLifeTimeValue := getConfig("MasterDB.ConnMaxLifeTime", false)

	// 转换为整数类型，并设置默认值
	maxIdleConns := 10 // 默认值
	if maxIdleConnsValue != nil {
		if val, ok := maxIdleConnsValue.(int); ok && val > 0 {
			maxIdleConns = val
		} else {
			log.Printf("警告：MasterDB.MaxIdleConns 不是有效的整数类型，使用默认值：%d", maxIdleConns)
		}
	}

	maxOpenConns := 100 // 默认值
	if maxOpenConnsValue != nil {
		if val, ok := maxOpenConnsValue.(int); ok && val > 0 {
			maxOpenConns = val
		} else {
			log.Printf("警告：MasterDB.MaxOpenConns 不是有效的整数类型，使用默认值：%d", maxOpenConns)
		}
	}

	connMaxIdleTime := 60 // 默认值
	if connMaxIdleTimeValue != nil {
		if val, ok := connMaxIdleTimeValue.(int); ok && val > 0 {
			connMaxIdleTime = val
		} else {
			log.Printf("警告：MasterDB.ConnMaxIdleTime 不是有效的整数类型，使用默认值：%d", connMaxIdleTime)
		}
	}

	connMaxLifeTime := 3600 // 默认值
	if connMaxLifeTimeValue != nil {
		if val, ok := connMaxLifeTimeValue.(int); ok && val > 0 {
			connMaxLifeTime = val
		} else {
			log.Printf("警告：MasterDB.ConnMaxLifeTime 不是有效的整数类型，使用默认值：%d", connMaxLifeTime)
		}
	}

	// 2. 检查配置值的有效性
	// 检查驱动类型是否支持
	if _, ok := opens[driverStr]; !ok {
		log.Printf("警告：不支持的数据库驱动类型 %s，将使用默认的 postgres", driverStr)
		driverStr = "postgres"
	}

	// 打印连接信息
	log.Printf("%s => %s", tenantID, pkg.Green(sourceStr))

	// 处理注册器
	var registers []toolsDB.ResolverConfigure
	if c != nil && len(c.MasterDB.Registers) > 0 {
		registers = make([]toolsDB.ResolverConfigure, len(c.MasterDB.Registers))
		for i := range c.MasterDB.Registers {
			registers[i] = toolsDB.NewResolverConfigure(
				c.MasterDB.Registers[i].Sources,
				c.MasterDB.Registers[i].Replicas,
				c.MasterDB.Registers[i].Policy,
				c.MasterDB.Registers[i].Tables)
		}
	} else {
		registers = []toolsDB.ResolverConfigure{}
	}

	// 3. 使用配置值初始化数据库
	resolverConfig := toolsDB.NewConfigure(
		sourceStr,
		maxIdleConns,
		maxOpenConns,
		connMaxIdleTime,
		connMaxLifeTime,
		registers)

	db, err := resolverConfig.Init(&gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
		Logger: mylogger.NewGormLogger(mylogger.Logger, toolsConfig.LoggerConfig.GormLoggerLevel),
	}, opens[driverStr])

	if err != nil {
		log.Fatalf(pkg.Red("%s connect error : %v\n"), driverStr, err)
	} else {
		log.Printf(pkg.Green("%s connect success ! \n"), driverStr)
	}

	e := mycasbin.Setup(db, "")

	sdk.Runtime.SetTenantDB(tenantID, db)
	sdk.Runtime.SetTenantCasbin(tenantID, e)
}

func setupSimpleCommandDatabase(tenantID string, c *toolsConfig.Database) {

	// 获取配置的优先级逻辑：
	// 1. 如果多租户为false，直接从config.Database获取
	// 2. 如果多租户为true：
	//    a. 先从Tenants.list[tenantID].Database获取
	//    b. 如果没有，再从Tenants.Defaults.Database获取
	//    c. 如果还没有，最后从config.Database获取
	getConfig := func(field string, required bool) interface{} {
		var result interface{}

		// 如果多租户为false，直接从config.Database获取
		if !toolsConfig.TenantsConfig.Enabled {
			result = getFieldValue(toolsConfig.DatabaseConfig, field)
		} else {
			// 多租户为true的情况
			// 1. 先从当前租户配置获取
			if c != nil {
				result = getFieldValue(c, field)
				if result != nil && result != "" && result != 0 {
					return result
				}
			}

			// 2. 然后从默认租户配置获取
			result = getFieldValue(&toolsConfig.TenantsConfig.Defaults.Database, field)
			if result != nil && result != "" && result != 0 {
				return result
			}

			// 3. 最后从全局数据库配置获取
			result = getFieldValue(toolsConfig.DatabaseConfig, field)
		}

		// 如果结果为nil或空值
		if result == nil || result == "" || result == 0 {
			// 如果是必需的配置项，报错
			if required {
				log.Fatalf("错误：无法从配置中获取 %s，请检查配置文件", field)
			}
			// 否则返回nil
			return nil
		}

		return result
	}

	// 1. 获取所有配置值
	// 获取数据库驱动（必需）
	driverValue := getConfig("CommandDB.Driver", true)
	var driverStr string
	if str, ok := driverValue.(string); ok {
		driverStr = str
	} else {
		log.Fatalf("错误：CommandDB.Driver 不是字符串类型：%v", driverValue)
	}

	// 获取数据库连接字符串（必需）
	sourceValue := getConfig("CommandDB.Source", true)
	var sourceStr string
	if str, ok := sourceValue.(string); ok {
		sourceStr = str
	} else {
		log.Fatalf("错误：CommandDB.Source 不是字符串类型：%v", sourceValue)
	}

	// 获取连接池配置（非必需，有默认值）
	maxIdleConnsValue := getConfig("CommandDB.MaxIdleConns", false)
	maxOpenConnsValue := getConfig("CommandDB.MaxOpenConns", false)
	connMaxIdleTimeValue := getConfig("CommandDB.ConnMaxIdleTime", false)
	connMaxLifeTimeValue := getConfig("CommandDB.ConnMaxLifeTime", false)

	// 转换为整数类型，并设置默认值
	maxIdleConns := 10 // 默认值
	if maxIdleConnsValue != nil {
		if val, ok := maxIdleConnsValue.(int); ok && val > 0 {
			maxIdleConns = val
		} else {
			log.Printf("警告：CommandDB.MaxIdleConns 不是有效的整数类型，使用默认值：%d", maxIdleConns)
		}
	}

	maxOpenConns := 100 // 默认值
	if maxOpenConnsValue != nil {
		if val, ok := maxOpenConnsValue.(int); ok && val > 0 {
			maxOpenConns = val
		} else {
			log.Printf("警告：CommandDB.MaxOpenConns 不是有效的整数类型，使用默认值：%d", maxOpenConns)
		}
	}

	connMaxIdleTime := 60 // 默认值
	if connMaxIdleTimeValue != nil {
		if val, ok := connMaxIdleTimeValue.(int); ok && val > 0 {
			connMaxIdleTime = val
		} else {
			log.Printf("警告：CommandDB.ConnMaxIdleTime 不是有效的整数类型，使用默认值：%d", connMaxIdleTime)
		}
	}

	connMaxLifeTime := 3600 // 默认值
	if connMaxLifeTimeValue != nil {
		if val, ok := connMaxLifeTimeValue.(int); ok && val > 0 {
			connMaxLifeTime = val
		} else {
			log.Printf("警告：CommandDB.ConnMaxLifeTime 不是有效的整数类型，使用默认值：%d", connMaxLifeTime)
		}
	}

	// 2. 检查配置值的有效性
	// 检查驱动类型是否支持
	if _, ok := opens[driverStr]; !ok {
		log.Printf("警告：不支持的数据库驱动类型 %s，将使用默认的 postgres", driverStr)
		driverStr = "postgres"
	}

	// 本文件用golang自带log不用zaplogger，目的是为了能带颜色打印
	// 打印连接信息
	log.Printf("%s => %s", tenantID, pkg.Green(sourceStr))

	// 处理注册器
	var registers []toolsDB.ResolverConfigure
	if c != nil && len(c.CommandDB.Registers) > 0 {
		registers := make([]toolsDB.ResolverConfigure, len(c.CommandDB.Registers))
		for i := range c.CommandDB.Registers {
			registers[i] = toolsDB.NewResolverConfigure(
				c.CommandDB.Registers[i].Sources,
				c.CommandDB.Registers[i].Replicas,
				c.CommandDB.Registers[i].Policy,
				c.CommandDB.Registers[i].Tables)
		}
	} else {
		registers = []toolsDB.ResolverConfigure{}
	}

	// 3. 使用配置值初始化数据库
	resolverConfig := toolsDB.NewConfigure(
		sourceStr,
		maxIdleConns,
		maxOpenConns,
		connMaxIdleTime,
		connMaxLifeTime,
		registers)

	db, err := resolverConfig.Init(&gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
		Logger: mylogger.NewGormLogger(mylogger.Logger, toolsConfig.LoggerConfig.GormLoggerLevel),
	}, opens[driverStr])

	if err != nil {
		log.Fatalf(pkg.Red("%s %s connect error : %v\n"), driverStr, sourceStr, err)
	} else {
		log.Printf(pkg.Green("%s %s connect success ! \n"), driverStr, sourceStr)
	}

	sdk.Runtime.SetTenantCommandDB(tenantID, db) //把db写入application结构，在gin中间件中再从applicaion获取db，写入gin.Context
}

func setupSimpleQueryDatabase(tenantID string, c *toolsConfig.Database) {

	// 获取配置的优先级逻辑：
	// 1. 如果多租户为false，直接从config.Database获取
	// 2. 如果多租户为true：
	//    a. 先从Tenants.list[tenantID].Database获取
	//    b. 如果没有，再从Tenants.Defaults.Database获取
	//    c. 如果还没有，最后从config.Database获取
	getConfig := func(field string, required bool) interface{} {
		var result interface{}

		// 如果多租户为false，直接从config.Database获取
		if !toolsConfig.TenantsConfig.Enabled {
			result = getFieldValue(toolsConfig.DatabaseConfig, field)
		} else {
			// 多租户为true的情况
			// 1. 先从当前租户配置获取
			if c != nil {
				result = getFieldValue(c, field)
				if result != nil && result != "" && result != 0 {
					return result
				}
			}

			// 2. 然后从默认租户配置获取
			result = getFieldValue(&toolsConfig.TenantsConfig.Defaults.Database, field)
			if result != nil && result != "" && result != 0 {
				return result
			}

			// 3. 最后从全局数据库配置获取
			result = getFieldValue(toolsConfig.DatabaseConfig, field)
		}

		// 如果结果为nil或空值
		if result == nil || result == "" || result == 0 {
			// 如果是必需的配置项，报错
			if required {
				log.Fatalf("错误：无法从配置中获取 %s，请检查配置文件", field)
			}
			// 否则返回nil
			return nil
		}

		return result
	}

	// 1. 获取所有配置值
	// 获取数据库驱动（必需）
	driverValue := getConfig("QueryDB.Driver", true)
	var driverStr string
	if str, ok := driverValue.(string); ok {
		driverStr = str
	} else {
		log.Fatalf("错误：QueryDB.Driver 不是字符串类型：%v", driverValue)
	}

	// 获取数据库连接字符串（必需）
	sourceValue := getConfig("QueryDB.Source", true)
	var sourceStr string
	if str, ok := sourceValue.(string); ok {
		sourceStr = str
	} else {
		log.Fatalf("错误：QueryDB.Source 不是字符串类型：%v", sourceValue)
	}

	// 获取连接池配置（非必需，有默认值）
	maxIdleConnsValue := getConfig("QueryDB.MaxIdleConns", false)
	maxOpenConnsValue := getConfig("QueryDB.MaxOpenConns", false)
	connMaxIdleTimeValue := getConfig("QueryDB.ConnMaxIdleTime", false)
	connMaxLifeTimeValue := getConfig("QueryDB.ConnMaxLifeTime", false)

	// 转换为整数类型，并设置默认值
	maxIdleConns := 10 // 默认值
	if maxIdleConnsValue != nil {
		if val, ok := maxIdleConnsValue.(int); ok && val > 0 {
			maxIdleConns = val
		} else {
			log.Printf("警告：QueryDB.MaxIdleConns 不是有效的整数类型，使用默认值：%d", maxIdleConns)
		}
	}

	maxOpenConns := 100 // 默认值
	if maxOpenConnsValue != nil {
		if val, ok := maxOpenConnsValue.(int); ok && val > 0 {
			maxOpenConns = val
		} else {
			log.Printf("警告：QueryDB.MaxOpenConns 不是有效的整数类型，使用默认值：%d", maxOpenConns)
		}
	}

	connMaxIdleTime := 60 // 默认值
	if connMaxIdleTimeValue != nil {
		if val, ok := connMaxIdleTimeValue.(int); ok && val > 0 {
			connMaxIdleTime = val
		} else {
			log.Printf("警告：QueryDB.ConnMaxIdleTime 不是有效的整数类型，使用默认值：%d", connMaxIdleTime)
		}
	}

	connMaxLifeTime := 3600 // 默认值
	if connMaxLifeTimeValue != nil {
		if val, ok := connMaxLifeTimeValue.(int); ok && val > 0 {
			connMaxLifeTime = val
		} else {
			log.Printf("警告：QueryDB.ConnMaxLifeTime 不是有效的整数类型，使用默认值：%d", connMaxLifeTime)
		}
	}

	// 2. 检查配置值的有效性
	// 检查驱动类型是否支持
	if _, ok := opens[driverStr]; !ok {
		log.Printf("警告：不支持的数据库驱动类型 %s，将使用默认的 postgres", driverStr)
		driverStr = "postgres"
	}

	// 本文件用golang自带log不用zaplogger，目的是为了能带颜色打印
	log.Printf("%s => %s \n", tenantID, pkg.Green(sourceStr))

	// 处理注册器
	var registers []toolsDB.ResolverConfigure
	if c != nil && len(c.QueryDB.Registers) > 0 {
		registers := make([]toolsDB.ResolverConfigure, len(c.QueryDB.Registers))
		for i := range c.QueryDB.Registers {
			registers[i] = toolsDB.NewResolverConfigure(
				c.QueryDB.Registers[i].Sources,
				c.QueryDB.Registers[i].Replicas,
				c.QueryDB.Registers[i].Policy,
				c.QueryDB.Registers[i].Tables)
		}
	} else {
		registers = []toolsDB.ResolverConfigure{}
	}

	// 3. 使用配置值初始化数据库
	resolverConfig := toolsDB.NewConfigure(
		sourceStr,
		maxIdleConns,
		maxOpenConns,
		connMaxIdleTime,
		connMaxLifeTime,
		registers)

	db, err := resolverConfig.Init(&gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
		Logger: mylogger.NewGormLogger(mylogger.Logger, toolsConfig.LoggerConfig.GormLoggerLevel),
	}, opens[driverStr])

	if err != nil {
		log.Fatalf(pkg.Red("%s %s connect error : %v\n"), driverStr, sourceStr, err)
	} else {
		log.Printf(pkg.Green("%s %s connect success ! \n"), driverStr, sourceStr)
	}

	sdk.Runtime.SetTenantQueryDB(tenantID, db) //把db写入application结构，在gin中间件中再从applicaion获取db，写入gin.Context
}

// getFieldValue 通用获取字段值函数，支持Database和DatabaseDefaults类型
func getFieldValue(config interface{}, field string) interface{} {
	if config == nil {
		return 0 // 返回0而不是空字符串
	}

	r := reflect.ValueOf(config)
	if r.Kind() == reflect.Ptr {
		if r.IsNil() {
			return 0
		}
		r = r.Elem()
	}

	// 处理嵌套字段，如 "MasterDB.Driver"
	fields := strings.Split(field, ".")
	current := r

	for _, f := range fields {
		if !current.IsValid() {
			log.Printf("警告: 字段路径 %s 无效", field)
			return 0
		}

		if current.Kind() == reflect.Struct {
			current = current.FieldByName(f)
		} else {
			log.Printf("警告: 字段路径 %s 不是结构体", field)
			return 0
		}
	}

	if !current.IsValid() {
		log.Printf("警告: 字段 %s 不存在", field)
		return 0
	}

	return current.Interface()
}

// getIntConfig 获取整数类型配置
func getIntConfig(c *toolsConfig.Database, field string, defaultValue int) int {
	// 使用与setupSimpleDatabase中相同的逻辑获取配置
	// 1. 如果多租户为false，直接从config.Database获取
	if !toolsConfig.TenantsConfig.Enabled {
		val := getFieldValue(toolsConfig.DatabaseConfig, field)
		if intVal, ok := val.(int); ok && intVal != 0 {
			return intVal
		}
		return defaultValue
	}

	// 2. 多租户为true的情况
	// a. 先从当前租户配置获取
	if c != nil {
		val := getFieldValue(c, field)
		if intVal, ok := val.(int); ok && intVal != 0 {
			return intVal
		}
	}

	// b. 然后从默认租户配置获取
	val := getFieldValue(&toolsConfig.TenantsConfig.Defaults.Database, field)
	if intVal, ok := val.(int); ok && intVal != 0 {
		return intVal
	}

	// c. 最后从全局数据库配置获取
	val = getFieldValue(toolsConfig.DatabaseConfig, field)
	if intVal, ok := val.(int); ok && intVal != 0 {
		return intVal
	}

	return defaultValue
}
