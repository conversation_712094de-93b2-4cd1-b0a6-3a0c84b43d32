package version

import (
	"runtime"

	"github.com/ChenBigdata421/jxt-core/sdk/config"

	"jxt-evidence-system/evidence-management/query/cmd/migrate/migration"
	"jxt-evidence-system/evidence-management/query/cmd/migrate/migration/models"
	common "jxt-evidence-system/evidence-management/shared/common/models"

	"gorm.io/gorm"
)

func init() {
	_, fileName, _, _ := runtime.Caller(0)
	migration.Migrate.SetVersion(migration.GetFilename(fileName), _1599190683659Tables)
}

func _1599190683659Tables(db *gorm.DB, version string) error {
	return db.Transaction(func(tx *gorm.DB) error {
		if config.DatabaseConfig.QueryDB.Driver == "mysql" {
			tx = tx.Set("gorm:table_options", "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4")
		}
		err := tx.Migrator().AutoMigrate(

			new(models.SysMenu), //必须migrate创建
			//new(models.SysApi),  //为什么这里即使不创建SysApi,查数据库却有呢？
			new(models.SysJob),

			new(models.MediaReadModel),
			new(models.EnforcementTypeReadModel),
			new(models.ArchiveMediaRelationReadModel),
		)
		if err != nil {
			return err
		}
		if err := models.InitDb(tx, config.DatabaseConfig.QueryDB.Driver); err != nil {
			return err
		}
		return tx.Create(&common.Migration{
			Version: version,
		}).Error
	})
}
