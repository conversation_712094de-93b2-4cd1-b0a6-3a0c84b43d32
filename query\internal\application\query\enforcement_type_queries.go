package query

import (
	"jxt-evidence-system/evidence-management/shared/common/query"
	"time"
)

// EnforcementTypeDTO 执法类型数据传输对象
//
//	可以用来返回查询结果，省略前端不需要的模型字段，添加其他前端需要的来自其他模型的字段
type EnforcementTypeDTO struct {
	ID                  int64                 `json:"id"`              //主键ID
	EnforcementTypeCode string                `json:"enforceTypeCode"` //执行类型编码
	EnforcementTypeName string                `json:"enforceTypeName"` //执行类型名称
	EnforcementTypeDesc string                `json:"enforceTypeDesc"` //执行类型描述
	EnforcementTypePath string                `json:"enforceTypePath"` //执法类型路径
	ParentId            int64                 `json:"parentId"`        //上级部门
	Source              string                `json:"source"`          //执法类型来源
	Sort                int                   `json:"sort"`            //排序
	CreateBy            int                   `json:"createBy"`        //创建者
	UpdateBy            int                   `json:"updateBy"`        //更新者
	CreatedAt           time.Time             `json:"createdAt"`       //创建时间
	UpdatedAt           time.Time             `json:"updatedAt"`       //更新时间
	Children            []*EnforcementTypeDTO `json:"children"`        //子节点
}

// GetEnforcementTypePagedQuery 执法类型分页查询
type EnforcementTypePagedQuery struct {
	query.Pagination    `search:"-"`
	EnforcementTypeCode string `form:"enforcementTypeCode" search:"type:exact;column:enforcement_type_code;table:t_evidence_enforcement_types_read"`
	EnforcementTypeName string `form:"enforcementTypeName" search:"type:contains;column:enforcement_type_name;table:t_evidence_enforcement_types_read"`
	EnforcementTypeDesc string `form:"enforcementTypeDesc" search:"type:contains;column:enforcement_type_desc;table:t_evidence_enforcement_types_read"`
	ParentId            int64  `form:"parentId" search:"type:exact;column:parent_id;table:t_evidence_enforcement_types_read"`
	Source              string `form:"source" search:"type:exact;column:source;table:t_evidence_enforcement_types_read"`
	EnforcementTypeOrder
}

type EnforcementTypeOrder struct {
	EnforcementTypeCode string `search:"type:order;column:enforcement_type_code;table:t_evidence_enforcement_types_read" form:"enforcementTypeCodeOrder"`
	ParentId            string `search:"type:order;column:parent_id;table:t_evidence_enforcement_types_read" form:"parentIdOrder"`
	CreatedAtOrder      string `search:"type:order;column:created_at;table:t_evidence_enforcement_types_read" form:"createdAtOrder"`
}

func (m *EnforcementTypePagedQuery) GetNeedSearch() interface{} {
	return *m
}
