package testhelpers

import (
	"jxt-evidence-system/evidence-management/command/internal/application/command"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archive"
	"time"
)

// CreateArchiveCommandBuilder 创建档案构建器
type CreateArchiveCommandBuilder struct {
	command *command.CreateArchiveCommand
}

func NewCreateArchiveCommandBuilder() *CreateArchiveCommandBuilder {
	return &CreateArchiveCommandBuilder{
		command: &command.CreateArchiveCommand{
			ArchiveTitle:    "测试档案",
			ArchiveType:     1,
			Description:     "测试档案描述",
			OrgID:           1,
			StorageDuration: 12,
			Remarks:         "测试备注",
		},
	}
}

func (b *CreateArchiveCommandBuilder) WithTitle(title string) *CreateArchiveCommandBuilder {
	b.command.ArchiveTitle = title
	return b
}

func (b *CreateArchiveCommandBuilder) WithType(archiveType int) *CreateArchiveCommandBuilder {
	b.command.ArchiveType = archiveType
	return b
}

func (b *CreateArchiveCommandBuilder) WithDescription(description string) *CreateArchiveCommandBuilder {
	b.command.Description = description
	return b
}

func (b *CreateArchiveCommandBuilder) WithOrgID(orgID int) *CreateArchiveCommandBuilder {
	b.command.OrgID = orgID
	return b
}

func (b *CreateArchiveCommandBuilder) WithStorageDuration(duration int) *CreateArchiveCommandBuilder {
	b.command.StorageDuration = duration
	return b
}

func (b *CreateArchiveCommandBuilder) WithRemarks(remarks string) *CreateArchiveCommandBuilder {
	b.command.Remarks = remarks
	return b
}

func (b *CreateArchiveCommandBuilder) Build() *command.CreateArchiveCommand {
	return b.command
}

// UpdateArchiveCommandBuilder 更新档案构建器
type UpdateArchiveCommandBuilder struct {
	command *command.UpdateArchiveCommand
}

func NewUpdateArchiveCommandBuilder() *UpdateArchiveCommandBuilder {
	return &UpdateArchiveCommandBuilder{
		command: &command.UpdateArchiveCommand{},
	}
}

func (b *UpdateArchiveCommandBuilder) WithID(id int64) *UpdateArchiveCommandBuilder {
	b.command.ID = id
	return b
}

func (b *UpdateArchiveCommandBuilder) WithTitle(title string) *UpdateArchiveCommandBuilder {
	b.command.ArchiveTitle = &title
	return b
}

func (b *UpdateArchiveCommandBuilder) WithType(archiveType int) *UpdateArchiveCommandBuilder {
	b.command.ArchiveType = &archiveType
	return b
}

func (b *UpdateArchiveCommandBuilder) WithDescription(description string) *UpdateArchiveCommandBuilder {
	b.command.Description = &description
	return b
}

func (b *UpdateArchiveCommandBuilder) WithOrgID(orgID int) *UpdateArchiveCommandBuilder {
	b.command.OrgID = &orgID
	return b
}

func (b *UpdateArchiveCommandBuilder) WithStorageDuration(duration int) *UpdateArchiveCommandBuilder {
	b.command.StorageDuration = &duration
	return b
}

func (b *UpdateArchiveCommandBuilder) WithExpirationTime(expirationTime *time.Time) *UpdateArchiveCommandBuilder {
	b.command.ExpirationTime = expirationTime
	return b
}

func (b *UpdateArchiveCommandBuilder) WithStatus(status int) *UpdateArchiveCommandBuilder {
	b.command.Status = &status
	return b
}

func (b *UpdateArchiveCommandBuilder) WithRemarks(remarks string) *UpdateArchiveCommandBuilder {
	b.command.Remarks = &remarks
	return b
}

func (b *UpdateArchiveCommandBuilder) Build() *command.UpdateArchiveCommand {
	return b.command
}

// BatchUpdateArchiveCommandBuilder 批量更新档案构建器
type BatchUpdateArchiveCommandBuilder struct {
	command *command.BatchUpdateArchiveCommand
}

func NewBatchUpdateArchiveCommandBuilder() *BatchUpdateArchiveCommandBuilder {
	return &BatchUpdateArchiveCommandBuilder{
		command: &command.BatchUpdateArchiveCommand{},
	}
}

func (b *BatchUpdateArchiveCommandBuilder) WithIDs(ids []int64) *BatchUpdateArchiveCommandBuilder {
	b.command.IDs = ids
	return b
}

func (b *BatchUpdateArchiveCommandBuilder) WithType(archiveType int) *BatchUpdateArchiveCommandBuilder {
	b.command.ArchiveType = &archiveType
	return b
}

func (b *BatchUpdateArchiveCommandBuilder) WithStorageDuration(duration int) *BatchUpdateArchiveCommandBuilder {
	b.command.StorageDuration = &duration
	return b
}

func (b *BatchUpdateArchiveCommandBuilder) WithExpirationTime(expirationTime *time.Time) *BatchUpdateArchiveCommandBuilder {
	b.command.ExpirationTime = expirationTime
	return b
}

func (b *BatchUpdateArchiveCommandBuilder) WithStatus(status int) *BatchUpdateArchiveCommandBuilder {
	b.command.Status = &status
	return b
}

func (b *BatchUpdateArchiveCommandBuilder) WithRemarks(remarks string) *BatchUpdateArchiveCommandBuilder {
	b.command.Remarks = &remarks
	return b
}

func (b *BatchUpdateArchiveCommandBuilder) Build() *command.BatchUpdateArchiveCommand {
	return b.command
}

// BatchDeleteArchiveCommandBuilder 批量删除档案构建器
type BatchDeleteArchiveCommandBuilder struct {
	command *command.BatchDeleteArchiveCommand
}

func NewBatchDeleteArchiveCommandBuilder() *BatchDeleteArchiveCommandBuilder {
	return &BatchDeleteArchiveCommandBuilder{
		command: &command.BatchDeleteArchiveCommand{},
	}
}

func (b *BatchDeleteArchiveCommandBuilder) WithIDs(ids []int64) *BatchDeleteArchiveCommandBuilder {
	b.command.IDs = ids
	return b
}

func (b *BatchDeleteArchiveCommandBuilder) Build() *command.BatchDeleteArchiveCommand {
	return b.command
}

// ArchiveBuilder 档案聚合根构建器
type ArchiveBuilder struct {
	archive *archive.Archive
}

func NewArchiveBuilder() *ArchiveBuilder {
	now := time.Now()
	return &ArchiveBuilder{
		archive: &archive.Archive{
			ArchiveCode:     "AR0001T01-01926b8e-3c4a-7890-abcd-ef1234567890",
			ArchiveTitle:    "测试档案",
			ArchiveType:     1,
			Description:     "测试档案描述",
			OrgID:           1,
			StorageDuration: 12,
			ExpirationTime:  &now,
			Status:          0,
			Remarks:         "测试备注",
		},
	}
}

func (b *ArchiveBuilder) WithID(id int64) *ArchiveBuilder {
	b.archive.ID = id
	return b
}

func (b *ArchiveBuilder) WithCode(code string) *ArchiveBuilder {
	b.archive.ArchiveCode = code
	return b
}

func (b *ArchiveBuilder) WithTitle(title string) *ArchiveBuilder {
	b.archive.ArchiveTitle = title
	return b
}

func (b *ArchiveBuilder) WithType(archiveType int) *ArchiveBuilder {
	b.archive.ArchiveType = archiveType
	return b
}

func (b *ArchiveBuilder) WithDescription(description string) *ArchiveBuilder {
	b.archive.Description = description
	return b
}

func (b *ArchiveBuilder) WithOrgID(orgID int) *ArchiveBuilder {
	b.archive.OrgID = orgID
	return b
}

func (b *ArchiveBuilder) WithStorageDuration(duration int) *ArchiveBuilder {
	b.archive.StorageDuration = duration
	return b
}

func (b *ArchiveBuilder) WithExpirationTime(expirationTime *time.Time) *ArchiveBuilder {
	b.archive.ExpirationTime = expirationTime
	return b
}

func (b *ArchiveBuilder) WithStatus(status int) *ArchiveBuilder {
	b.archive.Status = status
	return b
}

func (b *ArchiveBuilder) WithRemarks(remarks string) *ArchiveBuilder {
	b.archive.Remarks = remarks
	return b
}

func (b *ArchiveBuilder) WithCreatedAt(createdAt time.Time) *ArchiveBuilder {
	b.archive.CreatedAt = createdAt
	return b
}

func (b *ArchiveBuilder) WithUpdatedAt(updatedAt time.Time) *ArchiveBuilder {
	b.archive.UpdatedAt = updatedAt
	return b
}

func (b *ArchiveBuilder) Build() *archive.Archive {
	return b.archive
}
