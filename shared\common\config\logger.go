package config

// Logger 日志配置
type Logger struct {
	Path            string `mapstructure:"path"`
	Stdout          string `mapstructure:"stdout"`
	Level           string `mapstructure:"level"`
	EnabledDB       bool   `mapstructure:"enableddb"`
	MaxSize         int    `mapstructure:"maxsize"`
	ErrorMaxAge     int    `mapstructure:"errormaxage"`
	InfoMaxAge      int    `mapstructure:"infomaxage"`
	GormLoggerLevel int    `mapstructure:"gormloggerlevel"`
	Cap             uint   `mapstructure:"cap"`
	DaysToKeep      uint   `mapstructure:"daystokeep"`
}
