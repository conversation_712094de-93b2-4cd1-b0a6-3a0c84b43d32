package archivemediarelation

import (
	"errors"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/archive"
	"jxt-evidence-system/evidence-management/command/internal/domain/aggregate/media"
	"jxt-evidence-system/evidence-management/shared/common/models"
	"jxt-evidence-system/evidence-management/shared/domain/event"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
	jsoniter "github.com/json-iterator/go"
)

// ArchiveMediaRelation 档案媒体关联聚合根
type ArchiveMediaRelation struct {
	ID        int64 `json:"id" gorm:"primaryKey;column:id;autoIncrement;comment:主键"`
	ArchiveId int64 `json:"archiveId" gorm:"column:archive_id;comment:档案ID，外键关联t_evidence_archives.archive_id"`
	MediaId   int64 `json:"mediaId" gorm:"column:media_id;comment:媒体ID，外键关联t_evidence_media.media_id"`

	// 显性定义关联关系
	Archive *archive.Archive `json:"archive,omitempty" gorm:"foreignKey:ArchiveId;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`
	Media   *media.Media     `json:"media,omitempty" gorm:"foreignKey:MediaId;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;"`

	// 审计字段
	models.ControlBy
	models.ModelTime

	// 领域事件
	events []event.Event `gorm:"-"` // 使用 gorm:"-" 标签明确告诉 GORM 忽略这个字段
}

func (*ArchiveMediaRelation) TableName() string {
	return "t_archive_media_relations"
}

func (e *ArchiveMediaRelation) Generate() models.ActiveRecord {
	o := *e
	return &o
}

func (e *ArchiveMediaRelation) GetId() interface{} {
	return e.ID
}

func (e *ArchiveMediaRelation) AddEvent(event event.Event) {
	e.events = append(e.events, event)
}

func (e *ArchiveMediaRelation) Events() []event.Event {
	return e.events
}

func (e *ArchiveMediaRelation) ClearEvents() {
	e.events = []event.Event{}
}

// CreateArchiveMediaRelationAndSave 创建档案媒体关联并保存事件
func (e *ArchiveMediaRelation) CreateArchiveMediaRelationAndSave() error {
	// 业务规则验证：档案ID和媒体ID不能为空
	if e.ArchiveId <= 0 {
		return errors.New("档案ID不能为空")
	}
	if e.MediaId <= 0 {
		return errors.New("媒体ID不能为空")
	}

	// 注意：此时不创建事件，等到数据库保存后再创建事件（确保ID已设置）
	return nil
}

// CreateCreatedEventAfterSave 在数据库保存后创建创建事件
func (e *ArchiveMediaRelation) CreateCreatedEventAfterSave() {
	domainEvent := e.createArchiveMediaRelationCreatedEvent()
	e.AddEvent(domainEvent)
}

// DeleteArchiveMediaRelation 删除档案媒体关联
func (e *ArchiveMediaRelation) DeleteArchiveMediaRelation() error {
	domainEvent := e.createArchiveMediaRelationDeletedEvent()
	e.AddEvent(domainEvent)
	return nil
}

// createArchiveMediaRelationCreatedEvent 创建档案媒体关联创建事件
func (e *ArchiveMediaRelation) createArchiveMediaRelationCreatedEvent() *event.DomainEvent {
	payload := &event.ArchiveMediaRelationCreatedPayload{
		ID:        e.ID,
		ArchiveId: e.ArchiveId,
		MediaId:   e.MediaId,
		CreateBy:  e.CreateBy,
		UpdateBy:  e.UpdateBy,
		CreatedAt: e.CreatedAt,
		UpdatedAt: e.UpdatedAt,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("序列化 ArchiveMediaRelationCreatedPayload 失败", "error", err)
		return nil
	}

	return event.NewDomainEvent(event.EventTypeArchiveMediaRelationCreated, e.ID, "ArchiveMediaRelation", payloadJSON)
}

// createArchiveMediaRelationDeletedEvent 创建档案媒体关联删除事件
func (e *ArchiveMediaRelation) createArchiveMediaRelationDeletedEvent() *event.DomainEvent {
	payload := event.ArchiveMediaRelationDeletedPayload{
		ID:        e.ID,
		UpdateBy:  e.UpdateBy,
		DeletedAt: e.DeletedAt.Time,
	}

	payloadJSON, err := jsoniter.ConfigCompatibleWithStandardLibrary.Marshal(payload)
	if err != nil {
		logger.Error("无法将 ArchiveMediaRelationDeletedPayload 转换为 JSON", "error", err)
		return nil
	}

	return event.NewDomainEvent(event.EventTypeArchiveMediaRelationDeleted, e.ID, "ArchiveMediaRelation", payloadJSON)
}
