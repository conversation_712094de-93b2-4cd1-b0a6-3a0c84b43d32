package grpc_client

import (
	"context"
	"fmt"
	"sync"

	client "jxt-evidence-system/evidence-management/shared/common/grpc/client/port"
	lawcameraProto "jxt-evidence-system/evidence-management/shared/common/grpc/lawcamera/proto"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
)

// ============================================
// 执法记录仪信息服务客户端实现 - 专注于执法记录仪信息业务逻辑
// ============================================

// LawcameraInfoServiceClient 执法记录仪信息服务客户端实现
// 职责：实现执法记录仪信息服务接口，处理执法记录仪信息相关业务逻辑
type LawcameraInfoServiceClient struct {
	connectionManager *ConnectionManager
	lawcameraClient   lawcameraProto.LawcameraInfoServiceClient
	initOnce          func()
}

// NewLawcameraInfoServiceClient 创建执法记录仪信息服务客户端
func NewLawcameraInfoServiceClient(connManager *ConnectionManager) client.LawcameraInfoServiceClient {
	client := &LawcameraInfoServiceClient{
		connectionManager: connManager,
	}

	// 懒加载客户端初始化
	var once sync.Once
	client.initOnce = func() {
		once.Do(func() {
			conn := connManager.GetConnection()
			client.lawcameraClient = lawcameraProto.NewLawcameraInfoServiceClient(conn)
			logger.Info("执法记录仪服务客户端初始化完成")
		})
	}

	return client
}

// GetLawcameraById 根据执法记录仪ID查询信息
func (c *LawcameraInfoServiceClient) GetLawcameraById(ctx context.Context, tenantId string, id int32) (*lawcameraProto.LawcameraInfoReply, error) {
	c.initOnce()

	req := &lawcameraProto.GetLawcameraByIdReq{
		TenantId: tenantId,
		Id:       id,
	}

	logger.Debug(fmt.Sprintf("调用GetLawcameraById，tenantId: %s, id: %d", tenantId, id))

	var resp *lawcameraProto.LawcameraInfoReply
	err := c.connectionManager.ExecuteWithNetworkRetry(ctx, func() error {
		var err error
		resp, err = c.lawcameraClient.GetLawcameraById(ctx, req)
		return err
	})

	if err != nil {
		logger.Error(fmt.Sprintf("GetLawcameraById失败: %v", err))
		return nil, fmt.Errorf("查询执法记录仪信息失败: %w", err)
	}

	if resp == nil {
		logger.Warn(fmt.Sprintf("GetLawcameraById返回空结果，id: %d", id))
		return nil, fmt.Errorf("执法记录仪信息查询结果为空")
	}

	logger.Debug(fmt.Sprintf("GetLawcameraById成功，id: %d, 名称: %s",
		id, resp.Name))
	return resp, nil
}

// GetLawcameraByNo 根据执法记录仪编号查询信息
func (c *LawcameraInfoServiceClient) GetLawcameraByNo(ctx context.Context, tenantId string, no string) (*lawcameraProto.LawcameraInfoReply, error) {
	c.initOnce()

	req := &lawcameraProto.GetLawcameraByNoReq{
		TenantId: tenantId,
		No:       no,
	}

	logger.Debug(fmt.Sprintf("调用GetLawcameraByNo，tenantId: %s, no: %s", tenantId, no))

	var resp *lawcameraProto.LawcameraInfoReply
	err := c.connectionManager.ExecuteWithNetworkRetry(ctx, func() error {
		var err error
		resp, err = c.lawcameraClient.GetLawcameraByNo(ctx, req)
		return err
	})

	if err != nil {
		logger.Error(fmt.Sprintf("GetLawcameraByNo失败: %v", err))
		return nil, fmt.Errorf("查询执法记录仪信息失败: %w", err)
	}

	if resp == nil {
		logger.Warn(fmt.Sprintf("GetLawcameraByNo返回空结果，no: %s", no))
		return nil, fmt.Errorf("执法记录仪信息查询结果为空")
	}

	logger.Debug(fmt.Sprintf("GetLawcameraByNo成功，no: %s, id: %d, 名称: %s",
		no, resp.Id, resp.Name))
	return resp, nil
}

// GetLawcamerasByManagerId 根据管理员ID查询执法记录仪列表
func (c *LawcameraInfoServiceClient) GetLawcamerasByManagerId(ctx context.Context, tenantId string, managerId int32) (*lawcameraProto.LawcameraListReply, error) {
	c.initOnce()

	// 检查连接健康状态
	if !c.connectionManager.IsHealthy() {
		if c.connectionManager.IsReconnecting() {
			return nil, fmt.Errorf("gRPC连接正在重连中(第%d次尝试)，请稍后重试",
				c.connectionManager.GetReconnectAttempts())
		}
		return nil, fmt.Errorf("gRPC连接不健康，连接状态: %v",
			c.connectionManager.GetConnectionState())
	}

	req := &lawcameraProto.GetLawcamerasByManagerIdReq{
		TenantId:  tenantId,
		ManagerId: managerId,
	}

	logger.Debug(fmt.Sprintf("调用GetLawcamerasByManagerId，tenantId: %s, managerId: %d", tenantId, managerId))

	resp, err := c.lawcameraClient.GetLawcamerasByManagerId(ctx, req)
	if err != nil {
		logger.Error(fmt.Sprintf("GetLawcamerasByManagerId失败: %v", err))
		// 检查是否是连接相关错误，触发健康检查
		c.connectionManager.CheckHealth()
		return nil, fmt.Errorf("查询管理员执法记录仪列表失败: %w", err)
	}

	if resp == nil {
		logger.Warn(fmt.Sprintf("GetLawcamerasByManagerId返回空结果，managerId: %d", managerId))
		return nil, fmt.Errorf("管理员执法记录仪列表查询结果为空")
	}

	logger.Debug(fmt.Sprintf("GetLawcamerasByManagerId成功，managerId: %d, 数量: %d",
		managerId, resp.Total))
	return resp, nil
}

// GetLawcamerasByRequisitionerId 根据领用人ID查询执法记录仪列表
func (c *LawcameraInfoServiceClient) GetLawcamerasByRequisitionerId(ctx context.Context, tenantId string, requisitionerId int32) (*lawcameraProto.LawcameraListReply, error) {
	c.initOnce()

	// 检查连接健康状态
	if !c.connectionManager.IsHealthy() {
		if c.connectionManager.IsReconnecting() {
			return nil, fmt.Errorf("gRPC连接正在重连中(第%d次尝试)，请稍后重试",
				c.connectionManager.GetReconnectAttempts())
		}
		return nil, fmt.Errorf("gRPC连接不健康，连接状态: %v",
			c.connectionManager.GetConnectionState())
	}

	req := &lawcameraProto.GetLawcamerasByRequisitionerIdReq{
		TenantId:        tenantId,
		RequisitionerId: requisitionerId,
	}

	logger.Debug(fmt.Sprintf("调用GetLawcamerasByRequisitionerId，tenantId: %s, requisitionerId: %d", tenantId, requisitionerId))

	resp, err := c.lawcameraClient.GetLawcamerasByRequisitionerId(ctx, req)
	if err != nil {
		logger.Error(fmt.Sprintf("GetLawcamerasByRequisitionerId失败: %v", err))
		// 检查是否是连接相关错误，触发健康检查
		c.connectionManager.CheckHealth()
		return nil, fmt.Errorf("查询领用人执法记录仪列表失败: %w", err)
	}

	if resp == nil {
		logger.Warn(fmt.Sprintf("GetLawcamerasByRequisitionerId返回空结果，requisitionerId: %d", requisitionerId))
		return nil, fmt.Errorf("领用人执法记录仪列表查询结果为空")
	}

	logger.Debug(fmt.Sprintf("GetLawcamerasByRequisitionerId成功，requisitionerId: %d, 数量: %d",
		requisitionerId, resp.Total))
	return resp, nil
}
