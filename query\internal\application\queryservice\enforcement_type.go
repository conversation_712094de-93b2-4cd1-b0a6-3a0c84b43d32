package queryservice

import (
	"context"
	query "jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/application/queryservice/port"
	"jxt-evidence-system/evidence-management/query/internal/models"
	"jxt-evidence-system/evidence-management/query/internal/models/repository"
	"jxt-evidence-system/evidence-management/shared/common/di"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"github.com/ChenBigdata421/jxt-core/sdk/service"
)

func init() {
	registrations = append(registrations, registerEnforceTypeServiceDependencies)
}

// EnforcementTypeService 执法类型应用服务
type enforcementTypeQueryService struct {
	service.Service
	repo repository.EnforcementTypeReadModelRepository
}

func registerEnforceTypeServiceDependencies() {
	err := di.Provide(func(repo repository.EnforcementTypeReadModelRepository) port.EnforcementTypeService {
		return &enforcementTypeQueryService{
			repo: repo,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide EnforcementTypeService: %v", err)
	}
}

// GetByID 根据ID获取执法类型
func (s *enforcementTypeQueryService) EnforcementTypeGetByID(ctx context.Context, id int64) (*query.EnforcementTypeDTO, error) {
	model, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return s.modelToDTO(model), nil
}

// GetPage 获取执法类型列表
func (s *enforcementTypeQueryService) EnforcementTypeGetPage(ctx context.Context, r *query.EnforcementTypePagedQuery) ([]*query.EnforcementTypeDTO, int64, error) {
	// 获取模型数据
	modelList, count, err := s.repo.GetPage(ctx, r)
	if err != nil {
		return nil, 0, err
	}

	// 转换为DTO
	dtos := make([]*query.EnforcementTypeDTO, len(modelList))
	for i, model := range modelList {
		dtos[i] = s.modelToDTO(model)
	}
	return dtos, count, nil
}

// ListTree 获取执法类型树形结构
func (s *enforcementTypeQueryService) EnforcementTypeListTree(ctx context.Context) ([]*query.EnforcementTypeDTO, error) {
	modelList, _, err := s.repo.GetAll(ctx)
	if err != nil {
		return nil, err
	}

	tree := s.buildTree(modelList, 0)
	return tree, nil
}

// 根据 ParentId 构建树形结构
func (s *enforcementTypeQueryService) buildTree(models []*models.EnforcementType, parentID int64) []*query.EnforcementTypeDTO {
	var tree []*query.EnforcementTypeDTO
	for _, model := range models {
		if model.ParentId == parentID {
			dto := s.modelToDTO(model)
			// 递归构建子节点
			children := s.buildTree(models, model.ID)
			dto.Children = children
			tree = append(tree, dto)
		}
	}
	return tree
}

// modelToDTO 将读模型转换为DTO
func (s *enforcementTypeQueryService) modelToDTO(model *models.EnforcementType) *query.EnforcementTypeDTO {
	if model == nil {
		return nil
	}
	return &query.EnforcementTypeDTO{
		ID:                  model.ID,
		EnforcementTypeCode: model.EnforcementTypeCode,
		EnforcementTypeName: model.EnforcementTypeName,
		EnforcementTypeDesc: model.EnforcementTypeDesc,
		EnforcementTypePath: model.EnforcementTypePath,
		ParentId:            model.ParentId,
		Source:              model.Source,
		Sort:                model.Sort,
		CreateBy:            model.CreateBy,
		UpdateBy:            model.UpdateBy,
		CreatedAt:           model.CreatedAt,
		UpdatedAt:           model.UpdatedAt,
	}
}
