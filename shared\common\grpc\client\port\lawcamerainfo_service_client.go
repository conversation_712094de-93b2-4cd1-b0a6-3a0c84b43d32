package client

import (
	"context"
	proto "jxt-evidence-system/evidence-management/shared/common/grpc/lawcamera/proto"
)

// LawcameraInfoServiceClient 执法记录仪信息服务客户端接口
type LawcameraInfoServiceClient interface {
	// GetLawcameraById 根据执法记录仪ID查询信息
	GetLawcameraById(ctx context.Context, tenantId string, id int32) (*proto.LawcameraInfoReply, error)

	// GetLawcameraByNo 根据执法记录仪编号查询信息
	GetLawcameraByNo(ctx context.Context, tenantId string, no string) (*proto.LawcameraInfoReply, error)

	// GetLawcamerasByManagerId 根据管理员ID查询执法记录仪列表
	GetLawcamerasByManagerId(ctx context.Context, tenantId string, managerId int32) (*proto.LawcameraListReply, error)

	// GetLawcamerasByRequisitionerId 根据领用人ID查询执法记录仪列表
	GetLawcamerasByRequisitionerId(ctx context.Context, tenantId string, requisitionerId int32) (*proto.LawcameraListReply, error)
} 