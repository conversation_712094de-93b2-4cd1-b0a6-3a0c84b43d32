package router

import (
	"jxt-evidence-system/evidence-management/command/internal/interface/rest/api"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/middleware"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	jwt "github.com/ChenBigdata421/jxt-core/sdk/pkg/jwtauth"
	"github.com/gin-gonic/gin"
)

func init() {
	routerCheckRole = append(routerCheckRole, registerArchiveRouter)
}

func registerArchiveRouter(v1 *gin.RouterGroup, authMiddleware *jwt.GinJWTMiddleware) {
	// 通过依赖注入创建的api处理器，代替手工创建
	// 解析 ArchiveHandler

	err := di.Invoke(func(handler *api.ArchiveHandler) {
		if handler != nil {
			r := v1.Group("/archives").Use(authMiddleware.MiddlewareFunc()).Use(middleware.AuthCheckRole())
			{
				// 基础CRUD操作
				r.POST("", handler.CreateArchive)           // POST /archive - 创建档案
				r.PUT("/:id", handler.UpdateArchiveByID)    // PUT /archive/{id} - 更新档案
				r.DELETE("/:id", handler.DeleteArchiveByID) // DELETE /archive/{id} - 删除档案

				// 批量操作
				r.POST("/batch/update", handler.BatchUpdateArchive)                                 // POST /archive/batch/update - 批量更新档案
				r.POST("/batch/delete", handler.BatchDeleteArchive)                                 // POST /archive/batch/delete - 批量删除档案
				r.POST("/batch/update-status", handler.BatchUpdateArchiveStatus)                    // POST /archive/batch/update-status - 批量更新状态
				r.POST("/batch/update-expiration", handler.BatchUpdateArchiveExpiration)            // POST /archive/batch/update-expiration - 批量更新过期时间
				r.POST("/batch/update-storage-duration", handler.BatchUpdateArchiveStorageDuration) // POST /archive/batch/update-storage-duration - 批量更新保存期限
			}
		} else {
			logger.Fatal("ArchiveHandler is nil after resolution")
		}
	})

	if err != nil {
		logger.Fatalf("Failed to resolve ArchiveHandler: %v", err)
	}
}
