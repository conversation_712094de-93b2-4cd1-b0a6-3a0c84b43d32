package event

import (
	"time"
)

// media事件类型定义
const (
	// 单资源事件
	EventTypeMediaUploaded                        = "MediaUploaded"
	EventTypeMediaUpdated                         = "MediaUpdated"
	EventTypeMediaDeleted                         = "MediaDeleted"
	EventTypeMediaIsNonLawEnforcementMediaUpdated = "MediaIsNonLawEnforcementMediaUpdated"

	// 批量事件
	EventTypeMediaBatchUpdated                         = "MediaBatchUpdated"
	EventTypeMediaBatchDeleted                         = "MediaBatchDeleted"
	EventTypeMediaBatchIsNonLawEnforcementMediaUpdated = "MediaBatchIsNonLawEnforcementMediaUpdated"
	EventTypeMediaBatchEnforceTypeUpdated              = "MediaBatchEnforceTypeUpdated"
	EventTypeMediaBatchIsLockedUpdated                 = "MediaBatchIsLockedUpdated"
)

// 具体的领域事件结构体
type MediaUploadedPayload struct {

	// 基础信息
	MediaID   int64  `json:"mediaId"`
	MediaName string `json:"mediaName"`

	// 业务元数据
	MediaCate         int       `json:"mediaCate"`
	MediaSuffix       string    `json:"mediaSuffix"`
	ShotTimeStart     time.Time `json:"shotTimeStart"`
	ShotTime          time.Time `json:"shotTime"`
	VideoClarity      int       `json:"videoClarity"`
	VideoDuration     int       `json:"videoDuration"`
	Width             int       `json:"width"`
	Height            int       `json:"height"`
	ImportantLevel    int       `json:"importantLevel"`
	ImportantLevelRec int       `json:"importantLevelRec"`

	// 文件元数据
	FileIdentity string `json:"fileIdentity"`
	FileName     string `json:"fileName"`
	FileSize     int64  `json:"fileSize"`
	FileMd5      string `json:"fileMd5"`
	FileType     int    `json:"fileType"`
	ContentType  string `json:"contentType"`

	// 业务标注数据
	IsNonEnforcementMedia int    `json:"isNonEnforcementMedia"`
	Sequence              string `json:"sequence"`
	Comments              string `json:"comments"`

	// 状态元数据
	IsLocked   int        `json:"isLocked"`
	ExpiryTime *time.Time `json:"expiryTime"`

	// 归档元数据
	ArchiveID   int64      `json:"archiveId"`
	IsArchived  int        `json:"isArchived"`
	ArchiveDate *time.Time `json:"archiveDate"`

	// 存储相关
	URI             string `json:"uri"`
	Thumbnail       string `json:"thumbnail"`
	SiteID          int    `json:"siteId"`
	StorageID       int    `json:"storageId"`
	StorageType     int    `json:"storageType"`
	IsSendToStorage int    `json:"isSendToStorage"`
	IsNoticeSend    int    `json:"isNoticeSend"`

	// 时间相关

	// 关联元数据
	PoliceID      int        `json:"policeId,omitempty"`
	OrgID         int        `json:"orgId,omitempty"`
	RecorderID    int        `json:"recorderId,omitempty"`
	SiteClientID  int        `json:"siteClientId"`
	TerminalType  int        `json:"terminalType"`
	TrialID       int        `json:"trialId"`
	EnforceType   int        `json:"enforceType"`
	IncidentCode  string     `json:"incidentCode"`
	IsAssociated  int        `json:"isAssociated"`
	AssociateTime *time.Time `json:"associateTime"`

	// 审计字段
	ImportTime      *time.Time `json:"importTime"`
	AcquisitionTime *time.Time `json:"acquisitionTime"`
	CreateBy        int        `json:"createBy"`
	UpdateBy        int        `json:"updateBy"`
	CreatedAt       time.Time  `json:"createdAt"`
	UpdatedAt       time.Time  `json:"updatedAt"`
}

type MediaUpdatedPayload struct {
	MediaID       int64                  `json:"mediaId"`
	UpdatedFields map[string]interface{} `json:"updatedFields"`
}

type MediaDeletedPayload struct {
	MediaID   int64     `json:"mediaId"`
	UpdateBy  int       `json:"updateBy"`
	DeletedAt time.Time `json:"deletedAt"`
}

func (s *MediaDeletedPayload) GetId() interface{} {
	return s.MediaID
}

type MediaBatchUpdatedPayload struct {
	MediaIDs      []int64                `json:"mediaIds"`
	UpdatedFields map[string]interface{} `json:"updatedFields"`
}

func (s *MediaBatchUpdatedPayload) GetIds() interface{} {
	return s.MediaIDs
}

type MediaIsNonLawEnforcementMediaUpdatedPayload struct {
	MediaID      int64     `json:"mediaId"`
	IsEnforMedia int       `json:"isEnforMedia"`
	UpdateBy     int       `json:"updateBy"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

func (s *MediaIsNonLawEnforcementMediaUpdatedPayload) GetId() interface{} {
	return s.MediaID
}

type MediaBatchIsNonLawEnforcementMediaUpdatedPayload struct {
	MediaIDs     []int64   `json:"mediaIds"`
	IsEnforMedia int       `json:"isEnforMedia"`
	UpdateBy     int       `json:"updateBy"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

func (s *MediaBatchIsNonLawEnforcementMediaUpdatedPayload) GetIds() interface{} {
	return s.MediaIDs
}

type MediaBatchDeletedPayload struct {
	MediaIDs  []int64   `json:"mediaIds"`
	UpdateBy  int       `json:"updateBy"`
	DeletedAt time.Time `json:"deletedAt"`
}

func (s *MediaBatchDeletedPayload) GetIds() interface{} {
	return s.MediaIDs
}

type MediaBatchEnforceTypeUpdatedPayload struct {
	MediaIDs    []int64   `json:"mediaIds"`
	EnforceType int       `json:"enforceType"`
	UpdateBy    int       `json:"updateBy"`
	UpdatedAt   time.Time `json:"updatedAt"`
}

func (s *MediaBatchEnforceTypeUpdatedPayload) GetIds() interface{} {
	return s.MediaIDs
}

type MediaBatchIsLockedUpdatedPayload struct {
	MediaIDs  []int64   `json:"mediaIds"`
	IsLocked  int       `json:"isLocked"`
	UpdateBy  int       `json:"updateBy"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func (s *MediaBatchIsLockedUpdatedPayload) GetIds() interface{} {
	return s.MediaIDs
}
