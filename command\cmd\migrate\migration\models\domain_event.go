package models

import "time"

type DomainEvent struct {
	EventID       string    `json:"eventId" gorm:"type:char(36);primary_key;column:event_id;comment:事件ID"`
	EventType     string    `json:"eventType" gorm:"type:varchar(255);index;column:event_type;comment:事件类型"`
	OccurredAt    time.Time `json:"occurredAt" gorm:"type:datetime;index;column:occurred_at;comment:事件发生时间"`
	Version       int       `json:"version" gorm:"type:int;column:event_version;comment:事件版本"`
	AggregateID   int64     `json:"aggregateId" gorm:"type:bigint;index;column:aggregate_id;comment:聚合根ID"`
	AggregateType string    `json:"aggregateType" gorm:"type:varchar(255);index;column:aggregate_type;comment:聚合根类型"`
	Payload       []byte    `json:"payload" gorm:"type:json;column:event_payload;comment:事件载荷"`
}

func (DomainEvent) TableName() string {
	return "t_evidence_domain_events"
}
