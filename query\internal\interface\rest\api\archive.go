package api

import (
	"context"
	"strconv"
	"time"

	_ "github.com/ChenBigdata421/jxt-core/sdk/pkg/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"jxt-evidence-system/evidence-management/shared/common/restapi"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"

	"jxt-evidence-system/evidence-management/query/internal/application/query"
	"jxt-evidence-system/evidence-management/query/internal/application/queryservice/port"
	"jxt-evidence-system/evidence-management/shared/common/di"
)

func init() {
	registrations = append(registrations, registerArchiveApiDependencies)
}

// ArchiveHandler的依赖注入
func registerArchiveApiDependencies() {
	// 注册 ArchiveHandler
	err := di.Provide(func(query port.ArchiveQuery) *ArchiveHandler {
		return &ArchiveHandler{
			queryService: query,
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide ArchiveHandler: %v", err)
	}
}

type ArchiveHandler struct {
	restapi.RestApi
	queryService port.ArchiveQuery
}

// GetPage 通过查询条件获取档案列表数据分页
// @Summary 档案列表数据
// @Description 获取JSON
// @Tags Archive
// @Param archiveCode query string false "档案编码"
// @Param archiveTitle query string false "档案标题"
// @Param archiveType query int false "档案类型"
// @Param orgId query int false "组织ID"
// @Param orgCode query string false "组织编码"
// @Param orgName query string false "组织名称"
// @Param status query int false "状态"
// @Param inputTimeStart query string false "录入开始时间"
// @Param inputTimeEnd query string false "录入结束时间"
// @Param expirationStart query string false "过期开始时间"
// @Param expirationEnd query string false "过期结束时间"
// @Param storageDuration query int false "保存期限"
// @Param inputUserName query string false "录入人员"
// @Param pageSize query int false "页条数"
// @Param pageIndex query int false "页码"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/archive [get]
// @Security Bearer
func (h *ArchiveHandler) GetPage(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
	defer cancel()

	query := query.ArchivePagedQuery{}

	err := c.ShouldBindQuery(&query)
	if err != nil {
		h.GetLogger(c).Error(err.Error())
		h.Error(c, 500, err, err.Error())
		return
	}
	h.GetLogger(c).Sugar().Infof("ArchivePagedQuery: %+v", query)

	list, count, err := h.queryService.GetPage(ctx, &query)
	if err != nil {
		h.GetLogger(c).Error("查询档案分页列表失败", zap.Error(err))
		h.Error(c, 500, err, "查询失败")
		return
	}
	h.PageOK(c, list, int(count), query.GetPageIndex(), query.GetPageSize(), "查询成功")
}

// GetByID 通过档案ID获取档案
// @Summary 通过档案ID获取档案
// @Description 获取JSON
// @Tags Archive
// @Param id path int true "档案ID"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/archive/{id} [get]
// @Security Bearer
func (h *ArchiveHandler) GetByID(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
	defer cancel()

	// 获取有符号64位整数id
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)

	if err != nil {
		h.GetLogger(c).Error("解析档案ID失败", zap.Error(err))
		h.Error(c, 400, err, "档案ID格式错误")
		return
	}

	model, err := h.queryService.GetByID(ctx, id)
	if err != nil {
		h.GetLogger(c).Error("根据ID查询档案失败", zap.Error(err), zap.Int64("archiveId", id))
		h.Error(c, 500, err, "查询失败")
		return
	}
	h.OK(c, model, "查询成功")
}

// GetByCode 通过档案编码获取档案
// @Summary 通过档案编码获取档案
// @Description 获取JSON
// @Tags Archive
// @Param code path string true "档案编码"
// @Success 200 {object} response.Response "{"code": 200, "data": [...]}"
// @Router /api/v1/archive/code/{code} [get]
// @Security Bearer
func (h *ArchiveHandler) GetByCode(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
	defer cancel()

	code := c.Param("code")
	if code == "" {
		h.GetLogger(c).Error("档案编码不能为空")
		h.Error(c, 400, nil, "档案编码不能为空")
		return
	}

	model, err := h.queryService.GetByCode(ctx, code)
	if err != nil {
		h.GetLogger(c).Error("根据编码查询档案失败", zap.Error(err), zap.String("archiveCode", code))
		h.Error(c, 500, err, "查询失败")
		return
	}
	h.OK(c, model, "查询成功")
}
