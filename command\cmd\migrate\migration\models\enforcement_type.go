package models

import "jxt-evidence-system/evidence-management/shared/common/models"

type EnforcementType struct {
	ID                  int64  `json:"id" gorm:"primaryKey;column:enforcement_type_id;autoIncrement;comment:主键ID"` //主键ID
	EnforcementTypeCode string `json:"enforcementTypeCode" gorm:"column:enforcement_type_code;comment:执行类型编码"`     //执行类型编码
	EnforcementTypeName string `json:"enforcementTypeName" gorm:"column:enforcement_type_name;comment:执行类型名称"`     //执行类型名称
	EnforcementTypeDesc string `json:"enforcementTypeDesc" gorm:"column:enforcement_type_desc;comment:执行类型描述"`     //执行类型描述
	EnforcementTypePath string `json:"enforcementTypePath" gorm:"column:enforcement_type_path;comment:执法类型路径"`     //
	ParentId            int64  `json:"parentId" gorm:"column:parent_id;comment:父级Id"`                              //上级部门
	Source              string `json:"source" gorm:"column:source;comment:执法类型来源"`
	Sort                int    `json:"sort" gorm:"size:4;comment:排序"` //排序
	models.ControlBy
	models.ModelTime
}

func (*EnforcementType) TableName() string {
	return "t_evidence_enforcement_types"
}
