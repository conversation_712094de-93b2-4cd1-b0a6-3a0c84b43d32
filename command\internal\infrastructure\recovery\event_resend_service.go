package recovery

import (
	"context"
	"fmt"
	"jxt-evidence-system/evidence-management/command/internal/domain/event/publisher"
	"jxt-evidence-system/evidence-management/shared/common/di"
	"jxt-evidence-system/evidence-management/shared/common/eventbus"
	"jxt-evidence-system/evidence-management/shared/common/global"
	"jxt-evidence-system/evidence-management/shared/common/service"
	"jxt-evidence-system/evidence-management/shared/domain/event"
	event_repository "jxt-evidence-system/evidence-management/shared/domain/event/repository"
	"sync"
	"sync/atomic"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk"
	"github.com/ChenBigdata421/jxt-core/sdk/pkg/logger"
	"gorm.io/gorm"

	"go.uber.org/zap"
)

func init() {
	registrations = append(registrations, registerEventResendServiceDependencies)
}

func registerEventResendServiceDependencies() {
	err := di.Provide(func(eventRepo event_repository.DomainEventRepository, eventPublisher publisher.EventPublisher) EventResendService {
		return &eventResendService{
			eventRepo:      eventRepo,
			eventPublisher: eventPublisher,
			stopChan:       make(chan struct{}),
			wg:             &sync.WaitGroup{},
		}
	})
	if err != nil {
		logger.Fatalf("Failed to provide EventResendService: %v", err)
	}
}

type EventResendService interface {
	ResendEvents(ctx context.Context) error
	RegisterReconnectCallback()
}

type eventResendService struct {
	service.Service
	eventRepo      event_repository.DomainEventRepository
	eventPublisher publisher.EventPublisher
	isRepublishing atomic.Bool
	stopChan       chan struct{}   // 用于通知停止的通道
	wg             *sync.WaitGroup // 用于等待所有goroutine完成
}

// ResendEvents 重新发布事件，将未发布成功的事件重新发布，直到所有的事件都发布完毕或连续失败10次
func (e *eventResendService) ResendEvents(ctx context.Context) error {
	if e.isRepublishing.Load() {
		e.GetLogger(ctx).Info("事件重发服务已经在运行中，跳过本次重发")
		return nil
	}

	consecutiveFailures := 0
	maxConsecutiveFailures := 10

	for {
		select {
		case <-e.stopChan:
			e.GetLogger(ctx).Info("收到停止信号，终止事件重发")
			return nil
		default:

			var errors []error
			var errMu sync.Mutex
			var wg sync.WaitGroup
			// 遍历所有租户的数据库，检查数据库中有没有未发送成功的领域事件
			sdk.Runtime.GetTenantCommandDBs(func(tenantID string, db *gorm.DB) bool {
				wg.Add(1)
				go func(name string, database *gorm.DB) {
					defer wg.Done()
					// 记录tenantID到context中供后续使用
					ctxEvent := context.WithValue(ctx, global.TenantIDKey, tenantID)
					if err := e.processEvents(ctxEvent, &consecutiveFailures, maxConsecutiveFailures); err != nil {
						errMu.Lock()
						errors = append(errors, fmt.Errorf("error resending events %s: %w", name, err))
						errMu.Unlock()
					}
					time.Sleep(5 * time.Second) // 添加延迟以避免过于频繁的检查
				}(tenantID, db)
				return true
			})

			wg.Wait()

			if len(errors) > 0 {
				return fmt.Errorf("errors occurred while resending events: %v", errors)
			}

			return nil

		}
	}
}

// processEvents 处理事件重发的主要逻辑
func (e *eventResendService) processEvents(ctx context.Context, consecutiveFailures *int, maxConsecutiveFailures int) error {
	events, err := e.eventRepo.FindEarliestEvents(ctx)
	if err != nil {
		return fmt.Errorf("查找最早的事件失败: %w", err)
	}

	if len(events) == 0 {
		// 所有事件都已处理完毕，退出事件重新发布阶段
		e.isRepublishing.Store(false)
		return nil
	}

	// 进入事件重新发布阶段
	e.isRepublishing.Store(true)
	e.GetLogger(ctx).Info("执行事件重发服务")

	// 按聚合类型分组事件
	eventsByType := make(map[string][]event.DomainEvent)
	for _, evt := range events {
		eventsByType[evt.AggregateType] = append(eventsByType[evt.AggregateType], evt)
	}

	errChan := make(chan error, len(eventsByType))

	// 为每种聚合类型启动一个goroutine处理事件
	for aggregateType, typeEvents := range eventsByType {
		e.wg.Add(1)
		go func(aggrType string, evts []event.DomainEvent) {
			defer e.wg.Done()
			for _, domainEvent := range evts {
				select {
				case <-e.stopChan:
					errChan <- nil
					return
				default:
					if err := e.publishEvent(ctx, &domainEvent); err != nil {
						e.GetLogger(ctx).Error("重新发布事件失败",
							zap.Error(err),
							zap.String("EventID", domainEvent.EventID),
							zap.String("EventType", domainEvent.EventType),
							zap.Any("AggregateID", domainEvent.AggregateID))
						errChan <- err
						return
					}

					// 发送成功后，从数据库中删除该条domainEvent记录
					if err := e.eventRepo.Delete(ctx, domainEvent.EventID); err != nil {
						e.GetLogger(ctx).Error("删除已重新发布的事件失败",
							zap.Error(err),
							zap.String("EventID", domainEvent.EventID))
						errChan <- err
						return
					}
				}
			}
			errChan <- nil
		}(aggregateType, typeEvents)
	}

	// 等待所有goroutine完成
	e.wg.Wait()
	close(errChan)

	hasError := false
	// 检查是否有错误发生
	for err := range errChan {
		if err != nil {
			hasError = true
			*consecutiveFailures++
			if *consecutiveFailures >= maxConsecutiveFailures {
				e.isRepublishing.Store(false)
				return fmt.Errorf("连续%d次发布失败，终止重发流程", maxConsecutiveFailures)
			}
			break
		}
	}

	if !hasError {
		*consecutiveFailures = 0
	}
	return nil
}

func (e *eventResendService) publishEvent(ctx context.Context, domainEvent *event.DomainEvent) error {
	var topic string
	switch domainEvent.AggregateType {
	case "Media":
		topic = eventbus.MediaEventTopic
	case "Archive":
		topic = eventbus.ArchiveEventTopic
	case "ArchiveMediaRelation":
		topic = eventbus.ArchiveMediaRelationEventTopic
	case "LawEnforcement":
		topic = eventbus.EnforcementTypeEventTopic
	case "Record":
		topic = eventbus.RecordEventTopic
	default:
		return fmt.Errorf("未知的事件聚合类型: %s", domainEvent.AggregateType)
	}

	if err := e.eventPublisher.Publish(ctx, topic, domainEvent); err != nil {
		return fmt.Errorf("发布事件失败: %w", err)
	}

	return nil
}

// 注册在重连成功后的回调函数ResendEvents
func (e *eventResendService) RegisterReconnectCallback() {
	e.eventPublisher.RegisterReconnectCallback(e.ResendEvents)
}

func (e *eventResendService) Shutdown(ctx context.Context) error {
	// 创建一个带有超时的上下文
	shutdownCtx, shutdownCancel := context.WithTimeout(ctx, 10*time.Second)
	defer shutdownCancel()

	// 发送停止信号
	close(e.stopChan)

	// 等待所有goroutine完成或超时
	done := make(chan struct{})
	go func() {
		e.wg.Wait()
		close(done)
	}()

	select {
	case <-shutdownCtx.Done():
		e.GetLogger(ctx).Warn("事件重发服务关闭超时")
		return fmt.Errorf("shutdown timeout")
	case <-done:
		e.GetLogger(ctx).Info("事件重发服务优雅关闭完成")
		return nil
	}
}
