package eventbus

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"github.com/ChenBigdata421/jxt-core/sdk/pkg"
	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill-kafka/v2/pkg/kafka"
	"github.com/ThreeDotsLabs/watermill/message"
	lru "github.com/hashicorp/golang-lru/v2"
	"golang.org/x/time/rate"
)

var DefaultKafkaSubscriberManager *KafkaSubscriberManager

// 全局变量，用于追踪处理器数量
var totalProcessors int64
var activeProcessors int64

type KafkaSubscriberManagerConfig struct {
	// Kafka 配置
	KafkaConfig kafka.SubscriberConfig
	// 健康检查相关
	HealthCheckConfig    HealthCheckConfig
	MaxReconnectAttempts int
	// 聚合处理器相关
	IdleTimeout                     time.Duration
	ProcessingRateLimit             rate.Limit
	ProcessingRateBurst             int
	MaxGetOrCreateProcessorAttempts int
	AggregateProcessorCacheSize     int
	// 无积压检测相关
	MaxLagThreshold        int64
	MaxTimeThreshold       time.Duration
	NoBacklogCheckInterval time.Duration
	Threshold              float64
}

func DefaultKafkaSubscriberManagerConfig() KafkaSubscriberManagerConfig {
	return KafkaSubscriberManagerConfig{
		// 健康检查相关
		HealthCheckConfig:    DefaultHealthCheckConfig(),
		MaxReconnectAttempts: 5,
		// 聚合处理器相关
		IdleTimeout:                     5 * time.Minute,
		ProcessingRateLimit:             1000,
		ProcessingRateBurst:             1000,
		MaxGetOrCreateProcessorAttempts: 3,
		AggregateProcessorCacheSize:     1000,
		// 无积压检测
		MaxLagThreshold:        10,
		MaxTimeThreshold:       30 * time.Second,
		NoBacklogCheckInterval: 3 * time.Minute, // 每3分钟检查一次是否无积压，可以根据需求调整
		Threshold:              0.1,             // 10%，这个值可以根据实际情况调整
	}
}

type KafkaSubscriberManager struct {
	Config     KafkaSubscriberManagerConfig
	Subscriber atomic.Value // 存储 *kafka.Subscriber
	Logger     watermill.LoggerAdapter
	wg         sync.WaitGroup
	rootCtx    context.Context
	rootCancel context.CancelFunc
	// 用于reconnect时，重新恢复订阅的topic
	subscribedTopics sync.Map
	defaultTimeout   atomic.Int64
	// 在刚启动处理积压消息阶段，保证消息处理顺序新增
	isRecoveryMode      atomic.Bool
	aggregateProcessors *lru.Cache[string, *aggregateProcessor]
	processingRate      *rate.Limiter
	// 无积压检测添加
	NoBacklogDetector atomic.Value // 存储 *NoBacklogDetector
	noBacklogCount    int
	noBacklogCtx      context.Context    // 新增字段用于控制noBacklog检查循环
	noBacklogCancel   context.CancelFunc // 新增字段用于取消noBacklog检查循环
	// 健康检查
	healthChecker atomic.Value // 存储 *HealthChecker
}

type aggregateProcessor struct {
	messages     chan *message.Message
	lastActivity atomic.Value // stores time.Time
	aggregateID  string
	done         chan struct{}
}

func NewKafkaSubscriberManager(config KafkaSubscriberManagerConfig) (*KafkaSubscriberManager, error) {
	if len(config.KafkaConfig.Brokers) == 0 {
		return nil, fmt.Errorf("no Kafka brokers specified in config")
	}
	if config.HealthCheckConfig.Interval <= 0 {
		return nil, fmt.Errorf("invalid health check interval")
	}

	cache, err := lru.New[string, *aggregateProcessor](config.AggregateProcessorCacheSize)
	if err != nil {
		return nil, fmt.Errorf("failed to create LRU cache: %w", err)
	}

	detector, err := NewNoBacklogDetector(config.KafkaConfig.Brokers, config.KafkaConfig.ConsumerGroup, config.MaxLagThreshold, config.MaxTimeThreshold)
	if err != nil {
		return nil, fmt.Errorf("error creating detector: %v", err)
	}

	km := &KafkaSubscriberManager{
		Config: config,
		Logger: watermill.NewStdLogger(false, false),
		// 保证消息处理顺序新增
		isRecoveryMode:      atomic.Bool{},
		aggregateProcessors: cache,
		processingRate:      rate.NewLimiter(config.ProcessingRateLimit, config.ProcessingRateBurst),
		// 无积压检测添加
		noBacklogCount: 0,
	}
	km.isRecoveryMode.Store(true) // 开始时处于恢复模式
	km.NoBacklogDetector.Store(detector)

	// 初始化健康检查器
	healthChecker := NewHealthChecker(config.HealthCheckConfig, km)
	km.healthChecker.Store(healthChecker)

	return km, nil
}

func (km *KafkaSubscriberManager) SetRecoveryMode(isRecovery bool) {
	km.isRecoveryMode.Store(isRecovery)
	if isRecovery {
		log.Println(pkg.Yellow("Entering recovery mode: messages will be processed in order by aggregate ID"))
	} else {
		log.Println(pkg.Green("Exiting recovery mode: gradually transitioning to normal processing"))
	}
}

func (km *KafkaSubscriberManager) IsInRecoveryMode() bool {
	return km.isRecoveryMode.Load()
}

func (km *KafkaSubscriberManager) Start() error {
	km.rootCtx, km.rootCancel = context.WithCancel(context.Background())

	if err := km.initSubscriber(km.rootCtx); err != nil {
		return err
	}

	// 启动健康检查
	if healthChecker, ok := km.healthChecker.Load().(*HealthChecker); ok {
		if err := healthChecker.Start(); err != nil {
			return fmt.Errorf("failed to start health checker: %w", err)
		}
	}

	// 初始化noBacklog检查上下文
	km.noBacklogCtx, km.noBacklogCancel = context.WithCancel(km.rootCtx)
	km.wg.Add(2)
	go km.startNoBacklogCheckLoop(km.noBacklogCtx) // 检查是否已经无消息积压
	go km.healthCheckLoop(km.rootCtx)              // 健康检查，全生命周期存在，确保一直检查和尝试恢复

	return nil
}

func (km *KafkaSubscriberManager) SubscribeToHealthCheckTopic(topic string) (<-chan *message.Message, error) {
	subscriberAny := km.Subscriber.Load()
	if subscriberAny == nil {
		return nil, fmt.Errorf("subscriber is nil")
	}

	subscriber, ok := subscriberAny.(*kafka.Subscriber)
	if !ok {
		return nil, fmt.Errorf("invalid subscriber type")
	}
	return subscriber.Subscribe(km.rootCtx, topic)

}

func (km *KafkaSubscriberManager) initSubscriber(ctx context.Context) error {
	// 关闭旧的 Subscriber
	if oldSubscriber := km.Subscriber.Load(); oldSubscriber != nil {
		ctx, cancel := context.WithTimeout(ctx, 2*time.Minute)
		defer cancel()

		done := make(chan struct{})
		go func() {
			oldSubscriber.(*kafka.Subscriber).Close()
			close(done)
		}()

		select {
		case <-done:
			log.Println(pkg.Green("Subscriber closed successfully"))
		case <-ctx.Done():
			log.Println(pkg.Yellow("Close operation timed out, may need manual intervention"))
			// 可能需要强制关闭或其他清理操作
		}
	}

	// 创建新的 Subscriber
	subscriber, err := kafka.NewSubscriber(km.Config.KafkaConfig, km.Logger)
	if err != nil {
		return fmt.Errorf("failed to create new subscriber: %w", err)
	}
	km.Subscriber.Store(subscriber) // 使用 atomic.Value 存储新的 Subscriber
	return nil

}

// 使用指数退避算法重新建立连接，重连的责任还是KafkaSubscriberManager
func (km *KafkaSubscriberManager) reconnect() error {

	log.Print(pkg.Yellow("Kafka Subscriber Health check failed. Attempting to reconnect...\n"))

	if err := km.initSubscriber(km.rootCtx); err != nil {
		return fmt.Errorf("failed to reconnect %w", err)
	}

	// 重新启动健康检查
	if healthChecker, ok := km.healthChecker.Load().(*HealthChecker); ok {
		healthChecker.Stop()
		if err := healthChecker.Start(); err != nil {
			return fmt.Errorf("failed to restart health checker %w", err)
		}
	}

	// 重新订阅所有之前订阅的主题
	if err := km.resubscribeAllTopics(); err != nil {
		return fmt.Errorf("failed to resubscribe to all topics %w", err)
	}

	log.Println(pkg.Green("Reconnection successful"))
	km.isRecoveryMode.Store(true) // 重连成功后，切换到恢复模式

	// 停止之前的kafka消息积压检查循环
	if km.noBacklogCancel != nil {
		km.noBacklogCancel()
	}

	// 创建新的kafka消息积压检查循环
	km.noBacklogCtx, km.noBacklogCancel = context.WithCancel(km.rootCtx)
	km.wg.Add(1)
	go km.startNoBacklogCheckLoop(km.noBacklogCtx) // 检查是否已经无消息积压

	return nil

}

func (km *KafkaSubscriberManager) healthCheckLoop(ctx context.Context) {
	defer km.wg.Done()
	ticker := time.NewTicker(km.Config.HealthCheckConfig.Interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			log.Println(pkg.Yellow("Context cancelled, stopping health check"))
			return
		case <-ticker.C:
			// 健康检查
			if healthChecker, ok := km.healthChecker.Load().(*HealthChecker); ok {
				status, err := healthChecker.healthCheck()
				if err != nil {
					log.Printf(pkg.Red("Kafka Subscriber Health check failed: %v\n"), err)
					// 在这里可以添加重连逻辑
					if reconnectErr := km.reconnect(); reconnectErr != nil {
						log.Printf(pkg.Red("Kafka subscriber reconnection failed: %v\n"), reconnectErr)
					} else {
						log.Println(pkg.Green("Kafka subscriber reconnection successful"))
					}
				} else {
					log.Println(pkg.Green(status))
				}
			} else {
				log.Print("failed to load health checker")
			}

		}
	}
}

func (km *KafkaSubscriberManager) Shutdown(ctx context.Context) error {
	log.Println(pkg.Green("Shutting down Kafka subscriber manager..."))

	if healthChecker, ok := km.healthChecker.Load().(*HealthChecker); ok {
		healthChecker.Stop() //停止健康检查器
	}

	km.rootCancel() // 取消根上下文

	// 停止所有聚合处理器
	km.stopAllProcessors()

	// 使用 context 的超时机制
	shutdownComplete := make(chan struct{})
	go func() {
		km.wg.Wait()
		close(shutdownComplete)
	}()

	select {
	case <-shutdownComplete:
		log.Println(pkg.Green("All goroutines stopped"))
	case <-ctx.Done():
		log.Println(pkg.Yellow("Shutdown timed out, some goroutines may still be running"))
		return ctx.Err()
	}

	// 关闭其他资源
	if err := km.closeResources(); err != nil {
		return fmt.Errorf("error closing resources: %w", err)
	}

	return nil
}

func (km *KafkaSubscriberManager) closeResources() error {
	var errs []error

	// 关闭 Subscriber
	if subscriberAny := km.Subscriber.Load(); subscriberAny != nil {
		if subscriber, ok := subscriberAny.(*kafka.Subscriber); ok {
			if err := subscriber.Close(); err != nil {
				errs = append(errs, fmt.Errorf("error closing subscriber: %w", err))
			}
		}
	}

	// 关闭 NoBacklogDetector
	if detectorAny := km.NoBacklogDetector.Load(); detectorAny != nil {
		if detector, ok := detectorAny.(*NoBacklogDetector); ok {
			if err := detector.Close(); err != nil {
				errs = append(errs, fmt.Errorf("error closing detector: %w", err))
			}
		}
	}

	// 清理处理器缓存
	km.aggregateProcessors.Purge()

	if len(errs) > 0 {
		return fmt.Errorf("multiple close errors: %v", errs)
	}
	return nil
}

func (km *KafkaSubscriberManager) SubscribeToTopic(topic string, handler func(msg *message.Message) error, timeout time.Duration) error {
	if topic == "" {
		return fmt.Errorf("topic cannot be empty")
	}

	subscriberAny := km.Subscriber.Load()

	if subscriberAny == nil {
		return fmt.Errorf("subscriber is nil")
	}

	// 类型断言以获取实际的 *kafka.Subscriber
	subscriber, ok := subscriberAny.(*kafka.Subscriber)
	if !ok {
		return fmt.Errorf("invalid subscriber type")
	}

	ctx, cancel := context.WithCancel(km.rootCtx)

	messages, err := subscriber.Subscribe(ctx, topic)
	if err != nil {
		cancel()
		return fmt.Errorf("failed to subscribe to topic %s: %w", topic, err)
	}
	// 为了 reconnect 时重新订阅的topic，在这里添加到 subscribedTopics 中
	km.subscribedTopics.Store(topic, handler)
	km.defaultTimeout.Store(int64(timeout))

	km.wg.Add(1)
	go func() { // 此协程会在整个订阅期间运行，每个topic的消息接收一个协程
		defer km.wg.Done()
		defer cancel()

		for {
			select {
			case msg, ok := <-messages:
				if !ok {
					log.Printf("Channel closed for topic: %s\n", topic)
					return
				}
				km.processMessage(ctx, msg, handler, timeout)
			case <-ctx.Done():
				log.Printf("Context cancelled for topic: %s\n", topic)
				return
			}
		}
	}()

	return nil
}

func (km *KafkaSubscriberManager) resubscribeAllTopics() error {
	var errs []error
	km.subscribedTopics.Range(func(key, value interface{}) bool {
		topic := key.(string)
		handler := value.(func(msg *message.Message) error)
		if err := km.SubscribeToTopic(topic, handler, time.Duration(km.defaultTimeout.Load())); err != nil {
			errs = append(errs, fmt.Errorf("failed to resubscribe to topic %s: %w", topic, err))
		}
		return true // 继续尝试所有主题
	})
	if len(errs) > 0 {
		return fmt.Errorf("multiple resubscribe errors: %v", errs)
	}
	return nil
}

func (km *KafkaSubscriberManager) processMessage(ctx context.Context, msg *message.Message, handler func(msg *message.Message) error, timeout time.Duration) {
	if err := km.processingRate.Wait(ctx); err != nil {
		log.Printf("Rate limiting error: %v", err)
		msg.Nack()
		return
	}

	aggregateID := msg.Metadata.Get("aggregateID")

	// 检查是否在恢复模式下或聚合ID是否在处理器中
	if km.IsInRecoveryMode() || (aggregateID != "" && km.aggregateProcessors.Contains(aggregateID)) {
		km.processMessageWithAggregateProcessor(ctx, msg, handler, timeout)
	} else {
		km.processMessageImmediately(ctx, msg, handler, timeout)
	}
}

// 消息被立即处理
func (km *KafkaSubscriberManager) processMessageImmediately(ctx context.Context, msg *message.Message, handler func(msg *message.Message) error, timeout time.Duration) {
	msgCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	done := make(chan error)
	go func() {
		defer close(done)
		err := handler(msg) //每个消息由单独的协程处理
		done <- err
	}()

	select {
	case err := <-done:
		if err == nil {
			msg.Ack()
		} else {
			log.Printf("Error processing message: %v", err)
			// 这里可以根据错误类型决定是 Ack 还是 Nack
			if km.isRetryableError(err) {
				// 对于可重试的错误使用 Nack，对于不可重试的错误使用 Ack
				msg.Nack()
			} else {
				// 对于不可重试的错误，可以考虑将消息发送到死信队列
				km.sendToDeadLetterQueue(msg)
				msg.Ack()
			}
		}
	case <-msgCtx.Done():
		log.Printf("Message processing timed out or cancelled for topic: %s", msg.Metadata.Get("topic"))
		msg.Nack()
	}
}

// 判断错误是否可重试
func (km *KafkaSubscriberManager) isRetryableError(err error) bool {
	// 实现逻辑来判断错误是否可重试
	// 例如，网络错误通常是可重试的，而解析错误可能不可重试
	// 这里需要根据你的具体业务逻辑来实现
	switch err.(type) {
	case *net.OpError, *os.SyscallError:
		return true
	case *json.SyntaxError, *json.UnmarshalTypeError:
		return false
	default:
		return false
	}
}

// 发送消息到死信队列
func (km *KafkaSubscriberManager) sendToDeadLetterQueue(msg *message.Message) {
	// 实现将消息发送到死信队列的逻辑
	// 这可能涉及将消息发布到另一个专门的 Kafka 主题
	log.Printf("Sending message to dead letter queue: %v", msg)
	//deadLetterTopic := fmt.Sprintf("%s-dead-letter", msg.Metadata.Get("topic"))
	//km.producer.Produce(deadLetterTopic, msg.Value)
}

// 消息进入聚合处理器处理
func (km *KafkaSubscriberManager) processMessageWithAggregateProcessor(ctx context.Context, msg *message.Message, handler func(msg *message.Message) error, timeout time.Duration) {
	aggregateID := msg.Metadata.Get("aggregateID")
	if aggregateID == "" {
		km.processMessageImmediately(ctx, msg, handler, timeout)
		return
	}

	backoffDuration := 1 * time.Second

	for attempt := 0; attempt < km.Config.MaxGetOrCreateProcessorAttempts; attempt++ {
		processor, err := km.getOrCreateProcessor(ctx, aggregateID, handler)
		if err == nil {
			select {
			case processor.messages <- msg:
				// Message sent to processor successfully
				return
			case <-time.After(timeout):
				log.Printf("Timeout while sending message to processor for aggregate ID %s", aggregateID)
				// Continue to next attempt or Nack if all attempts failed
			case <-ctx.Done():
				log.Printf("Context cancelled while sending message to processor for aggregate ID %s", aggregateID)
				msg.Nack()
				return
			}
		} else {
			log.Printf("Failed to get or create processor for aggregate ID %s: %v. Attempt %d of %d", aggregateID, err, attempt+1, km.Config.MaxGetOrCreateProcessorAttempts)
		}

		// Wait before next attempt
		select {
		case <-time.After(backoffDuration):
			// Continue to next attempt
		case <-ctx.Done():
			msg.Nack()
			return
		}

		// Increase backoff for next attempt
		backoffDuration *= 2
	}

	// If all attempts fail, log and Nack the message
	log.Printf("Failed to process message for aggregate ID %s after %d attempts", aggregateID, km.Config.MaxGetOrCreateProcessorAttempts)
	msg.Nack()
}

func (km *KafkaSubscriberManager) getOrCreateProcessor(ctx context.Context, aggregateID string, handler func(msg *message.Message) error) (*aggregateProcessor, error) {
	if proc, exists := km.aggregateProcessors.Get(aggregateID); exists {
		return proc, nil
	}

	// 只在恢复模式下创建新的处理器
	if !km.IsInRecoveryMode() {
		return nil, fmt.Errorf("not in recovery mode, cannot create new processor")
	}

	// 使用带超时的上下文
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// 创建新的处理器
	proc := &aggregateProcessor{
		aggregateID: aggregateID,
		messages:    make(chan *message.Message, 100),
		done:        make(chan struct{}),
	}
	proc.lastActivity.Store(time.Now())
	// 在创建新处理器时增加计数
	atomic.AddInt64(&totalProcessors, 1)
	atomic.AddInt64(&activeProcessors, 1)

	// 使用 select 语句来处理可能的超时或取消
	select {
	case <-ctxWithTimeout.Done():
		return nil, fmt.Errorf("context cancelled or timed out while creating processor: %w", ctxWithTimeout.Err())
	default:
		// 尝试添加到缓存
		if evicted := km.aggregateProcessors.Add(aggregateID, proc); evicted {
			// 如果有处理器被驱逐，我们需要停止它
			if evictedProc, ok := km.aggregateProcessors.Get(aggregateID); ok {
				close(evictedProc.done)
			}
		}

		// 启动处理器，处理器协程可能长时间运行，所以需要km.wg.Add(1)
		km.wg.Add(1)
		go km.runProcessor(ctx, proc, handler) // 注意这里也传入了上下文

		return proc, nil
	}
}

func (km *KafkaSubscriberManager) runProcessor(ctx context.Context, proc *aggregateProcessor, handler func(msg *message.Message) error) {
	defer km.wg.Done()
	atomic.AddInt64(&activeProcessors, 1)
	defer atomic.AddInt64(&activeProcessors, -1)
	defer km.releaseProcessor(proc)

	for {
		select {
		case msg, ok := <-proc.messages:
			if !ok { // channel 已经被关闭
				log.Printf("Channel closed for aggregateProcessor: %s\n", proc.aggregateID)
				return
			}
			// Process message
			if err := handler(msg); err != nil {
				// 这里可以根据错误类型决定是 Ack 还是 Nack
				if km.isRetryableError(err) {
					// 例如，对于可重试的错误使用 Nack，对于不可重试的错误使用 Ack
					msg.Nack()
				} else {
					// 对于不可重试的错误，可以考虑将消息发送到死信队列
					km.sendToDeadLetterQueue(msg)
					msg.Ack()
				}
			}
			proc.lastActivity.Store(time.Now())
		case <-time.After(km.Config.IdleTimeout):
			lastActivity := proc.lastActivity.Load().(time.Time)
			if time.Since(lastActivity) > km.Config.IdleTimeout {
				// 退出处理循环，从而触发defer km.releaseProcessor(proc)
				log.Printf("Processor for aggregate ID %s is idle, initiating shutdown", proc.aggregateID)
				return
			}
		case <-proc.done:
			return
		case <-ctx.Done():
			// 上下文被取消,退出处理器
			log.Printf("Context canceled for processor of aggregate ID %s, exiting", proc.aggregateID)
			return
		}
	}
}

func (km *KafkaSubscriberManager) releaseProcessor(proc *aggregateProcessor) {
	km.aggregateProcessors.Remove(proc.aggregateID)
	close(proc.messages)
	close(proc.done)
	// 减少处理器计数
	atomic.AddInt64(&totalProcessors, -1)
}

func (km *KafkaSubscriberManager) stopAllProcessors() {
	// 获取所有处理器的切片
	keys := km.aggregateProcessors.Keys()
	for _, key := range keys {
		if proc, ok := km.aggregateProcessors.Get(key); ok {
			close(proc.done) // 发送停止信号
		}
	}

	// 等待所有处理器完成
	deadline := time.Now().Add(30 * time.Second)
	for time.Now().Before(deadline) {
		if km.aggregateProcessors.Len() == 0 {
			return
		}
		time.Sleep(100 * time.Millisecond)
	}
	log.Println(pkg.Yellow("Warning: Not all processors stopped within the timeout period"))
}

func (km *KafkaSubscriberManager) startNoBacklogCheckLoop(ctx context.Context) {
	defer km.wg.Done()
	ticker := time.NewTicker(km.Config.NoBacklogCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if km.checkNoBacklog(ctx) {
				km.noBacklogCount++
				if km.noBacklogCount >= 3 {
					km.SetRecoveryMode(false)
					log.Println(pkg.Green("连续三次检测无积压，切换到正常消费者状态"))
					km.noBacklogCount = 0 // 重置计数
					return                // 退出循环，停止检查
				}
			} else {
				km.noBacklogCount = 0 // 重置计数
			}
		case <-ctx.Done():
			log.Println(pkg.Yellow("停止无积压检查循环"))
			return
		}
	}
}

// 检查积压消息是否处理完毕
func (km *KafkaSubscriberManager) checkNoBacklog(ctx context.Context) bool {
	// 1. 检查Kafka消费者组滞后
	noBacklogDetectorAny := km.NoBacklogDetector.Load()
	if noBacklogDetectorAny != nil {
		if subscnoBacklogDetector, ok := noBacklogDetectorAny.(*NoBacklogDetector); ok {
			noBacklog, err := subscnoBacklogDetector.IsNoBacklog(ctx)
			if err != nil {
				log.Printf(pkg.Red("Error checking backlog: %v\n"), err)
				return false
			}
			if noBacklog {
				log.Println(pkg.Green("No backlog detected. The system is up-to-date."))
			} else {
				log.Println(pkg.Yellow("Backlog detected. The system is still processing messages."))
				return false
			}
		}
	}

	// 2. 检查是否有大量活跃的处理器
	if km.hasHighActiveProcessors() {
		return false
	}

	// 如果所有检查都通过，认为积压消息已处理完毕
	return true
}

// 检查是否有大量活跃的处理器
func (km *KafkaSubscriberManager) hasHighActiveProcessors() bool {
	total := atomic.LoadInt64(&totalProcessors)
	active := atomic.LoadInt64(&activeProcessors)

	if total == 0 {
		return false
	}
	// 处理器数量由聚合id的数量决定
	// 如果活跃处理器占总处理器的比例低于某个阈值，就认为没有大量活跃处理器
	return float64(active)/float64(total) > km.Config.Threshold
}

// 使用示例（作为注释）：
/*
err := DefaultKafkaSubscriberManager.SubscribeToTopic("my-topic", func(msg *message.Message) {
    // 处理接收到的消息
    fmt.Printf("Received message: %s\n", string(msg.Payload))
}, 30*time.Second)
if err != nil {
    log.Printf("Failed to subscribe to topic: %v", err)
}
*/

/*

改进需求：
在我原有的代码基础上，确保不破坏原来代码的任何功能，实现：
（1）系统刚启动阶段处于恢复模式，由于要处理大量积压在kafka的消息，要确保同一个聚合id的所有消息（都属于同一个topic）都是严格按kafka发布顺序进行处理，即同一聚合ID的消息在同一个 goroutine 中按顺序处理。
（2）恢复模式下，聚合处理器采用固定大小资源池方式，当消息携带新的聚合id，则从资源池中分配一个空闲处理器;
（3）恢复模式下，当消息携带的聚合id已分配处理器，则消息送往该处理器处理；
（4）恢复模式下，当处理器资源池无资源可用时，新来的聚合id需要等待处理器资源池释放出空闲资源；
（5）恢复模式下，聚合id当前无消息需要处理时，处理器资源释放；
（6）恢复模式下，可以控制消息处理的速度，避免出现瞬间消息吞吐量过大，导致系统资源耗尽的情况；
（7）系统运行一段时间后，系统检测到积压的消息都已处理完，再切换为普通消费者模式，可以实现每个消息并行消费，提高性能
（8）从恢复模式到普通消费者模式的过度要平滑，在普通消费者模式下，如果某个聚合 ID 还有对应的处理器，消息仍会被送入该处理器进行顺序处理，当消息的聚合id查不到已占用聚合处理器，就立即处理；
（9）逐步的，聚合处理器资源池就全部空闲了，系统也就完全进入普通消费者模式了；
（10）考虑可靠性异常处理充分，特别注意func (km *KafkaSubscriberManager) processMessage的改动”
（11）尽量避免或少用锁，从而减少锁对性能的损耗
（12）请再看仔细检查，是否未破坏原有的代码，同时新增的代码稳定/可靠/优雅


*/

/*

当某个聚合的发布订阅出现性能瓶颈时，可以考虑以下解决方案：

1. 增加分区数：
    - 增加分区数可以提高并行度（提升Kafka自身并行度），从而提高消息处理能力。
    - 可以通过 kafka-configs.sh 脚本增加分区数。
    - 增加分区数后，需要重新创建 topic 并迁移数据。

2. 增加消费者组：
    - 增加消费者组可以提高并行度，从而提高消息处理能力。
    - 可以通过 kafka-configs.sh 脚本增加消费者组。
    - 增加消费者组后，需要重新创建消费者组并迁移数据。

3. 增加消费者数量：
    - 增加消费者数量可以提高并行度，从而提高消息处理能力。
    - 可以通过 kafka-configs.sh 脚本增加消费者数量。
    - 增加消费者数量后，需要重新创建消费者组并迁移数据。


三者的关系是：一个消费者组内有多个消费者实例，每个消费者实例消费不同分区；
可以考虑把查询压力大的聚合做成独立的查询微服务，从而降低单个聚合的查询压力。不用增加分区，就不用迁移数据。
如果还是不够，就多增加该聚合的微服务实例，每个实例消费一个分区，从而提升查询性能。涉及迁移数据，需要停机。

kafka知识

Kafka 的消息订阅和消费机制确实是按 topic 进行的，并且消息的顺序保证也是基于 topic 和分区（partition）的。
消费者订阅的是整个 topic 或 topic 的特定分区。
Kafka 只能保证同一 topic 的同一分区内的消息按发布顺序被消费。
每个分区内的消息都有一个唯一的递增序号（offset），确保了分区内的顺序。
如果一个 topic 有多个分区，不同分区之间的消息顺序不能保证。
消费者可能会并行处理来自不同分区的消息，这可能导致跨分区的消息处理顺序与发布顺序不一致。
如果你希望确保特定聚合 ID 的消息按顺序处理，你需要确保同一聚合 ID 的所有消息都发送到同一个分区。
这通常通过使用聚合 ID 作为分区键（partition key）来实现。
在使用消费者组时，每个分区只会被组内的一个消费者消费，这有助于维护分区内的顺序。
但是，如果消费者数量少于分区数量，一个消费者可能会消费多个分区，这时需要特别注意跨分区的消息顺序。

Kafka 的默认分区器使用消息的键来决定消息应该发送到哪个分区。
如果你使用聚合 ID 作为消息的键，默认分区器会自动将相同聚合 ID 的消息发送到同一个分区。

使用聚合 ID 作为消息键不会直接导致出现大量的分区。分区的数量是在创建 Kafka topic 时由你指定的，
并且是固定的。使用聚合 ID 作为消息键的主要作用是确保具有相同聚合 ID 的消息被路由到同一个分区，
以保证消息的顺序性。

创建 Kafka topic 通常是通过 Kafka 自身的工具或 Kafka 客户端库来完成的。
在 Kafka 中，topic 是消息发布和订阅的基本单位。通常情况下，Kafka 需要在发布消息之前预先创建 topic。
然而，Kafka 也支持自动创建 topic 的功能，但这需要在 Kafka broker 的配置中启用。
Kafka broker 的默认配置确实是允许在首次尝试发布消息到不存在的 topic 时自动创建该 topic。
auto.create.topics.enable：默认值：true
自动创建的 topic 会使用 Kafka broker 的默认分区数（通常是1）和副本因子（通常是1）。
这些默认值可以通过 num.partitions 和 default.replication.factor 配置项来设置。
生产环境建议：
    在生产环境中，通常建议显式创建 topic，以便更好地控制分区和副本配置。
	自动创建的 topic 可能不符合你的性能和可靠性需求。
*/
