package tests

import (
	"fmt"
	"math/rand"
	"net/http"
	"testing"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	commandTesthelpers "jxt-evidence-system/evidence-management/command/testhelpers"
	queryTesthelpers "jxt-evidence-system/evidence-management/query/testhelpers"
	testhelpers "jxt-evidence-system/evidence-management/tests/testhelpers"
)

func TestEnforcementTypeApi(t *testing.T) {
	RegisterFailHandler(Fail)
	//RunSpecs(t, "EnforcementTypeApi Suite")
}

var _ = Describe("EnforcementTypeApi", func() {
	BeforeEach(func() {
		// 清空数据库表
		dbCommand.Exec("DELETE FROM t_evidence_enforcement_types;")
		dbQuery.Exec("DELETE FROM t_evidence_enforcement_types_read;")
	})

	Describe("Create", func() {
		It("成功创建一个新的执法类型", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建一个执法类型记录
			createCommand := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
				WithCode(fmt.Sprintf("CODE001forCreate%06d", randNum)).
				WithName(fmt.Sprintf("执法类型%06d", randNum)).
				WithDesc(fmt.Sprintf("测试执法类型%06d", randNum)).
				Build()

			// 发送创建请求
			resp, err := testhelpers.SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", createCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))

			// 读取并验证响应
			response := testhelpers.ParseResponseToMap(resp)
			Expect(response["code"]).To(Equal(float64(http.StatusOK)))
			Expect(response["msg"]).To(Equal("创建执法类型成功"))

			// 验证命令数据库中是否创建了新记录
			var count int64
			enforcementType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.Model(enforcementType).Where("enforcement_type_code = ?", createCommand.EnforcementTypeCode).Count(&count)
			Expect(count).To(Equal(int64(1)))

			// 这里延迟200毫秒，等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证查询数据库中是否创建了新记录
			enforcementTypeRead := queryTesthelpers.NewEnforcementTypeReadModelBuilder().Build()
			dbQuery.Model(enforcementTypeRead).Where("enforcement_type_code = ?", createCommand.EnforcementTypeCode).Count(&count)
			Expect(count).To(Equal(int64(1)))

			// 通过 API 查询刚创建的执法类型
			queryParams := map[string]string{
				"enforcementTypeCode": createCommand.EnforcementTypeCode,
			}
			queryResp, err := testhelpers.SendRequestWithAuthAndQuery(baseURL, "GET", "/api/v1/enforcement-types", nil, queryParams, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(queryResp.StatusCode).To(Equal(http.StatusOK))

			// 读取并验证查询响应
			queryResponse := testhelpers.ParseResponseToMap(queryResp)
			Expect(queryResponse["code"]).To(Equal(float64(http.StatusOK)))
			Expect(queryResponse["msg"]).To(Equal("查询成功"))

			// 验证返回的执法类型数据
			data, ok := queryResponse["data"].(map[string]interface{})
			Expect(ok).To(BeTrue())
			list, ok := data["list"].([]interface{})
			Expect(ok).To(BeTrue())
			Expect(len(list)).To(Equal(1))

			item := list[0].(map[string]interface{})
			Expect(item["enforceTypeCode"]).To(Equal(createCommand.EnforcementTypeCode))
			Expect(item["enforceTypeName"]).To(Equal(createCommand.EnforcementTypeName))
			Expect(item["enforceTypeDesc"]).To(Equal(createCommand.EnforcementTypeDesc))
		})

		It("当尝试创建编码重复的执法类型时应该返回错误", func() {

			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)
			// 创建第一个执法类型
			createCommand := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
				WithCode(fmt.Sprintf("CODE999%06d", randNum)).
				WithName("执法类型A").
				Build()

			// 发送请求创建第一个执法类型
			resp, err := testhelpers.SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", createCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))
			resp.Body.Close()

			// 尝试创建编码重复的执法类型（名称不同）
			duplicateCommand := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
				WithCode(fmt.Sprintf("CODE999%06d", randNum)). // 相同编码
				WithName(fmt.Sprintf("执法类型B%06d", randNum)).   // 不同名称
				Build()

			// 发送请求创建重复编码的执法类型
			resp, err = testhelpers.SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", duplicateCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))

			// 验证错误消息
			result := testhelpers.ParseResponseToMap(resp)
			Expect(result["code"]).To(Equal(float64(http.StatusInternalServerError)))
			Expect(result["msg"]).To(And(
				ContainSubstring("创建执法类型失败"),
				ContainSubstring("执法类型编码已存在"),
			))
		})
	})

	Describe("Update", func() {
		It("成功更新一个已存在的执法类型", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 先创建一个新的执法类型
			createCommand := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
				WithCode(fmt.Sprintf("CODE001forUpdate%06d", randNum)).
				WithName(fmt.Sprintf("待更新执法类型%06d", randNum)).
				WithDesc(fmt.Sprintf("原始描述%06d", randNum)).
				Build()

			// 发送创建请求
			insertResp, err := testhelpers.SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", createCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(insertResp.StatusCode).To(Equal(http.StatusOK))

			// 获取创建的执法类型ID
			enforcementType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.Where("enforcement_type_name = ?", createCommand.EnforcementTypeName).First(&enforcementType)

			// 更新执法类型
			updateCommand := commandTesthelpers.NewUpdateEnforcementTypeCommandBuilder().
				WithID(enforcementType.ID).
				WithCode(enforcementType.EnforcementTypeCode).
				WithName(fmt.Sprintf("更新后的执法类型%06d", randNum)).
				WithDesc(fmt.Sprintf("更新后的描述%06d", randNum)).
				Build()

			// 发送更新请求
			resp, err := testhelpers.SendRequestWithAuth(baseURL, "PUT", fmt.Sprintf("/api/v1/enforcement-types/%d", enforcementType.ID), updateCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))

			// 读取并验证响应
			response := testhelpers.ParseResponseToMap(resp)
			Expect(response["code"]).To(Equal(float64(http.StatusOK)))
			Expect(response["msg"]).To(Equal("更新执法类型成功"))

			// 验证数据库中的记录已被更新
			updatedType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.First(&updatedType, enforcementType.ID)
			Expect(updatedType.EnforcementTypeName).To(Equal(*updateCommand.EnforcementTypeName))
			Expect(updatedType.EnforcementTypeDesc).To(Equal(*updateCommand.EnforcementTypeDesc))

			// 这里延迟200毫秒，等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证查询数据库中的记录已被更新
			updatedTypeRead := queryTesthelpers.NewEnforcementTypeReadModelBuilder().Build()
			dbQuery.First(&updatedTypeRead, enforcementType.ID)
			Expect(updatedTypeRead.EnforcementTypeName).To(Equal(*updateCommand.EnforcementTypeName))
			Expect(updatedTypeRead.EnforcementTypeDesc).To(Equal(*updateCommand.EnforcementTypeDesc))
		})

		It("更新父Id成功，也刷新自己以及所有子孙后代执法类型的相应路径", func() {

			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 先创建父执法类型
			parentCommand := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
				WithCode(fmt.Sprintf("PARENT_CODE%06d", randNum)).
				WithName(fmt.Sprintf("父执法类型%06d", randNum)).
				WithDesc(fmt.Sprintf("父执法类型描述%06d", randNum)).
				WithParentId(0).
				Build()

			resp, err := testhelpers.SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", parentCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))

			// 获取父执法类型ID
			parentType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.Where("enforcement_type_code = ?", parentCommand.EnforcementTypeCode).First(&parentType)

			// 创建子执法类型
			childCommand := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
				WithCode(fmt.Sprintf("%s_CHILD_CODE%06d", parentCommand.EnforcementTypeCode, randNum)).
				WithName(fmt.Sprintf("%s_子执法类型%06d", parentCommand.EnforcementTypeName, randNum)).
				WithDesc(fmt.Sprintf("%s_子执法类型描述%06d", parentCommand.EnforcementTypeDesc, randNum)).
				WithParentId(parentType.ID).
				Build()

			resp, err = testhelpers.SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", childCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))

			// 获取子执法类型ID
			childType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.Where("enforcement_type_code = ?", childCommand.EnforcementTypeCode).First(&childType)

			// 为子执法类型创建10个孙执法类型
			grandChildCodes := testhelpers.AddChildEnforcementTypes(randNum, childCommand.EnforcementTypeCode, 10, baseURL, token, dbCommand)

			// 验证所有孙执法类型的路径
			for _, grandChildCode := range grandChildCodes {
				grandChildType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
				dbCommand.Where("enforcement_type_code = ?", grandChildCode).First(&grandChildType)

				// 验证每个孙执法类型的父ID和路径
				Expect(grandChildType.ParentId).To(Equal(childType.ID))
				Expect(grandChildType.EnforcementTypePath).To(Equal(fmt.Sprintf("/%s/%s/%s",
					parentCommand.EnforcementTypeCode,
					childCommand.EnforcementTypeCode,
					grandChildCode)))
			}

			// 更新子执法类型，设置父ID为0
			updateCommand := commandTesthelpers.NewUpdateEnforcementTypeCommandBuilder().
				WithID(childType.ID).
				WithCode(childType.EnforcementTypeCode).
				WithParentId(0).
				Build()

			// 发送更新请求
			resp, err = testhelpers.SendRequestWithAuth(baseURL, "PUT", fmt.Sprintf("/api/v1/enforcement-types/%d", childType.ID), updateCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))

			// 读取并验证响应
			response := testhelpers.ParseResponseToMap(resp)
			Expect(response["code"]).To(Equal(float64(http.StatusOK)))
			Expect(response["msg"]).To(Equal("更新执法类型成功"))

			// 验证数据库中的子执法类型记录已被更新
			updatedType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.First(&updatedType, childType.ID)
			Expect(updatedType.ParentId).To(Equal(int64(0)))
			Expect(updatedType.EnforcementTypePath).To(Equal(fmt.Sprintf("/%s", childCommand.EnforcementTypeCode)))

			// 验证所有孙执法类型的路径同步更新
			for _, grandChildCode := range grandChildCodes {
				grandChildType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
				dbCommand.Where("enforcement_type_code = ?", grandChildCode).First(&grandChildType)
				Expect(grandChildType.ParentId).To(Equal(childType.ID))
				Expect(grandChildType.EnforcementTypePath).To(Equal(fmt.Sprintf("/%s/%s",
					childCommand.EnforcementTypeCode,
					grandChildCode)))
			}

			/* 查询数据库不检查

			// 这里延迟200毫秒，等待领域事件处理
			time.Sleep(200 * time.Millisecond)
			// 验证查询数据库中的子执法类型记录已被更新
			updatedTypeRead := queryTesthelpers.NewEnforcementTypeReadModelBuilder().Build()
			dbQuery.First(&updatedTypeRead, childType.ID)
			Expect(updatedTypeRead.ParentId).To(Equal(int64(0)))
			Expect(updatedTypeRead.EnforcementTypePath).To(Equal(fmt.Sprintf("/%s", childCommand.EnforcementTypeCode)))

			// 验证查询数据库中的所有孙执法类型路径都已更新
			for _, grandChildCode := range grandChildCodes {
				grandChildTypeRead := queryTesthelpers.NewEnforcementTypeReadModelBuilder().Build()
				dbQuery.Where("enforcement_type_code = ?", grandChildCode).First(&grandChildTypeRead)
				Expect(grandChildTypeRead.ParentId).To(Equal(childType.ID))
				Expect(grandChildTypeRead.EnforcementTypePath).To(Equal(fmt.Sprintf("/%s/%s",
					childCommand.EnforcementTypeCode,
					grandChildCode)))
			}
			*/
		})

		It("当尝试更新不存在的执法类型时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)
			updateCommand := commandTesthelpers.NewUpdateEnforcementTypeCommandBuilder().
				WithID(999999).                                // 不存在的ID
				WithCode(fmt.Sprintf("CODE999%06d", randNum)). // 添加必填的编码字段
				WithName(fmt.Sprintf("不存在的执法类型%06d", randNum)).
				WithDesc(fmt.Sprintf("这个执法类型不存在%06d", randNum)).
				Build()

			// 发送更新请求
			resp, err := testhelpers.SendRequestWithAuth(baseURL, "PUT", "/api/v1/enforcement-types/999999", updateCommand, token)
			Expect(err).NotTo(HaveOccurred())
			// 不检查HTTP状态码，因为API总是返回200
			// Expect(resp.StatusCode).To(Equal(http.StatusNotFound))

			// 读取并验证响应
			response := testhelpers.ParseResponseToMap(resp)
			Expect(response["code"]).To(Equal(float64(http.StatusNotFound)))
			Expect(response["msg"]).To(And(
				ContainSubstring("更新执法类型失败"),
				ContainSubstring("执法类型不存在"),
			))
		})

		It("当尝试更新执法类型编码时应该返回错误", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)
			// 先创建一个执法类型
			createCommand := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
				WithCode(fmt.Sprintf("CODE001forUpdateButNotChange%06d", randNum)).
				WithName(fmt.Sprintf("测试执法类型%06d", randNum)).
				Build()

			resp, err := testhelpers.SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", createCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))
			resp.Body.Close()

			// 获取创建的执法类型ID
			enforcementType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.Where("enforcement_type_code = ?", createCommand.EnforcementTypeCode).First(&enforcementType)

			// 构建更新命令，尝试更新编码
			updateCommand := commandTesthelpers.NewUpdateEnforcementTypeCommandBuilder().
				WithID(enforcementType.ID).
				WithCode(fmt.Sprintf("CHANGED_CODE%06d", randNum)). // 尝试更改为新的编码值
				WithName(fmt.Sprintf("更新后的执法类型%06d", randNum)).
				WithDesc(fmt.Sprintf("更新后的描述%06d", randNum)).
				Build()

			// 发送更新请求
			resp, err = testhelpers.SendRequestWithAuth(baseURL, "PUT", fmt.Sprintf("/api/v1/enforcement-types/%d", enforcementType.ID), updateCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))

			// 读取并验证响应
			response := testhelpers.ParseResponseToMap(resp)
			Expect(response["code"]).To(Equal(float64(http.StatusBadRequest))) // 应该是BadRequest(400)而不是InternalServerError(500)
			Expect(response["msg"]).To(And(
				ContainSubstring("更新执法类型失败"),
				ContainSubstring("执法类型编码不可更改"),
			))

			// 验证数据库中的记录未被更新
			updatedType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.First(&updatedType, enforcementType.ID)
			Expect(updatedType.EnforcementTypeCode).To(Equal(createCommand.EnforcementTypeCode))
		})

		It("当尝试更新父节点id为当前节点时，返回失败", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建一个执法类型
			createCommand := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
				WithCode(fmt.Sprintf("CODE001forUpdateParent%06d", randNum)).
				WithName(fmt.Sprintf("测试执法类型%06d", randNum)).
				Build()

			resp, err := testhelpers.SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", createCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))
			resp.Body.Close()

			// 获取创建的执法类型ID
			enforcementType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.Where("enforcement_type_code = ?", createCommand.EnforcementTypeCode).First(&enforcementType)

			// 构建更新命令，尝试将父节点设置为当前节点
			updateCommand := commandTesthelpers.NewUpdateEnforcementTypeCommandBuilder().
				WithID(enforcementType.ID).
				WithCode(enforcementType.EnforcementTypeCode). // 添加必需的编码字段
				WithParentId(enforcementType.ID).              // 尝试将父节点设置为当前节点
				Build()

			// 发送更新请求
			resp, err = testhelpers.SendRequestWithAuth(baseURL, "PUT", fmt.Sprintf("/api/v1/enforcement-types/%d", enforcementType.ID), updateCommand, token)
			Expect(err).NotTo(HaveOccurred())
			// 不检查HTTP状态码，API总是返回200
			// Expect(resp.StatusCode).To(Equal(http.StatusOK))

			// 读取并验证响应
			response := testhelpers.ParseResponseToMap(resp)
			Expect(response["code"]).To(Equal(float64(http.StatusBadRequest))) // 应该是400而不是500
			Expect(response["msg"]).To(And(
				ContainSubstring("更新执法类型失败"),
				ContainSubstring("父节点ID不能设置为当前节点的ID"),
			))

			// 验证数据库中的记录未被更新
			updatedType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.First(&updatedType, enforcementType.ID)
			Expect(updatedType.ParentId).To(Equal(int64(0))) // 验证父节点ID仍然为0
		})

		It("当尝试更新父节点id为当前节点后代的id时，返回失败", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建父执法类型
			parentCommand := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
				WithCode(fmt.Sprintf("父执法类型CODE%06d", randNum)).
				WithName(fmt.Sprintf("父执法类型NAME%06d", randNum)).
				Build()

			resp, err := testhelpers.SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", parentCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))
			resp.Body.Close()

			// 获取父执法类型ID
			parentType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.Where("enforcement_type_code = ?", parentCommand.EnforcementTypeCode).First(&parentType)

			// 创建子执法类型
			childCommand := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
				WithCode(fmt.Sprintf("子执法类型CODE%06d", randNum)).
				WithName(fmt.Sprintf("子执法类型NAME%06d", randNum)).
				WithParentId(parentType.ID).
				Build()

			resp, err = testhelpers.SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", childCommand, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))
			resp.Body.Close()

			// 获取子执法类型ID
			childType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.Where("enforcement_type_code = ?", childCommand.EnforcementTypeCode).First(&childType)

			// 尝试将父执法类型的父节点设置为子执法类型
			updateCommand := commandTesthelpers.NewUpdateEnforcementTypeCommandBuilder().
				WithID(parentType.ID).
				WithCode(parentType.EnforcementTypeCode). // 添加必需的编码字段
				WithParentId(childType.ID).
				Build()

			// 发送更新请求
			resp, err = testhelpers.SendRequestWithAuth(baseURL, "PUT", fmt.Sprintf("/api/v1/enforcement-types/%d", parentType.ID), updateCommand, token)
			Expect(err).NotTo(HaveOccurred())
			// 不检查HTTP状态码，API总是返回200
			// Expect(resp.StatusCode).To(Equal(http.StatusOK))

			// 读取并验证响应
			response := testhelpers.ParseResponseToMap(resp)
			Expect(response["code"]).To(Equal(float64(http.StatusBadRequest))) // 应该是400而不是500
			Expect(response["msg"]).To(ContainSubstring("父节点不能设置为当前节点的后代"))

			// 验证数据库中的记录未被更新
			updatedType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.First(&updatedType, parentType.ID)
			Expect(updatedType.ParentId).To(Equal(int64(0))) // 父节点应该保持为0
		})
	})

	Describe("Delete", func() {
		It("成功删除一个已存在的执法类型", func() {
			// 随机生成一个6位数字，避免与其它用例冲突
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)
			// 先创建一个新的执法类型
			type1 := commandTesthelpers.NewCreateEnforcementTypeCommandBuilder().
				WithCode(fmt.Sprintf("待删除执法类型CODE%06d", randNum)).
				WithName(fmt.Sprintf("待删除执法类型NAME%06d", randNum)).
				Build()

			// 创建第一个执法类型
			resp, err := testhelpers.SendRequestWithAuth(baseURL, "POST", "/api/v1/enforcement-types", type1, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))
			resp.Body.Close()

			// 获取创建的执法类型ID
			enforcementType := commandTesthelpers.NewEnforcementTypeBuilder().Build()
			dbCommand.Where("enforcement_type_name = ?", type1.EnforcementTypeName).First(&enforcementType)

			id := enforcementType.ID

			// 发送删除执法类型请求
			resp, err = testhelpers.SendRequestWithAuth(baseURL, "DELETE", fmt.Sprintf("/api/v1/enforcement-types/%d", id), nil, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))

			// 读取并验证响应
			response := testhelpers.ParseResponseToMap(resp)
			Expect(response["code"]).To(Equal(float64(http.StatusOK)))
			Expect(response["msg"]).To(Equal("删除执法类型成功"))

			// 验证数据库中的记录已被删除
			var count int64
			dbCommand.Model(commandTesthelpers.NewEnforcementTypeBuilder().Build()).Where("enforcement_type_id = ?", id).Count(&count)
			Expect(count).To(Equal(int64(0)))

			// 这里延迟200毫秒，等待领域事件处理
			time.Sleep(200 * time.Millisecond)

			// 验证查询数据库中的记录已被删除
			typeRead := queryTesthelpers.NewEnforcementTypeReadModelBuilder().Build()
			dbQuery.Model(typeRead).Where("enforcement_type_id = ?", id).Count(&count)
			Expect(count).To(Equal(int64(0)))
		})

		It("当尝试删除不存在的执法类型时应该返回错误", func() {

			// 发送删除请求，删除一个不存在的ID
			resp, err := testhelpers.SendRequestWithAuth(baseURL, "DELETE", fmt.Sprintf("/api/v1/enforcement-types/%d", 1000000), nil, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(resp.StatusCode).To(Equal(http.StatusOK))

			// 读取并验证响应
			response := testhelpers.ParseResponseToMap(resp)
			Expect(response["code"]).To(Equal(float64(http.StatusInternalServerError)))
			Expect(response["msg"]).To(And(
				ContainSubstring("删除执法类型失败"),
				ContainSubstring("要删除的执法类型不存在"),
			))
		})
	})

	Describe("执法类型查询", func() {
		It("成功创建一片执法类型森林，并树形查询成功", func() {

			//为了避免和其它用例的编号冲突，这里随机生成一个编号
			rand.Seed(time.Now().UnixNano())
			randNum := 100000 + rand.Intn(900000)

			// 创建一个执法类型记录
			var maxRootCount = 5
			var maxChildCount = 10

			// 创建根执法类型
			rootTypeCodes := testhelpers.AddRootEnforcementTypes(randNum, maxRootCount, baseURL, token, dbCommand)

			// 为每个根执法类型创建子执法类型
			for _, rootTypeCode := range rootTypeCodes {
				testhelpers.AddChildEnforcementTypes(randNum, rootTypeCode, maxChildCount, baseURL, token, dbCommand)
			}

			// 这里延迟200毫秒，再进行查询
			time.Sleep(200 * time.Millisecond)

			// 通过 API 查询刚创建的执法类型森林
			queryResp, err := testhelpers.SendRequestWithAuthAndQuery(baseURL, "GET", "/api/v1/enforcement-types/tree", nil, nil, token)
			Expect(err).NotTo(HaveOccurred())
			Expect(queryResp.StatusCode).To(Equal(http.StatusOK))

			// 读取并验证查询响应
			queryResponse := testhelpers.ParseResponseToMap(queryResp)
			Expect(queryResponse["code"]).To(Equal(float64(http.StatusOK)))
			Expect(queryResponse["msg"]).To(Equal("获取执法类型树形结构成功"))

			// 验证返回的执法类型数据
			data, ok := queryResponse["data"].([]interface{})

			Expect(ok).To(BeTrue())

			//fmt.Printf("执法类型树形结构数据: %+v", data)

			Expect(len(data)).To(Equal(maxRootCount)) // 验证根节点数量

			// 遍历每个根节点及其子节点
			for i, rootNode := range data {
				rootMap, ok := rootNode.(map[string]interface{})
				Expect(ok).To(BeTrue())

				// 验证根节点数据
				Expect(rootMap["enforceTypeCode"]).To(Equal(fmt.Sprintf("ROOT_CODE%06d", randNum+i)))
				Expect(rootMap["enforceTypeName"]).To(Equal(fmt.Sprintf("根执法类型%06d", randNum+i)))
				Expect(rootMap["enforceTypeDesc"]).To(Equal(fmt.Sprintf("根测试执法类型%06d", randNum+i)))
				Expect(rootMap["parentId"]).To(Equal(float64(0)))
				Expect(rootMap["source"]).To(Equal("test"))
				Expect(rootMap["sort"]).To(Equal(float64(1)))

				// 验证子节点
				children, ok := rootMap["children"].([]interface{})
				Expect(ok).To(BeTrue())
				Expect(len(children)).To(Equal(maxChildCount)) // 验证每个根节点子节点数量

				// 遍历子节点
				for j, childNode := range children {
					childMap, ok := childNode.(map[string]interface{})
					Expect(ok).To(BeTrue())

					// 验证子节点数据
					Expect(childMap["enforceTypeCode"]).To(Equal(fmt.Sprintf("ROOT_CODE%06d_CHILD_CODE%06d", randNum+i, randNum+j)))
					Expect(childMap["enforceTypeName"]).To(Equal(fmt.Sprintf("根执法类型%06d_子执法类型%06d", randNum+i, randNum+j)))
					Expect(childMap["enforceTypeDesc"]).To(Equal(fmt.Sprintf("根测试执法类型%06d_子测试执法类型%06d", randNum+i, randNum+j)))
					Expect(childMap["parentId"]).To(Equal(rootMap["id"])) // 验证父节点ID
					Expect(childMap["source"]).To(Equal("test"))
					Expect(childMap["sort"]).To(Equal(float64(1)))
				}
			}
		})

		It("应该正确处理空树的情况", func() {
			// 测试没有数据时的树形结构查询
		})

		It("应该正确处理循环引用的情况", func() {
			// 测试父子关系出现循环引用的情况
		})
	})
})
