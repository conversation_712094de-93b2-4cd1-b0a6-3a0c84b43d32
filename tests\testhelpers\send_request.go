package testhelpers

import (
	"bytes"
	"encoding/json"
	"net/http"
)

// SendRequest 发送HTTP请求并返回响应
func SendRequest(baseURL, method, path string, body interface{}) (*http.Response, error) {
	// 将请求体序列化为JSON
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, baseURL+path, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 创建HTTP客户端
	client := &http.Client{}

	// 发送请求
	return client.Do(req)
}
